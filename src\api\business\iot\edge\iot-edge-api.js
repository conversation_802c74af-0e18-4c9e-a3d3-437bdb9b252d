/*
  * 边缘API
  *
  * @Author:    文伊仪
  * @Date:      2025-03-27 21:21:21
  * @Copyright  2025 电子科技大学中山学院大数据与智能计算实验室
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const iotGatewayApi = {

  /**
   * 自定义分页查询  
   */
  queryPage : (param) => {
    return postRequest('/gateway/page', param);
  },
  /**
   * 分页  
   */
  list : (param) => {
    return postRequest('/gateway/list', param);
  },
  /**
   * 新增 
   */
  save: (param) => {
      return postRequest('/gateway/save', param);
  },
  /**
   * 新增或修改 
   */
  submit: (param) => {
      return postRequest('/gateway/submit', param);
  },
  /**
   * 修改 
   */
  update: (param) => {
      return postRequest('/gateway/update', param);
  },
  /**
   * 逻辑删除 
   */
  remove: (param) => {
      return postRequest(`/gateway/remove`,param);
  },
  /**
   * 排序 
   */
  sort: (param) => {
      return postRequest('/gateway/sort', param);
  },
  /**
   * 禁用 
   */
  disabled: (param) => {
      return postRequest('/gateway/disabled', param);
  },
    /**
   * 详情
   */
  detail: (param) => {
      return postRequest('/gateway/detail', param);
  },


};
