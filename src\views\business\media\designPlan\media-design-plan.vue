<template>
    <!-- 视频预览区域 -->
    <div ref="cvsWrapEl"></div>
    <!-- 工具栏 -->
    <a-row  style="padding: 16px;background-color: black;border: 1px solid bisque;">
        <a-col :span="16">
            <a-button type="primary" @click="selectMaterial" class="smart-margin-left10">添加素材</a-button>
            <a-button @click="showTextForm" class="smart-margin-left10">添加文字</a-button>
            <a-button type="primary" @click="exportVideo" class="smart-margin-left10">导出视频</a-button>
            <a-button danger @click="deleteSelectedClip" class="smart-margin-left10">删除片段</a-button>
            <a-button @click="splitSelectedClip" class="smart-margin-left10">分割</a-button>
        </a-col>
        <a-col :span="8" style="text-align: right">
            <a-button @click="handlePlayPause" class="smart-margin-right20">{{ playing ? '暂停' : '播放' }}</a-button>
            <span style="color: white;">缩放：{{ pixelsPerSecond }}</span>
            <a-button @click="() => pixelsPerSecond += 25" class="smart-margin-left10">+</a-button>
            <a-button @click="() => pixelsPerSecond -= 25" class="smart-margin-left10">-</a-button>
        </a-col>
    </a-row>

    <!-- 视频时间轴 -->
    <video-timeline ref="timelineRef" :duration="videoDuration" :current-time="currentTime"
        :pixelsPerSecond="pixelsPerSecond" @time-update="handleTimeUpdate" @clip-select="handleClipSelect"
        @clip-move="handleClipMove" @clip-resize="handleClipResize" />

    <!-- 模态框 -->
    <text-form-modal ref="textFormRef" @submit="addMedia" />
    <media-material-modal ref="mediaMaterialModalRef" @select="addMedia" />
    <export-media-material-form ref="exportMediaMaterialForm"/>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { AVCanvas } from '@webav/av-canvas';
import { ImgClip, MP4Clip, VisibleSprite, renderTxt2ImgBitmap } from '@webav/av-cliper';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { message } from 'ant-design-vue';
import TextFormModal from './components/text-form-modal.vue';
import VideoTimeline from './components/video-time-line.vue';
import MediaMaterialModal from './components/media-material-modal.vue';
import ExportMediaMaterialForm from './components/export-media-material-form.vue';
import { fileApi } from '/@/api/support/file-api';

// 基础状态
let avCvs = null; // 画布
let clips = []; // 片段列表
const cvsWrapEl = ref(null); // 画布元素
const playing = ref(false); // 播放状态
const currentTime = ref(0); // 当前时间
const pixelsPerSecond = ref(25); // 缩放比例
const timelineRef = ref(null); // 时间轴引用
const videoDuration = ref(100); // 视频总时长

// 初始化画布
function initCanvas() {
    if (!cvsWrapEl.value || avCvs) return;
    try {
        avCvs = new AVCanvas(cvsWrapEl.value, { width: 1280, height: 600 });
        avCvs.on('timeupdate', time => currentTime.value = time / 1e6);
        avCvs.on('playing', () => playing.value = true);
        avCvs.on('paused', () => playing.value = false);
    } catch (error) {
        console.error('Canvas初始化错误:', error);
        message.error('初始化视频播放器失败');
    }
}

// 文字模态框
const textFormRef = ref(null);
function showTextForm() {
    textFormRef.value.showForm();
}
//素材选择模态框
const mediaMaterialModalRef = ref(null);
function selectMaterial() {
    mediaMaterialModalRef.value.showForm();
}

const mainSprite = ref(null); // 主视频精灵
// 素材添加处理
async function addMedia(item) {
    if (!avCvs) await initCanvas();
    if (!avCvs) return;
    try {
        // 计算起始时间
        const startTime = Math.max(...clips
            .filter(clip => clip.type === item.type)
            .map(clip => clip.startTime + clip.duration)
            .concat([currentTime.value || 0])
        );
        
        // 创建媒体片段
        let clip, duration = 5, trackIndex; // 默认5秒
        
        if (item.type === 'TEXT') {
            // 文本转图像
            const bitmap = await renderTxt2ImgBitmap(
                item.content,
                `font-size: ${item.fontSize}px; color: ${item.color};`
            );
            console.log(bitmap);
            clip = new ImgClip(bitmap);
            trackIndex = 2;
        } else {
            // 加载媒体文件
            const stream = await fetch(item.fileKey[0].fileUrl);
            
            if (item.type === 'VIDEO') {
                clip = new MP4Clip(stream.body);
                await clip.ready;
                duration = clip.meta.duration / 1e6 || 10;
                trackIndex = 0;
            } else {
                clip = new ImgClip(stream.body);
                trackIndex = 1;
            }
        }
        
        // 创建并配置精灵
        const sprite = new VisibleSprite(clip);
        sprite.time = { 
            offset: startTime * 1e6, 
            duration: duration * 1e6 
        };
        await avCvs.addSprite(sprite);
        
        // 设置主精灵（仅第一个视频）
        if (item.type === 'VIDEO' && !clips.some(c => c.type === 'VIDEO')) {
            mainSprite.value = sprite;
        }
        
        // 添加到时间轴
        const clipInfo = {
            id: Date.now().toString(),
            name: item.type === 'TEXT' ? item.content : item.name,
            type: item.type,
            sprite,
            startTime,
            duration,
            originalDuration: item.type === 'VIDEO' ? duration : null // 记录视频的原始时长,防止视频在resize时超出视频本身长度
        };
        
        timelineRef.value.addClip(trackIndex, clipInfo);
        clips.push(clipInfo);
        
        // 更新状态
        currentTime.value = startTime;
        updateVideoDuration(startTime + duration);
        message.success('添加成功');
    } catch (error) {
        console.error('添加失败:', error);
        message.error('添加失败');
    }
}

// 辅助函数：更新视频总时长
function updateVideoDuration(endTime) {
    if (endTime === videoDuration.value)  {
        videoDuration.value = endTime+100;
    }
}

// 时间轴事件处理
function handleTimeUpdate(time) {
    currentTime.value = time;
    avCvs.pause();
    if (avCvs) {
        avCvs.previewFrame(time * 1e6);
    }
}

const selectedClip = ref(null); // 当前选中片段

function handleClipSelect(clip) {
    selectedClip.value = clip;
    message.success(`选中片段: ${clip.name}`);
}

function handleClipMove(clip) {
    const clipInfo = clips.find(c => c.id === clip.id);
    if (clipInfo?.sprite) {
        try {
            clipInfo.sprite.time.offset = clip.startTime * 1e6;
            clipInfo.startTime = clip.startTime;
        } catch (e) {
            console.warn('更新sprite时间偏移失败', e);
        }
    }
}

function handleClipResize(clip) {
    const clipInfo = clips.find(c => c.id === clip.id);
    if (clipInfo?.sprite) {
        try {   
            clipInfo.sprite.time.offset = clip.startTime * 1e6;
            clipInfo.sprite.time.duration = clip.duration * 1e6;
            clipInfo.startTime = clip.startTime;
            clipInfo.duration = clip.duration;
            updateVideoDuration(clip.startTime + clip.duration);
        } catch (e) {
            console.warn('更新精灵尺寸失败:', e);
        }
    }
}

// 播放控制
function handlePlayPause() {
    if (clips.length === 0) {
        message.warning('请先添加片段');
        return;
    }

    try {
        if (playing.value) {
            avCvs.pause();
        } else {
            avCvs.play({ start: currentTime.value * 1e6 });
        }
    } catch (error) {
        console.error('播放错误:', error);
        message.warn('已经到达视频末尾');
    }
}

// 删除片段
async function deleteSelectedClip() {
    if (!selectedClip.value) {
        message.warning('请先选择一个片段');
        return;
    }

    try {
        const clipIndex = clips.findIndex(clip => clip.id === selectedClip.value.id);
        console.log(clipIndex)
        if (clipIndex === -1) return;
        // 从画布移除精灵
        if (clips[clipIndex].sprite && avCvs) {
            await avCvs.removeSprite(clips[clipIndex].sprite);
        }
        // 从时间轴和数组中移除
        timelineRef.value?.removeClip(selectedClip.value.id);
        clips.splice(clipIndex, 1);
        selectedClip.value = null;
        message.success('片段删除成功');
    } catch (error) {
        console.error('删除片段错误:', error);
        message.error('删除片段失败');
    }
}

// 导出视频
const exportMediaMaterialForm = ref(null);
async function exportVideo() {
    if (clips.length === 0) {
        message.warning('请先添加片段');
        return;
    }
    SmartLoading.show();
    try {
        const fileData = await createVideoFile();
        console.log(fileData)
        const formData = new FormData();
        formData.append('file', fileData.file);
        const res = await fileApi.uploadFile(formData, 1);
        console.log(res.data)
        exportMediaMaterialForm.value.showForm(res.data,fileData.totalDuration);
    } catch (error) {
        console.error('导出视频错误:', error);
        message.error('导出视频失败');
    } finally {
        SmartLoading.hide();
    }
}

// 创建视频文件
async function createVideoFile() {
    try {
        if (playing.value) avCvs.pause();

        // 获取主精灵
        const videoClips = clips.filter(clip => clip.type === 'VIDEO');
        const mainVideoSprite = videoClips[0]?.sprite || mainSprite.value || clips[0]?.sprite;
        // 计算总时长
        const totalDuration = Math.max(...clips.map(clip => clip.startTime + clip.duration || 0));

        // 创建合成器
        const combinator = await avCvs.createCombinator({
            __unsafe_hardwareAcceleration__: 'no-preference',
            mainSprite: mainVideoSprite,
            duration: totalDuration * 1e6
        });

        // 获取视频流并转换为文件
        const stream = await combinator.output();
        const response = new Response(stream);
        const blob = await response.blob();
        // 创建文件对象
        const fileName = `${videoClips[0]?.name || 'export'}_${Date.now().toString()}`;
        const file = new File([blob], `${fileName}.mp4`, { type: 'video/mp4' });

        return { file, totalDuration };
    } catch (error) {
        console.error('创建视频文件错误:', error);
        message.error('创建视频文件失败');
    }
}


// 分割视频片段
async function splitSelectedClip() {
    if (selectedClip.value?.type !== 'VIDEO') {
        message.warning('请先选择一个视频片段，并将时间指针位置在片段内部');
        return;
    }
    
    try {
        // 获取选中片段和对应精灵
        const clip = selectedClip.value;
        const clipIndex = clips.findIndex(c => c.id === clip.id);
        if (clipIndex === -1) return;
        const sprite = clips[clipIndex].sprite;
        // 计算分割点位置(微秒)
        const splitTime = currentTime.value * 1e6 - sprite.time.offset;
        const [clip1, clip2] = await sprite.getClip().split(splitTime);

        // 移除原精灵
        await avCvs.removeSprite(sprite);

        // 创建两个新精灵
        const sprite1 = new VisibleSprite(clip1);
        const sprite2 = new VisibleSprite(clip2);

        // 计算时间属性
        const firstDuration = splitTime / 1e6;
        const secondDuration = clip.duration - firstDuration;

        sprite1.time.offset = clip.startTime * 1e6;
        sprite1.time.duration = splitTime;

        sprite2.time.offset = (clip.startTime + firstDuration) * 1e6;
        sprite2.time.duration = clip.duration * 1e6 - splitTime;

        // 添加到画布
        await Promise.all([
            avCvs.addSprite(sprite1),
            avCvs.addSprite(sprite2)
        ]);

        // 移除原片段
        timelineRef.value.removeClip(clip.id);
        clips.splice(clipIndex, 1);

        // 创建并添加新片段
        const clip1Info = {
            id: `${clip.id}-1`,
            name: `${clip.name}-1`,
            type: clip.type,
            sprite: sprite1,
            startTime: clip.startTime,
            duration: firstDuration,
            originalDuration: firstDuration // 保留原始时长信息
        };
        
        const clip2Info = {
            id: `${clip.id}-2`,
            name: `${clip.name}-2`,
            type: clip.type,
            sprite: sprite2,
            startTime: clip.startTime + firstDuration,
            duration: secondDuration,
            originalDuration: secondDuration // 保留原始时长信息
        };

        timelineRef.value.addClip(0, clip1Info);
        timelineRef.value.addClip(0, clip2Info);
        clips.push(clip1Info, clip2Info);
        
        message.success('视频分割成功');
    } catch (error) {
        console.error('分割视频错误:', error);
        message.error('分割视频失败');
    }
}

// 销毁画布
function destroyCanvas() {
    if (avCvs) {
        avCvs.destroy();
        avCvs = null;
    }
}
onMounted(() => initCanvas());

onUnmounted(() => destroyCanvas());
</script>
