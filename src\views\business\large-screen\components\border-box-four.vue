<template>
  <div ref="borderBoxContainer" class="border-box-container">
    <div class="center-container">
      <decoration1 class="center-left-decoration" />
      <Decoration7 class="center-decoration" :color="['#18abd9', '#1ba6d0']">
        <text style="margin:1vw;">当前区域数据统计</text>
      </Decoration7>
      <decoration1 class="center-right-decoration"  />
    </div>
    <div class="border-box-content">
      <BorderBox10 :color="['#3896b3', '#329bbb']">
        <div class="content">
          <div class="water-level-container">
            <div class="water-level-item">
              <div class="water-level-wrapper">
                <water-level-pond
                    :config="getWaterLevelConfig(data.lightRoadOnlineRate,['#91c6fc', '#cce6ff'])"
                    style="width: 90%; height: 95%;"
                />
                <div class="water-level-label">路灯在线率</div>
              </div>
            </div>
            <div class="water-level-item">
              <div class="water-level-wrapper">
                <water-level-pond
                    :config="getWaterLevelConfig(data.gatewayOnlineRate,['#ff9966', '#ffcc99'])"
                    style="width: 90%; height: 95%;"
                />
                <div class="water-level-label">网关在线率</div>
              </div>
            </div>
          </div>
          <div class="grid-container">
            <div class="grid-item">
              <div class="item-inner">
                <div class="item-data">
                  <div class="item-title">设备数</div>
                  <div class="item-value" style="color:#fe8900;">{{ data.deviceCount }}</div>
                  <div class="item-unit">个</div>
                </div>
              </div>
            </div>
            <div class="grid-item">
              <div class="item-inner">
                <div class="item-data">
                  <div class="item-title">工单数</div>
                  <div class="item-value" style="color:#fdfd00;">{{ data.workOrderCount }}</div>
                  <div class="item-unit">个</div>
                </div>
              </div>
            </div>
            <div class="grid-item">
              <div class="item-inner">
                <div class="item-data">
                  <div class="item-title">网关数</div>
                  <div class="item-value" style="color:#e91f45;">{{ data.gatewayCount }}</div>
                  <div class="item-unit">个</div>
                </div>
              </div>
            </div>
            <div class="grid-item">
              <div class="item-inner">
                <div class="item-data">
                  <div class="item-title">路灯数</div>
                  <div class="item-value" style="color:#00ca7c;">{{ data.lightRoadCount }}</div>
                  <div class="item-unit">个</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </BorderBox10>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { BorderBox10, Decoration7, WaterLevelPond, Decoration1 } from '@kjgl77/datav-vue3';

const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
});

// --------------------------------------------------边框
const borderBoxContainer = ref(null);

onMounted(() => {
  if (!borderBoxContainer.value) {
    console.error('BorderBox container ref not exist');
    return;
  }
  console.log('BorderBox container mounted:', borderBoxContainer.value);
});

//  --------------------------------------------------处理数据
function getWaterLevelConfig(value,color){
  // 确保值是数字类型，并且在 [0, 100] 范围内
  const formattedValue = Math.round(parseFloat(value) || 0);
  const clampedValue = Math.max(0, Math.min(100, formattedValue)); // 限制值在 [0, 100] 范围内
  return {
    data: [clampedValue],
    shape: 'roundRect',
    colors: color,
    waveHeight: 5,
    formatter: '{value}%',
  };
}

</script>

<style scoped>
.border-box-container {
  position: absolute;
  top: 7vh;
  right: 1vw;
  width: 25vw;
  height: 27vh;
  z-index: 1000;
}

.center-container {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 3vh;
}

.center-right-decoration {
  width: 6vw;
  height: 2vh;
}

.center-left-decoration {
  transform: rotate(-180deg);
  width: 6vw;
  height: 2vh;
}

.center-decoration {
  position: absolute;
  color: white;
  font-size: 1.1vw;
  height: 3vh;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.border-box-content {
  position: absolute;
  top: 4.5vh;
  left: 0;
  width: 100%;
  height: calc(100% - 4.5vh);
}

.content {
  width: 100%;
  height: 100%;
  color: white;
  padding: 10px;
}

.water-level-container {
  width: 100%;
  height: 58%;
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.water-level-item {
  width: 48%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  /*background: rgba(0, 21, 44, 0.8);*/
  border-radius: 5px;
}

.water-level-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.water-level-label {
  margin-top: 5px;
  /*color: white;*/
  font-size: 0.8vw;
}

.grid-container {
  width: 100%;
  height: 50%;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  padding: 5px;
  box-sizing: border-box;
}

.grid-item {
  /*background: rgba(0, 21, 44, 0.8);*/
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.item-inner {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.item-data {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.item-title {
  font-size: 0.8vw;
  color: rgba(255, 255, 255, 0.7);
}

.item-value {
  font-size: 1.2vw;
  font-weight: bold;
  color: #017aff;
}

.item-unit {
  font-size: 0.8vw;
  color: rgba(255, 255, 255, 0.7);
}
</style>