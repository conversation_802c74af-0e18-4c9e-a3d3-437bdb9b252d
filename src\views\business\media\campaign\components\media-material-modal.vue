<template>
    <a-modal v-model:open="visible" title="选择素材" width="1000px" :footer="null" @cancel="handleCancel">
        <!-- 查询表单 -->
        <a-form class="smart-query-form" @submit.prevent>
            <a-row class="smart-query-form-row">
                <a-form-item label="素材名称" class="smart-query-form-item">
                    <a-input style="width: 200px" v-model:value="queryForm.name" placeholder="素材名称" />
                </a-form-item>
                <a-form-item label="素材类型" class="smart-query-form-item">
                    <a-select v-model:value="queryForm.type" allowClear placeholder="请选择素材类型"
                        :options="MEDIA_TYPE_ENUM.getOptions()" style="width: 150px" />
                </a-form-item>
                <a-form-item label="审核状态" class="smart-query-form-item">
                    <a-select v-model:value="queryForm.status" allowClear placeholder="请选择审核状态"
                        :options="MEDIA_STATUS_ENUM.getOptions()" style="width: 150px" />
                </a-form-item>
                <a-form-item class="smart-query-form-item">
                    <a-button type="primary" @click="onSearch">查询</a-button>
                    <a-button @click="resetQuery" class="smart-margin-left10">重置</a-button>
                </a-form-item>
            </a-row>
        </a-form>

        <!-- 素材表格 -->
        <a-table size="small" :dataSource="tableData" :columns="columns" rowKey="id" :loading="tableLoading" bordered
            :pagination="false" :row-selection="{
                selectedRowKeys: selectedRowKeyList,
                onChange: onSelectChange,
            }">
            <template #bodyCell="{ text, record, column }">
                <template v-if="column.dataIndex === 'type'">
                    {{ MEDIA_TYPE_ENUM.getDesc(text) }}
                </template>
                <template v-if="column.dataIndex === 'status'">
                    {{ MEDIA_STATUS_ENUM.getDesc(text) }}
                </template>
            </template>
        </a-table>

        <div class="smart-query-table-page">
            <a-pagination showSizeChanger showQuickJumper show-less-items :pageSizeOptions="PAGE_SIZE_OPTIONS"
                :defaultPageSize="queryForm.pageSize" v-model:current="queryForm.pageNum"
                v-model:pageSize="queryForm.pageSize" :total="total" @change="queryData" @showSizeChange="queryData"
                :show-total="(total) => `共${total}条`" />
        </div>

        <!-- 底部按钮 -->
        <div style="text-align:right;margin-top:16px;">
            <a-button @click="handleCancel" style="margin-right:8px;">取消</a-button>
            <a-button type="primary" @click="handleOk">确定</a-button>
        </div>
    </a-modal>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import { mediaMaterialApi } from '/@/api/business/media/material/media-material-api'
import { smartSentry } from '/@/lib/smart-sentry'
import { MEDIA_TYPE_ENUM, MEDIA_STATUS_ENUM } from '/@/constants/business/media/material/media-material-const'

const visible = ref(false)
const tableLoading = ref(false)
const tableData = ref([])
const selectedRowKeyList = ref([])

const columns = [
    { title: '序号', dataIndex: 'index', width: 80, customRender: ({ index }) => index + 1 },
    { title: '素材ID', dataIndex: 'id', ellipsis: true },
    { title: '素材名称', dataIndex: 'name', ellipsis: true },
    { title: '素材类型', dataIndex: 'type', ellipsis: true },
    { title: '文件格式', dataIndex: 'fileFormat', ellipsis: true },
    {title: '审核状态', dataIndex: 'status', ellipsis: true,},
    // { title: '上传人', dataIndex: 'createUserId', ellipsis: true },
    { title: '创建时间', dataIndex: 'createTime', ellipsis: true }
]

// 查询表单
const queryFormState = {
    name: undefined,
    type: undefined,
    status: undefined,
    pageNum: 1,
    pageSize: 10,
}
const queryForm = reactive({ ...queryFormState })
// 总数
const total = ref(0);
// 查询
async function queryData() {
    tableLoading.value = true
    try {
        const res = await mediaMaterialApi.queryPage(queryForm)
        tableData.value = res.data.list;
        total.value = res.data.total;
    } catch (error) {
        smartSentry.captureError(error)
        message.error('查询失败')
        tableData.value = []
    } finally {
        tableLoading.value = false
    }
}

// 搜索
function onSearch() {
    queryForm.pageNum = 1
    queryData()
}

// 重置
function resetQuery() {
    let pageSize = queryForm.pageSize
    Object.assign(queryForm, queryFormState)
    queryForm.pageSize = pageSize
    queryData()
}

// 选中项变化
function onSelectChange(keys) {
    selectedRowKeyList.value = keys
}

function show() {
    visible.value = true
    queryData()
}

// 关闭
function handleCancel() {
    visible.value = false
    selectedRowKeyList.value = []
}

// 确定，emit选中数据
const emit = defineEmits(['select'])
function handleOk() {
    const selected = tableData.value.filter(item => selectedRowKeyList.value.includes(item.id))
    visible.value = false
    // selectedRowKeyList.value = []
    emit('select', selected)
}

// 暴露show方法
defineExpose({ show })
</script>