/**
 * 照明计划（智能控制） api 封装
 *
 * @Author:    李帅兵
 * @Date:      2025-04-11 20:27:39
 * @Copyright  中山睿数信息技术有限公司
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const lightPlanApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/lightPlan/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/lightPlan/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/lightPlan/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/lightPlan/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
      return postRequest('/lightPlan/batchDelete', idList);
  },
  /**
   * 根据id查询详情 <AUTHOR>
   */
  detail: (id) => {
    return getRequest(`/lightPlan/detail/${id}`);
},
  /**
   *配置 <AUTHOR>
   */
   set: (idList) => {
    return postRequest('/lightPlan/set', idList);
},
  /**
   *分页查询 <AUTHOR>
   */
   lightSceneQueryPage: (idList) => {
    return postRequest('/lightScene/queryPage', idList);
},
};
