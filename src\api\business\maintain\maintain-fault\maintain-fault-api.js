/**
 * 设备报修单 api 封装
 *
 * @Author:    yourName
 * @Date:      2025-04-11 11:58:43
 * @Copyright  bdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const maintainFaultApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/maintainFault/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/maintainFault/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/maintainFault/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/maintainFault/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
      return postRequest('/maintainFault/batchDelete', idList);
  },
  audit: (param) => {
    return postRequest('/maintainFault/audit', param);
  },
};
