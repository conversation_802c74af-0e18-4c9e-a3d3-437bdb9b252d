<!--
  * 登录
  *
  * @Author:    1024创新实验室-主任：卓大
  * @Date:      2022-09-12 22:34:00
  * @Wechat:    zhuda1024
  * @Email:     <EMAIL>
  * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
  *
-->
<template>
  <div class="login-container">
    <div class="box-item desc">
      <img class="welcome-logo" :src="wiselumeLogo">
      <div class="welcome">
        <span>Wiselume</span>
        <br>
        <span>智光云联——智慧灯杆一体化平台</span>
      </div>
      <img class="welcome-img" :src="wiselumeDesc" />
    </div>
    <div class="box-item login">
      <a-config-provider :theme="{
        token: {
          colorPrimary: '#ff6200',
        },
      }">
        <a-tabs v-model:activeKey="activeKey" centered size="large" tabBarGutter="150px" tabBarStyle="color: #fff;">
          <a-tab-pane key="1" tab="登录">
            <account-login />
          </a-tab-pane>
          <a-tab-pane key="2" tab="手机登录">
            <phone-login />
          </a-tab-pane>
        </a-tabs>
      </a-config-provider>
      <div class="more">
        <div class="title-box">
          <p class="line"></p>
          <p class="title">其他方式登录</p>
          <p class="line"></p>
        </div>
        <div class="login-type">
          <img :src="wechatIcon" />
          <!-- <img :src="aliIcon"  /> -->
          <!-- <img :src="douyinIcon" /> -->
          <!-- <img :src="qqIcon"  /> -->
          <!-- <img :src="weiboIcon" /> -->
          <img :src="feishuIcon" @click="getFeishuQr" />
          <!-- <img :src="googleIcon" /> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import AccountLogin from './components/account-login.vue';
import PhoneLogin from './components/phone-login.vue';
import wiselumeDesc from '/@/assets/images/login/wiselume-desc.png';
import wiselumeLogo from '/@/assets/images/logo/wiselume-logo.png';
import wechatIcon from '/@/assets/images/login/wechat-icon.png';
import qqIcon from '/@/assets/images/login/qq-icon.png';
import feishuIcon from '/@/assets/images/login/feishu-icon.png';
import { loginThirdQrApi } from '/@/api/system/login-third-qr-api';
import _ from 'lodash';
import { useRouter, useRoute } from 'vue-router';
import { smartSentry } from '/@/lib/smart-sentry';
import { message } from 'ant-design-vue';
import { useUserStore } from "/src/store/modules/system/user.js";
import LocalStorageKeyConst from "/src/constants/local-storage-key-const.js";
import { localSave } from '/@/utils/local-util.js';

const activeKey = ref('1');
// 飞书登录临时code
const loginTmpCode = ref('');
async function getFeishuQr() {
  const res = await loginThirdQrApi.getQr(1);
  const goto = res.data;
  window.location.href = goto;
  var handleMessage = function (event) {
    // 使用 matchOrigin 和 matchData 方法来判断 message 和来自的页面 url 是否合法
    if (
      QRLoginObj.matchOrigin(event.origin) &&
      QRLoginObj.matchData(event.data)
    ) {
      loginTmpCode.value = event.data.tmp_code;
      // 在授权页面地址上拼接上参数 tmp_code，并跳转
      window.location.href = ` ${goto}&tmp_code=${loginTmpCode.value}`;
    }
  };
  if (typeof window.addEventListener != "undefined") {
    window.addEventListener("message", handleMessage, false);
  } else if (typeof window.attachEvent != "undefined") {
    window.attachEvent("onmessage", handleMessage);
  }
}
//请求参数
const formState = {
  code: undefined,
  state: undefined
}

const router = useRouter()
const route = useRoute()

const form = reactive({ ...formState })
//处理第三方登录回调
async function handleThirdLoginCallback() {
  const params = route.query
  if (!_.isEmpty(params)) {
    form.code = params?.code;
    form.state = params?.state;
    if (form.code && form.state) {
      try {
        // 调用登录回调接口
        const res = await loginThirdQrApi.Callback(form);
        // 保存token到本地
        localSave(LocalStorageKeyConst.USER_TOKEN, res.data.token ? res.data.token : '');
        //更新用户信息到pinia
        useUserStore().setUserLoginInfo(res.data);
        message.success('登录成功');
        router.push('/home');
      } catch (e) {
        smartSentry.captureError(e);
        message.error(e.message);
      }
    } else {
      message.warning('登录信息缺乏');
    }
  }
}
onMounted(() => {
  handleThirdLoginCallback();
})
</script>

<style lang="less" scoped>
// @import './login.less';
.login-container {
  width: 100%;
  height: 100%;
  background-image: url('/@/assets/images/login/background.png');
  background-size: cover;
  display: flex;
  align-items: center;
  justify-content: center;

  .box-item {
    width: 444px;
    height: 600px;

    &.desc {
      background: rgba(44, 37, 43, 0.611);
      border-radius: 12px 0px 0px 12px;
      box-shadow: 0px 16px 73px 8px rgba(203, 203, 203, 0.2);
      width: 650px;
    }

    &.login {
      background: rgb(42, 35, 41);
      border-radius: 0px 12px 12px 0px;
      padding: 34px 42px;
      position: relative;
    }

    .welcome {
      padding-bottom: 55px;

      span {
        color: #b5c7e3;
        font-weight: 400;
        font-size: 24px;
      }
    }

    .welcome-logo {
      width: 65px;
      height: 65px;
      float: left;
    }

    .welcome-img {
      width: 600px;
      height: 350px;
      margin: 0px 25px;
    }
  }


  .more {
    margin-top: 30px;

    .title-box {
      display: flex;
      align-items: center;
      justify-content: center;

      >p {
        margin-bottom: 0;
      }
    }

    .line {
      width: 114px;
      height: 1px;
      background: #e6e6e6;
    }

    .title {
      font-size: 14px;
      font-weight: 500;
      color: #a1aebe;
      margin: 0 19px;
    }

    .login-type {
      padding: 0 5px;
      margin-top: 25px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      >img {
        width: 30px;
        height: 30px;
      }
    }
  }
}
</style>