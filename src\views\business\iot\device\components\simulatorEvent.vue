<!--
    * 设备事件调试
    * 返回数据：对象类型
    * 
    * 
-->
<template>
    <a-row>
      <a-cascader
        v-model:value="identifier"
        :options="dataEvent"
        @change="eventChange"
        style="width: 100%"
        placeholder="请选择事件"
      />
    </a-row>
    <a-row style="margin: 16px 0">
      <code-editor ref="editorRef" v-model:value="code" :json="true" theme="nord" height="400px"/>
    </a-row>
    <a-row>
      <a-button type="primary" @click="sendCommand">发送指令</a-button>
      <a-button class="smart-margin-left10" @click="prettyCode">格式化数据</a-button>
      <a-button class="smart-margin-left10" @click="resetEvent">重置数据</a-button>
    </a-row>
  </template>
  
  <script setup>
  import { ref, watch } from 'vue';
  import { message } from 'ant-design-vue';
  import CodeEditor from '/@/components/business/code-editor/index.vue';
  import { validatejson, validatenull } from '/@/utils/validate';
  
  const props = defineProps({
    modelValue: {
      type: Object,
      default: () => ({})
    }
  });
  
  const emit = defineEmits(['sendCommand']);
  
  const code = ref('');
  const event = ref({});
  const identifier = ref([]);
  const dataEvent = ref([]);
  const editorRef = ref(null);
  
  // 监听模型数据变化
  watch(() => props.modelValue, (newVal) => {
    if (validatenull(newVal)) {
      return;
    }
    loadEvent();
  }, { deep: true });
  
  // 加载事件数据
  function loadEvent() {
    dataEvent.value = props.modelValue.blocks.map(block => ({
      value: block.id,
      label: block.blockName,
      children: props.modelValue.events
        .filter(event => event.blockId === block.id)
        .map(item => ({
          value: item.identifier,
          label: item.name + ' - ' + item.identifier,
        }))
    }));
  }
  
  // 事件选择变化
  function eventChange(value) {
    if (validatenull(value) || value.length === 0) {
      return;
    }
    identifier.value = value;
    event.value = props.modelValue.events.find(item => item.identifier === value[1]);
  }
  
  // 发送命令
  function sendCommand() {
    if (validatenull(identifier.value)) {
      message.warning('请先选择事件');
      return;
    }
    if (validatenull(code.value)) {
      message.warning('请填写调试数据');
      return;
    }
    if (!validatejson(code.value)) {
      message.warning('Json格式不正确');
      return;
    }
    const output = JSON.stringify(JSON.parse(code.value));
    const data = {
      eventName: event.value.name,
      eventType: event.value.eventType,
      identifier: identifier.value[1],
      output
    };
    emit('sendCommand', data);
  }
  
  // 格式化代码
  function prettyCode() {
    editorRef.value?.prettyCode();
  }
  
  // 重置事件
  function resetEvent() {
    code.value = '';
  }
  
  // 暴露方法供外部使用
  // defineExpose({
  //   loadEvent,
  //   resetEvent
  // });
  </script> 