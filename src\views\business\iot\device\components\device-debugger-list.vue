<!--
  * 设备选择弹窗
  * 
  * @Author:    your name
  * @Date:      2024-03-25
  * @Copyright  your company
-->
<template>
    <a-modal v-model:open="visible" title="请选择设备" width="1200px" @ok="handleOk" @cancel="handleCancel">
        <a-form class="smart-query-form">
            <a-row class="smart-query-form-row">
                <a-form-item label="设备名称" class="smart-query-form-item">
                    <a-input style="width: 150px" v-model:value="queryForm.deviceName" placeholder="请输入设备名称" />
                </a-form-item>
                <a-form-item label="设备密钥" class="smart-query-form-item">
                    <a-input style="width: 150px" v-model:value="queryForm.deviceSecret" placeholder="请输入设备密钥" />
                </a-form-item>
                <a-form-item label="唯一编号" class="smart-query-form-item">
                    <a-input style="width: 150px" v-model:value="queryForm.uniqueCode" placeholder="请输入唯一编号" />
                </a-form-item>
                <a-form-item class="smart-query-form-item">
                    <a-button type="primary" @click="onSearch">
                        <template #icon>
                            <SearchOutlined />
                        </template>
                        查询
                    </a-button>
                    <a-button @click="reset">
                        <template #icon>
                            <ReloadOutlined />
                        </template>
                        重置
                    </a-button>
                </a-form-item>
            </a-row>
        </a-form>

        <a-table size="small" :dataSource="tableData" :columns="columns" rowKey="id" bordered :loading="tableLoading"
            :pagination="false"
            :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange, preserveSelectedRowKeys: true, type: 'radio', }"
           >
            <template #bodyCell="{ column, record }">
                <!-- <template v-if="column.dataIndex === 'deviceStatus'">
                    <a-tag :color="record.deviceStatus === '离线' ? 'error' : 'success'">{{ record.deviceStatus }}</a-tag>
                </template> -->
            </template>
        </a-table>

        <div class="smart-query-table-page">
            <a-pagination showSizeChanger showQuickJumper show-less-items :pageSizeOptions="PAGE_SIZE_OPTIONS"
                :defaultPageSize="queryForm.pageSize" v-model:current="queryForm.pageNum"
                v-model:pageSize="queryForm.pageSize" :total="total" @change="queryData" @showSizeChange="queryData"
                :show-total="(total) => `共${total}条`" />
        </div>
    </a-modal>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons-vue';
import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
import { smartSentry } from '/@/lib/smart-sentry';
import { iotDeviceApi } from '/@/api/business/iot/device/iot-device-api';
import { message } from 'ant-design-vue';
import _ from 'lodash';

// 表格列定义
const columns = [
    {
        title: '产品编号',
        dataIndex: 'productKey',
        width: 120,
        ellipsis: true,
        align: 'center',
    },
    {
        title: '设备名称',
        dataIndex: 'deviceName',
        width: 120,
        ellipsis: true,
        align: 'center',
    },
    {
        title: '设备密钥',
        dataIndex: 'deviceSecret',
        width: 200,
        ellipsis: true,
        align: 'center',
    },
    {
        title: '唯一编号',
        dataIndex: 'uniqueNo',
        width: 200,
        ellipsis: true,
        align: 'center',
    },
    {
        title: '设备状态',
        dataIndex: 'deviceStatus',
        width: 100,
        ellipsis: true,
        align: 'center',
    },
    {
        title: '激活时间',
        dataIndex: 'createTime',
        width: 160,
        ellipsis: true,
        align: 'center',
    },
];

// 查询表单数据
const queryFormState = {
    pageNum: 1,
    pageSize: 10,
    deviceName: undefined,
    deviceSecret: undefined,
    uniqueCode: undefined,
    productKey: undefined,
};

const visible = ref(false);
const queryForm = reactive({ ...queryFormState });
const tableLoading = ref(false);
const tableData = ref([]);
const total = ref(0);

// 查询数据
async function queryData() {
    tableLoading.value = true;
    try {
        const res = await iotDeviceApi.queryPage(queryForm);
        tableData.value = res.data.list;
        total.value = res.data.total;
    } catch (e) {
        smartSentry.captureError(e);
    } finally {
        tableLoading.value = false;
    }
}

// 选择相关的响应式变量
const selectedRowKeys = ref([]);
const selectedRows = ref([]);

// 选择变化的处理函数
const onSelectChange = (keys, rows) => {
    selectedRowKeys.value = keys;
    selectedRows.value = rows;
};

const emit = defineEmits(['selectData']);

function handleOk() {
    visible.value = false;
    if (_.size(selectedRows.value)) {
        emit('selectData', selectedRows.value);
    }
}

function handleCancel() {
    visible.value = false;
    reset();
}

// 重置查询
function reset() {
    Object.assign(queryForm, queryFormState);
    selectedRowKeys.value = [];
    selectedRows.value = [];
    queryData();
}

// 查询
function onSearch() {
    queryForm.pageNum = 1;
    queryData();
}

// 打开弹窗
function showForm(productKey) {
    queryForm.productKey = productKey;
    visible.value = true;
    queryData();
}

defineExpose({
    showForm,
});
</script>