<!--
  * 配置示例
  *
  * @Author:    潘茜茜
  * @Date:      2025-03-27 20:50:38
  * @Copyright  2025 电子科技大学中山学院大数据与智能计算实验室
-->
  
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="示例标题" class="smart-query-form-item">
        <a-input style="width: 200px" v-model:value="queryForm.label" placeholder="请输入示例标题" />
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="onSearch">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  
  <a-card size="small" :bordered="false" :hoverable="true">
    <!-- 表格操作行-->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="showForm" type="primary" size="small">
          <template #icon>
            <PlusOutlined />
          </template>
          新建
        </a-button>
        <a-button @click="confirmBatchDelete" type="primary" danger size="small" :disabled="selectedRowKeyList.length === 0">
          <template #icon>
            <DeleteOutlined />
          </template>
          批量删除
        </a-button>
      </div>
    </a-row>

    <a-list :grid="{ gutter: 10, xs: 1, sm: 1, md: 2, lg: 2, xl: 3, xxl: 4 }" :data-source="data" :loading="initLoading">
          <template #renderItem="{ item, index }">
              <a-list-item>
              <a-card
                  hoverable
                  :loading="loading"
                  size="default"
                  class="demoCardImg demoCardColor"
              >
                  <template #title>
                    <a>{{ item.label }}</a>
                  </template>
                  <template #extra>
                    <div style="width: inherit; position: absolute; right: 30px; top: 15px">No{{ index + 1 }}</div>
                    <a-checkbox v-model:checked="item.checked" style="position: absolute; right: 5px; top: 3px" @change="onSelectChange(item)"/>
                  </template>
                  <div style="height: 100%; width: 100%; display: flex">
                    <div style="flex: 2; display: flex; flex-direction: column">
                      <span class="span-multiline-ellipsis">来源：
                        <span class="multiline-ellipsis">
                          <div>{{item.source}}</div>
                        </span>
                      </span>
                      <span class="span-multiline-ellipsis">类型：
                        <span class="multiline-ellipsis">
                          <div>{{item.type}}</div>
                        </span>
                      </span>
                      <span class="span-multiline-ellipsis">是否禁用：
                      <a-switch :checked="item.disabled === true" checked-children="是" un-checked-children="否" @change="switchChange(item)"/></span>
                      <span class="span-multiline-ellipsis">排序号：
                        <span class="multiline-ellipsis">
                          <a-input-number id="inputNumber" v-model:value="item.sort" :min="1" :max="10" @blur="sort(item)" />
                        </span>
                      </span>

                      <span class="span-multiline-ellipsis">部署时间：
                        <span class="multiline-ellipsis">
                          <div>{{item.deployTime}}</div>
                        </span>
                      </span>
                  </div>
                  </div>
                  <template #actions>
                  <span style="color: #108ee9" @click="showDetail(item.id)"><setting-outlined key="setting" /> 配置</span>
                  <span style="color: #108ee9" @click="showForm(item)"><FormOutlined /> 编辑</span>
                  <span style="color: #f56c6c" @click="onDelete(item)"><DeleteOutlined /> 删除</span>
                  </template>
              </a-card>
              </a-list-item>
          </template>
      </a-list>
      <div class="smart-query-table-page">
          <a-pagination
              showSizeChanger
              showQuickJumper
              show-less-items
              :pageSizeOptions="PAGE_SIZE_OPTIONS"
              :defaultPageSize="queryForm.size"
              v-model:current="queryForm.current"
              v-model:pageSize="queryForm.size"
              :total="total"
              @change="queryData"
              @showSizeChange="queryData"
              :show-total="(total) => `共${total}条`"
          />
      </div>

      <iotEdgeDemoForm ref="formRef" @reloadList="queryData" />
      <!-- <EquipmentInstanceDetail ref="detailRef" /> -->
  </a-card>
</template>
<script setup>
  import { reactive, ref, onMounted } from 'vue';
  import { message, Modal } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { PAGE_PATH_404, PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import DictSelect from '/@/components/support/dict-select/index.vue';// 字典下拉框
  import TableOperator from '/@/components/support/table-operator/index.vue';
  import { iotEdgeDemoApi } from '/@/api/business/iot/edge/iot-edge-demo-api';
  import { DEVICE_TYPE_ENUM } from '/@/constants/business/iot/product/iot-product-const.js';
  import iotEdgeDemoForm from './components/iot-edge-demo-form.vue';
  import { TwitterSquareFilled } from '@ant-design/icons-vue';
  import {useIotStore} from '/@/store/modules/business/iot/iot.js';
  // ---------------------------- 表格 ----------------------------
  //加载覆盖
  const initLoading = ref(false);
  const loading = ref(false);
  // 表格数据
  const data = ref([]);
  // ---------------------------- 分页 ----------------------------
  const total = ref(0);
  const queryFormState = {
    label: undefined,
    source: "demo",
    current: 1,
    size: 6,
  };
// 查询表单form
const queryForm = reactive({ ...queryFormState });
// 重置查询条件
function resetQuery() {
  let pageSize = queryForm.pageSize;
  Object.assign(queryForm, queryFormState);
  queryForm.pageSize = pageSize;
  queryData();
}
// 搜索
function onSearch() {
  queryForm.pageNum = 1;
  queryData();
}
// 查询数据
async function queryData() {
  initLoading.value = true;
  loading.value = true;
  try {
  let queryResult = await iotEdgeDemoApi.list(queryForm);
    total.value = queryResult.data.total;
    data.value = queryResult.data.records;
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    initLoading.value = false;
    loading.value = false;
  }
}
onMounted(queryData);
// ---------------------------- 添加/修改 ----------------------------
const formRef = ref();

//新增/编辑
function showForm(data) {
  formRef.value.show(data);
}
//排序
async function sort(data) {
  try {
    let result = await iotEdgeDemoApi.sort({
      id:data.id,
      sort:data.sort
    });
    queryData();
  } catch (e) {
    smartSentry.captureError(e);
  }
}
//禁用
async function switchChange(data) {
  if (!data.id) {
    return;
  }
  try {
    data.disabled = data.disabled === true ? false : true;
    await iotEdgeDemoApi.disabled(data);
    queryData();
  } catch (err) {
    smartSentry.captureError(err);
  }
}

// ---------------------------- 单个删除 ----------------------------
function onDelete(data) {
  Modal.confirm({
    title: '提示',
    content: '确定要删除示例吗?',
    okText: '删除',
    okType: 'danger',
    onOk() {
      selectedRowKeyList.value.push(data.id);
      requestBatchDelete();
    },
    cancelText: '取消',
    onCancel() {},
  });
}
// ---------------------------- 批量删除 ----------------------------

// 选择表格行
const selectedRowKeyList = ref([]);

function onSelectChange(item) {
  const index = selectedRowKeyList.value.findIndex((select) => select === item.id);
  console.log(index);
  
  if(index==-1){
    item.checked=true;
    selectedRowKeyList.value.push(item.id);
  }
  else{
    item.checked=false;
    selectedRowKeyList.value = selectedRowKeyList.value.filter((id) => id !== item.id);
  }
  console.log(selectedRowKeyList.value);
  
}

// 批量删除
function confirmBatchDelete() {
  Modal.confirm({
    title: '提示',
    content: '确定要批量删除这些数据吗?',
    okText: '删除',
    okType: 'danger',
    onOk() {
      requestBatchDelete();
    },
    cancelText: '取消',
    onCancel() {},
  });
}

//请求批量删除
async function requestBatchDelete() {
  try {
    SmartLoading.show();
    await iotEdgeDemoApi.remove(selectedRowKeyList.value);
    message.success('删除成功');
    selectedRowKeyList.value=[];
    queryData();
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    SmartLoading.hide();
  }
}

// 正确获取 token 的方式
const iotStore = useIotStore(); // 获取 store 实例
iotStore.getIotToken();  // 调用方法获取 token 
const token = iotStore.iotToken; // 获取 token 值

// 然后在 showDetail 函数中使用
function showDetail(id) {
    const baseUrl = 'http://node-red.iot.wisedataacademic.com:1880/#flow/';
    const fullUrl = `${baseUrl}${id}?token=${token}`;
    // console.log("打印网站地址1", fullUrl,id);
    window.open(fullUrl);
    // console.log("打印网站地址2","1",token);
}


</script>

<style scoped lang="less">
  :deep(.ant-card-body) {
    padding: 10px 20px;
  }
  .scroll-container {
    height: 580px; /* 设置容器的高度 */
    overflow-y: auto; /* 启用 y 轴滚动 */
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  ::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 8px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
  .multiline-ellipsis {
    display: -webkit-box; /* 使用弹性盒子布局 */
    -webkit-box-orient: vertical; /* 垂直排列 */
    // -webkit-line-clamp: 1; /* 显示两行 */
    overflow: hidden; /* 隐藏溢出内容 */
    text-overflow: ellipsis; /* 显示省略号 */
    width: 160px; /* 设置固定宽度 */
    
  }
  .span-multiline-ellipsis {
    display: flex;
    width: 230px;
    margin: 5px;
    
  }
  .demoCardColor{
    background: linear-gradient(rgba(128, 128, 128, 0.1), white),url('src/assets/images/catagory/icon7.svg');
    background-size: cover,50%,50%;
    background-repeat: no-repeat;
    background-position: 100%;
  }
</style>
