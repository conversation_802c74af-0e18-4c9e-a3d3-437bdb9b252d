<template>
  <div class="map-container">
    <div id="container" ref="mapContainer"></div>
    <!-- 区域搜索面板 -->
    <div class="search-panel" v-if="showSearch">
      <div class="search-panel-header">
        <span class="title">区域搜索</span>
        <a-button type="text" @click="toggleSearch">
          <close-outlined />
        </a-button>
      </div>
      <map-search @select-region="handleSelectRegion" />
    </div>
    
    <!-- 搜索按钮 -->
    <div class="search-button" v-if="!showSearch">
      <a-button type="primary" @click="toggleSearch">
        <search-outlined />搜索区域
      </a-button>
    </div>
    
    <!-- 左侧图表面板 - 在搜索面板下方 -->
    <div class="graph-panel" :class="{ 'with-search': showSearch }">
      <map-left-graph :deviceList="deviceInfoList" ref="mapLeftGraph" />
    </div>
    
    <!-- 右侧设备列表面板 -->
    <div class="right-graph-panel">
      <map-right-graph :deviceList="deviceInfoList" />
    </div>
  </div>
</template>

<script setup>
  import {ref, onMounted, onUnmounted, watch, nextTick, reactive} from 'vue';
  import {message} from 'ant-design-vue';
  import AMapLoader from'@amap/amap-jsapi-loader';
  import { iotDeviceApi } from '/@/api/business/iot/device/iot-device-api';
  import streetLampIcon from '/@/assets/images/light/lamp-street.png'
  import MapSearch from './components/map-search.vue';
  import MapLeftGraph from './components/map-LeftGraph.vue';
  import MapRightGraph from './components/map-RightGraph.vue';
  
  // 图表引用
  const mapLeftGraph = ref(null);
  
  //--------------------------初始化地图-------------------------------
  let map=null;
  let AMapInstance= null;
  let mouseTool=null;
  let polygon = null;
  let currentOverlay = null;
  
  const initMap =()=>{
    window._AMapSecurityConfig = {
      securityJsCode: 'fd83369091f64a0f25572251e0c9eae5',
    };
    AMapLoader.load({
      key: '1778f4eaad1a5a43d9b04ef0c9690b3f',
      version: '2.0',
      plugins: [
         'AMap.MouseTool',
         'AMap.ToolBar',
         'AMap.Scale',
         'AMap.InfoWindow',
         'AMap.Marker'    
       ]
    }).then((AMap)=>{
      AMapInstance = AMap;
      map = new AMap.Map('container',{
        center:[113.392738, 22.529924],
        zoom:16,
        viewMode: '2D'
      })
      mouseTool = new AMap.MouseTool(map);
      mouseTool.on('draw', (event) => {
        if (event.obj instanceof AMap.Polygon) {
          polygon = event.obj;
          currentOverlay = event.obj; 
        }
      });

      const scale = new AMap.Scale({
        position: {
          bottom: '20px',
          left: '20px'
        },
        offset: new AMap.Pixel(0, 0)
      });
      map.addControl(scale);

      const toolBar = new AMap.ToolBar({
        position: {
          top: '110px',
          right: '40px',
        },
      });
      map.addControl(toolBar);
      //监听地图的移动
      map.on('moveend',getDeviceInfo);
      //监听地图的缩放
      map.on('zoomend',getDeviceInfo);
      // 地图加载完成后显示已获取的设备信息
      getDeviceInfo();
    })
    .catch((e) =>{
      message.error('地图初始化失败',e);
    })
  }
  
  //-------------------获取区域信息---------------------------------
  // 搜索面板显示控制
  const showSearch = ref(true);
  const deviceRegionId = ref(null);
  
  // 切换搜索面板显示/隐藏
  function toggleSearch() {
    showSearch.value = !showSearch.value;
  }
  
  // 处理选择的区域
  function handleSelectRegion(region) {
    if (!region) {
      deviceRegionId.value = null; // 清除区域ID筛选
      map.setCenter([113.392738, 22.529924]);
      map.setZoom(14);
      getDeviceInfo();
      return;
    }
    deviceRegionId.value = region.id;
    if (region.longitude && region.latitude) {
      if (region.longitude >= 73.66 && region.longitude <= 135.05 && 
        region.latitude >= 3.86 && region.latitude <= 53.55) {
        map.setCenter([region.longitude, region.latitude]);
        map.setZoom(16);
      } else {
        message.warning('位置超出范围，显示默认位置');
        map.setCenter([113.392738, 22.529924]);
        map.setZoom(14);
      }
    } else {
      message.warning('该区域没有位置信息');
    }
    getDeviceInfo();
  }
  
  //------------------获取设备信息------------------------
  let deviceMarkers = []; // 用于存储设备标记
  let infoWindow = null; // 信息窗口
  const deviceInfoList = ref([]); // 设备信息
  
  async function getDeviceInfo(){
    try {
      const queryData=reactive({
        pageNum: 1,
        pageSize: 500,
        regionId: deviceRegionId.value
      })
      const result = await iotDeviceApi.queryDeviceByRegion(queryData)
      if (!result.data) {
        clearDeviceMarkers();
        deviceInfoList.value = [];
      } else {
        clearDeviceMarkers();
        deviceInfoList.value = result.data.map(item => ({
          id: item.id,
          productName: item.productName,
          deviceName: item.deviceName,
          longitude: item.longitude,
          latitude: item.latitude,
          deviceStatus: item.deviceStatus || 0, // 确保deviceStatus有默认值
          activeTime: item.activeTime,
          deviceType: item.deviceType
        }));
      }
      
      // 更新图表
      nextTick(() => {
        if (mapLeftGraph.value) {
          mapLeftGraph.value.updateCharts();
        }
      });
      
      if(map && AMapInstance){
        addDeviceMarker();
      }
    } catch (error) {
      console.error('获取设备信息失败:', error);
      message.error('获取设备信息失败');
    }
  }
  
  const addDeviceMarker=()=>{
    if(!map || !AMapInstance || !deviceInfoList.value || !deviceInfoList.value.length) return;
    //清除所有的标记
    clearDeviceMarkers();
    if(!deviceInfoList.value || !deviceInfoList.value.length) {
      message.info('当前区域暂无设备');
      return;
    }
    deviceInfoList.value.forEach(device => {
      // 跳过没有经纬度的设备
      if (!device.longitude || !device.latitude) return;
      const markerContent = document.createElement('div');
      markerContent.className = 'custom-device-marker';
      markerContent.innerHTML = `
        <div style="position: relative;">
          <img src="${streetLampIcon}" style="width: 50px; height: 50px; cursor: pointer;">
        </div>
      `;
      const marker = new AMapInstance.Marker({
        position: [device.longitude, device.latitude],
        content: markerContent,  // 使用自定义HTML内容
        offset: new AMapInstance.Pixel(-12, -12), 
        title: device.deviceName
      });
      marker.on('click',() =>{
        showDeviceInfo(device, marker.getPosition());
      })
      //添加到地图里面去
      map.add(marker);
      deviceMarkers.push(marker);
    })
  }
  
  //清除所有设备标记
  const clearDeviceMarkers=()=>{
    // 关闭信息窗口
    if(infoWindow) {
      infoWindow.close();
      infoWindow = null;
    }
    // 清除标记
    if(map && deviceMarkers.length >0){
      deviceMarkers.forEach(marker =>{
        map.remove(marker)  
      });
      deviceMarkers=[];
    }
  }
  
  const showDeviceInfo=(device,position)=>{
    if(infoWindow){
      infoWindow.close();
    }
    const content =`
     <div style="padding: 8px 0;">
        <div style="line-height: 1.5;">
          <b style="font-size: 14px; color: #333;">设备名称: ${device.deviceName || '未命名设备'}</b>
          <p style="margin: 6px 0;">产品名称: ${device.productName || '未知'}</p>
        </div>
      </div>
    `
    infoWindow = new AMapInstance.InfoWindow({
      content: content,
      offset: new AMapInstance.Pixel(0, -30),
      closeWhenClickMap: true
    });
    infoWindow.open(map, position);
  }
  
  onMounted(()=>{
    initMap();
  })
  
  onUnmounted(()=>{
    if(map){
      map.destroy();
      map = null;
    }  
  })
</script>

<style scoped> 
  #container {
    width: 100%;
    height: 100%;
  }
  
  .map-container {
    position: relative;
    width: 100%;
    height: 100%;
  }
  
  .search-panel {
    position: absolute;
    top: 10px;
    left: 10px;
    width: 300px;
    height: 45%; 
    background: white;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 100;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  
  .search-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .search-panel-header .title {
    font-weight: bold;
    font-size: 16px;
  }
  
  .search-button {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 100;
  }
  
  .graph-panel {
    position: absolute;
    top: 46%; 
    left: 10px;
    height: 40%;
    z-index: 90;
    transition: top 0.3s ease;
  }
  
  .graph-panel:not(.with-search) {
    top: 50px;
  }
  
  .right-graph-panel {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 100;
    height: 100%;
  }
</style>