<!--
  * 品类信息
  *
  * @Author:    文希希
  * @Date:      2025-03-26 17:17:51
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
  <div class="section">
    <a-descriptions title="品类详情" :column="3" size="default" :bordered="false">
      <a-descriptions-item label="品类行业">{{ categoryData.categoryIndustryName }}</a-descriptions-item>
      <a-descriptions-item label="品类场景">{{ categoryData.categorySceneName }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ categoryData.createTime }}</a-descriptions-item>
    </a-descriptions>
    <a-divider />

    <a-descriptions title="标签信息" :column="1" size="default" :bordered="false">
      <a-descriptions-item label="标签列表">
        <div class="tag-container">
          <a-tag v-for="tag in tagList" :key="tag.key" color="blue" class="tag-item"> {{ tag.label }}：{{ tag.labelValue }} </a-tag>
          <a-button type="link" @click="visible = true">编辑</a-button>
        </div>
      </a-descriptions-item>
    </a-descriptions>
    <!-- 编辑模态框 -->
    <a-modal title="品类标签配置" :open="visible" width="800px" @ok="onSubmit" @cancel="onClose">
      <a-button type="primary" @click="addRow">
        <template #icon>
          <PlusOutlined />
        </template>
        新建
      </a-button>
      <a-table :dataSource="tableData" :columns="columns" :pagination="false" bordered style="margin-top: 15px">
        <template #bodyCell="{ text, record, column }">
          <template v-if="column.dataIndex === 'label'">
            <a-input v-model:value="record.label" placeholder="请输入标签名" :allowClear="true" />
          </template>
          <template v-if="column.dataIndex === 'labelValue'">
            <a-input v-model:value="record.labelValue" placeholder="请输入标签值" :allowClear="true" />
          </template>
          <template v-if="column.dataIndex === 'action'">
            <div class="smart-table-operate">
              <a-button type="link">
                <template #icon>
                  <PlusCircleTwoTone />
                </template>
                保存
              </a-button>
              <a-button type="link" @click="deleteRow(record.key)">
                <template #icon>
                  <CloseCircleTwoTone />
                </template>
                删除
              </a-button>
            </div>
          </template>
        </template>
      </a-table>
    </a-modal>
  </div>
</template>

<script setup>
  import { ref, reactive, computed } from 'vue';
  import { PlusOutlined, CloseCircleTwoTone, PlusCircleTwoTone } from '@ant-design/icons-vue';

  //定义props
  const props = defineProps({
    categoryId: {
      type: String,
      required: true,
    },
    categoryData: {
      type: Object,
      default: () => ({}),
    },
  });

  //-------------------------------编辑模态框显示----------------------
  const visible = ref(false);
  const form = reactive({});
  const onSubmit = () => {
    //TODO 提交表单
    visible.value = false;
  };
  const onClose = () => {
    visible.value = false;
  };

  //---------------------------------获取品类详情数据

  //------------------------------表格新增动态列------------------------
  //表格列定义
  const columns = [
    {
      title: '标签名',
      dataIndex: 'label',
    },
    {
      title: '标签值',
      dataIndex: 'labelValue',
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
    },
  ];

  const tableData = ref([]);

  // 标签列表数据，自动过滤有效标签
  const tagList = computed(() => {
    return tableData.value.filter((item) => item.label && item.labelValue);
  });
  //新增行
  const addRow = () => {
    const newKey = Date.now().toString();
    tableData.value.push({
      key: newKey,
      label: '',
      labelValue: '',
    });
  };
  //删除行
  const deleteRow = (key) => {
    tableData.value = tableData.value.filter((item) => item.key !== key);
  };
</script>

<style scoped>
  .section {
    margin-top: 20px;
    padding: 0 20px;
  }

  .section-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 16px;
    color: rgba(0, 0, 0, 0.85);
  }

  .info-row {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  .info-item {
    display: flex;
    align-items: baseline;
  }
  .tag-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
  }

  .tag-item {
    margin-right: 8px;
    margin-bottom: 8px;
  }
</style>
