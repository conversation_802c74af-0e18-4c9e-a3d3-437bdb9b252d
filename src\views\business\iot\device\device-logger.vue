<template>
  <div style="background-color: #fff;">
    <a-container>
      <div style="padding: 30px;">
        <a-row>
          <a-text class="title">所属产品：</a-text>
          <a-select v-model="productKey" placeholder="请选择产品" allow-clear show-arrow @change="productChange" class="margin-node" style="width: 200px">
            <!-- <a-select-option
                v-for="item in productSelect"
                :key="item.productKey"
                :label="item.productName"
                :value="item.productKey">
            </a-select-option> -->
          </a-select>
          <a-text class="margin-node title">查询设备：</a-text>
          <a-input v-model="deviceId" placeholder="请选择设备" style="width: 200px" />
          <a-button :loading="loading" :disabled="isConnected" type="primary" class="margin-node" @click="onConnect">开启日志</a-button>
          <a-button :disabled="!isConnected" type="text" class="margin-node custom-close-button" @click="disConnect">关闭日志</a-button>
          <a-button @click="openTsl">物模型 TSL</a-button>
        </a-row>
      </div>

      <a-row v-if="isConnected">
        <a-col :span="24">
          <a-tabs v-model:activeKey="activeKey" tab-position="left" type="card">
            <a-tab-pane key="1" tab="物模型日志">
              <device-function :product-key="productKey" :device-id="deviceId"></device-function>
            </a-tab-pane>
            <a-tab-pane key="2" tab="设备日志">
              <device-log :product-key="productKey" :device-id="deviceId" :device-name="deviceName"></device-log>
            </a-tab-pane>
          </a-tabs>
        </a-col>
      </a-row>
      <a-row v-if="!isConnected">
        <a-col :span="24" style="text-align: center; padding: 20px;">
          <img style="width: 200px; height: 200px" src="/@/assets/images/product/icon4.svg" />
          <h2 style="margin: 20px 0;">设备日志库</h2>
          <p style="margin: 30px 0; color: gray;">设备日志库可随时查看每一台设备的属性、命令、事件等总览的日志信息</p>
          <a-alert type="info" show-icon style="text-align: center; width: 30%; margin: 0 auto; margin-bottom: 20px; border-radius: 4px; background-color: #f5f5f5; border: 1px solid #e8e8e8;">
            <template #message>
              <div>
                <a-text size="small" class="alert-text">
                  设备日志仅可查看特定设备的日志信息，若需要查看设备的其他信息请移步
                </a-text>
                <a-text size="small" type="primary" class="cursor alert-text" @click="viewDevice">
                  设备管理
                </a-text>
              </div>
            </template>
          </a-alert>
          <a-button type="primary" @click="onConnect()">开启设备日志库</a-button>
        </a-col>
      </a-row>
    </a-container>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import DeviceFunction from '/@/views/business/iot/device/components/device-function.vue';
import DeviceLog from '/@/views/business/iot/device/components/device-log.vue';

const router = useRouter();
const productKey = ref('');
const deviceId = ref('');
const isConnected = ref(false);
const loading = ref(false);
const activeKey = ref('1'); // 默认选中第一个标签页
const visibleTsl = ref(false);
const blockId = ref(null);
const tsl = ref('');
const productSelect = ref([]);
const dataBlock = ref([]);

const productChange = () => {
  console.log('Product changed');
};

const onConnect = () => {
  isConnected.value = true;
};

const disConnect = () => {
  isConnected.value = false;
};

const openTsl = () => {
  visibleTsl.value = true;
};

const beforeCloseTsl = () => {
  console.log('Modal closed');
};

const blockTslChange = () => {
  console.log('Block changed');
};

const viewDevice = () => {
  router.push({
    path: '/iot/device/list', // 跳转到设备管理页面
  });
};

const onLoadDevice = () => {
  console.log('Load device');
};
</script>

<style scoped>
.margin-node {
  margin-left: 12px;
}
.cursor {
  cursor: pointer;
  color: #2a7ff;
}
.title {
  margin-top: 7px;
}
.custom-close-button {
  background-color: #ffe6e6;
  color: #ff4d4f;
  border-color: #ff4d4f;
  border-radius: 4px;
  margin-right: 12px;
}
.custom-close-button:hover {
  background-color: #ffcccc;
  color: #ff4d4f;
  border-color: #ff4d4f;
}
.alert-text {
  font-size: 12px; /* 调整字体大小 */
}
</style>