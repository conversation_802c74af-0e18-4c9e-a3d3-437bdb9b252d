<!--
  * 工单
  *
  * @Author:    yourName
  * @Date:      2025-04-11 11:45:56
  * @Copyright  bdic
-->
<template>
  <a-modal
      :title="form.id ? '编辑' : '添加'"
      :width="500"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }" >
        <a-form-item label="维修单"  name="faultId">
          <a-select 
          v-model:value="form.faultId"
          placeholder="选择报修单"
          :allowClear="true"
          >
            <a-select-option v-for="item in faultIdOptions" :key="item.value" :value="item.value">
              <div>
                {{ item.label }}
                <br />
                <span style="font-size: 12px; color: #999">{{ item.desc }}</span>
              </div>
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="工单编号"  name="orderNumber">
          <a-input-number style="width: 100%" v-model:value="form.orderNumber" placeholder="留空则自动填充" />
        </a-form-item>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { reactive, ref, nextTick, onMounted } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { maintainOrderApi } from '/@/api/business/work-order/maintain-order-api';
  import { maintainFaultApi } from '/@/api/business/maintain/maintain-fault/maintain-fault-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show() {
    Object.assign(form, formDefault);
    // 使用字典时把下面这注释修改成自己的字典字段 有多个字典字段就复制多份同理修改 不然打开表单时不显示字典初始值
    // if (form.status && form.status.length > 0) {
    //   form.status = form.status.map((e) => e.valueCode);
    // }
    getDeviceIdOptions()
    visibleFlag.value = true;
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
      faultId: undefined, //维修单id
      orderNumber: undefined, //工单编号
  };

  let form = reactive({ ...formDefault });

  const rules = {
      faultId: [{ required: true, message: '维修单id 必填' }],
  };
   //维修单下拉数组
   const faultIdOptions = ref([]);
    // 获取设备下拉框列表
    async function getDeviceIdOptions() {
      try {
        const res = await maintainFaultApi.queryPage({
          pageNum: 1,
          pageSize: 500,
          checkOpinion: 1,
          faultHandleStatus: 0,
        });
        faultIdOptions.value =res.data.list.map((item)=>{
          return {
            label: item.faultNumber,
            value: item.id,
            desc: item.faultRemark,
          }
        })
       
      } catch (err) {
        smartSentry.captureError(err);
      }
    }
  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
        await maintainOrderApi.add(form);
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  defineExpose({
    show,
  });
  // onMounted(getDeviceIdOptions);
</script>
