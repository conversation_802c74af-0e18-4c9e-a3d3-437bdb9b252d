/**
 * 设备表 api 封装
 *
 * @Author:    林丽专
 * @Date:      2025-03-27 22:21:07
 * @Copyright  中山睿数信息技术有限公司 2025
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const iotRuleApi = {

    /**
     * 自定义分页查询  <AUTHOR>
    queryPage: (param) => {
        return postRequest('/gateway/page', param);
    },
    /**
     * 分页查询  <AUTHOR>
    query: (param) => {
        return postRequest('/gateway/list', param);
    },
    /**
     * 增加  <AUTHOR>
    add: (param) => {
        return postRequest('/gateway/save', param);
    },

    /**
     * 修改  <AUTHOR>
    update: (param) => {
        return postRequest('/gateway/update', param);
    },
    /**
     * 删除  <AUTHOR>
    delete: (param) => {
        return postRequest(`/gateway/remove`,param);
    },

    /**
     * 修改  <AUTHOR>
    sort: (param) => {
        return postRequest('/gateway/sort', param);
    },
    /**
     * 修改  <AUTHOR>
    disabled: (param) => {
        return postRequest('/gateway/disabled', param);
    },
    // /**
    //  * 删除  <AUTHOR>  */
    // delete: (id) => {
    //     return getRequest(`/iotDevice/delete/${id}`);
    // },

    // /**
    //  * 批量删除  <AUTHOR>  */
    // batchDelete: (idList) => {
    //     return postRequest('/iotDevice/batchDelete', idList);
    // },

};
