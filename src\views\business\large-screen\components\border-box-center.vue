<template>
  <div ref="borderBoxContainer" class="border-box-container">
    <div class="center-container">
      <decoration1 class="center-left-decoration" />
      <Decoration7 class="center-decoration" :color="['#18abd9', '#1ba6d0']">
        <text style="margin:1vw;">素材投放排行 Top10</text>
      </Decoration7>
      <decoration1 class="center-right-decoration"  />
    </div>
    <div class="border-box-content">
      <BorderBox10 :color="['#3896b3', '#329bbb']">
        <div class="content">
          <scroll-ranking-board :config="chartConfig" class="ranking-board" />
        </div>
      </BorderBox10>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, onMounted, watch} from 'vue';
import { BorderBox10, Decoration7,Decoration1 } from '@kjgl77/datav-vue3';
import { ScrollRankingBoard } from '@kjgl77/datav-vue3';

const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
});

// ---------------------------------------------------边框
const borderBoxContainer = ref(null);

onMounted(() => {
  if (!borderBoxContainer.value) {
    console.error('BorderBox container ref not exist');
    return;
  }
  console.log('BorderBox container mounted:', borderBoxContainer.value);
});

//----------------------------------------------------数据处理
const rankingConfig = ref([]);
const chartConfig = ref({});

watch(() => props.list, () => {
  initializeData();
});

// 初始化数据
const initializeData = () => {
  // 排名轮播表配置
  rankingConfig.value = props.list.map(item => ({
    name: item.mediaName,
    value: item.mediaUseTimes
  }));

  console.log('Ranking config:', rankingConfig.value);

  chartConfig.value = {
    data: rankingConfig.value,
    unit: '次',
    waitTime: 2000,
    rowNum: 4,
    carousel: 'single',
    fontSize: '1.2vw',
    color: '#099dbe',
    textColor: 'white',
  };
}
</script>

<style scoped>
.border-box-container {
  position: absolute;
  top: 65vh;
  left: 31%;
  width: 37vw;
  height: 28vh;
  z-index: 1000;
}

.center-container {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 3vh;
}

.center-right-decoration {
  width: 11vw;
  height: 2vh;
}

.center-left-decoration {
  transform: rotate(-180deg);
  width: 11vw;
  height: 2vh;
}

.center-decoration {
  position: absolute;
  color: white;
  font-size: 1.1vw;
  height: 3vh;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.border-box-content {
  position: absolute;
  top: 4.5vh;
  left: 0;
  width: 100%;
  height: calc(100% - 4.5vh);
}

.content {
  width: 100%;
  height: 100%;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
}

.ranking-board {
  width: 95%;
  height: 100%;
}
</style>