<!--
  * 控制地图-设备卡片
  *
  * @Author:    骆伟林
  * @Date:      2025-03-26 17:51:27
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
   <a-list :data-source="processedLampList"  style="height: 100%;">
      <template #renderItem="{ item }">
        <a-list-item @click="goLampDetail(item)">
          <div class="entire">
            <!-- 渐变背景 -->
            <div class="card-content-bg1" :style="{ background: bgColor(item) }"></div>

            <!-- 工作时长标签 -->
            <div class="card-workHours" :style="{ background: tagColor(item) }">
              <div class="productStatus">
                <span>工作时长：{{ item.workDuration ?? '暂无' }}</span>
              </div>
            </div>

            <!-- 状态/开关切换 -->
            <div @mouseover="item.isSwitchShow = true" @mouseout="item.isSwitchShow = false">
              <div v-if="!item.isSwitchShow" class="card-state" :style="{ background: tagColor(item) }">
                <div class="productStatus">
                  <span class="ant-badge-status-dot" :style="{ background: pointColor(item) }"></span>
                  <span class="ant-badge-status-text">{{ statusTitle(item) }}</span>
                </div>
              </div>
              <div v-else class="card-switch">
                <a-switch
                  :disabled="!item.onlineStatus"
                  v-model:checked="item.data.switchState"
                  :checkedValue="1"
                  :unCheckedValue="0"
                  checked-children="开"
                  un-checked-children="关"
                  @change="handleSwitchChange(item)"
                />
              </div>
            </div>

            <!-- 灯杆信息 -->
            <a-popover :title="item.deviceName" >
              <div class="box1" style="margin-top: 10px;padding: 5px;">
                <div class="number">
                  <div class="num1">
                    <span>{{ truncateText(item.deviceName, 10) }}</span>
                  </div>
                  <div class="num2">
                    <span style="color: #a9a9a9">品类名：{{ item.categoryName ?? '暂无' }}</span>
                  </div>
                </div>
                <div class="syrategy">
                  <div>所属网关：</div>
                  <span style="font-size: 16px; font-weight: 500">
                    {{ item.gatewayName || '暂无' }}
                  </span>
                </div>
                <div class="energy">
                  <div class="nenghao"><span>能耗:&nbsp;</span></div>
                  <div class="value">
                    <span style="font-size: 24px; font-weight: 700; font-style: italic; color: green">
                      {{ item.data?.powerConsumption?.toFixed(2) || '0.00' }}
                    </span>
                    <span style="margin-left: 6px; font-size: 15px">KWH</span>
                  </div>
                </div>
              </div>
            </a-popover>

            <!-- 亮度进度条 -->
            <div class="box2">
              <a-progress
                type="circle"
                :strokeColor="pointColor(item)"
                :percent="item.data.brightness"
                :size="100"
                :showInfo="true"
              >
                <template #format="percent">
                  <div style="display: flex; flex-direction: column">
                    <span style="font-size: 14px; color: #000000">亮度</span>
                    <span class="PercentageBr" :style="{ color: pointColor(item) }">
                      {{ percent }}%
                    </span>
                  </div>
                </template>
              </a-progress>
            </div>
          </div>
        </a-list-item>
      </template>
    </a-list>
</template>

<script setup>
import { computed, ref } from 'vue';
import { router } from '/@/router';

const props = defineProps({
  lampList: {
    type: Array,
    default: () => [],
  },
});
console.log('props.lampList', props.lampList);


const processedLampList = computed(() => {
  return props.lampList.map(item => ({
    ...item,
    data: {
      ...item.data,
      brightness: item.data.brightness === null ? 0 : item.data.brightness,
      power: item.data.power === null ? 0 : item.data.power,
      switchState: item.data.switchState === null ? 0 : item.data.switchState,
      voltage: item.data.voltage === null ? 0 : item.data.voltage,
      current: item.data.current === null ? '暂无' : item.data.current,
      powerConsumption:item.data.powerConsumption===null?0:item.data.powerConsumption,
    },
    electric: item.electric || { consumption: 0 },
    isSwitchShow: false, 
  }));
});
console.log('processedLampList.value', processedLampList.value);

console.log('processedLampList', processedLampList.value);
processedLampList.value=processedLampList.value[0]
console.log('processedLampList.value', processedLampList.value);




// 计算属性
const tagColor = (item) => {
  return item.onlineStatus === 1 ? '#D8DDFB' : '#FAD8DA'; // 蓝/红
};

const bgColor = (item) => {
  console.log(item);
  
  return item.onlineStatus === 1 
    ? 'linear-gradient(188.4deg, rgba(9, 46, 231, 0.08) 30%, rgba(9, 46, 231, 0) 80%)'
    : 'linear-gradient(188.4deg, rgba(229, 0, 18, 0.03) 30%, rgba(229, 0, 18, 0) 80%)';
};

const statusTitle = (item) => {
  return item.onlineStatus === 1 ? '在线' : '离线';
};

const pointColor = (item) => {
  console.log(item);
  
  return item.onlineStatus === 1 ? '#1890FF' : '#E50012'; // 蓝/红
};

// 截断长文本
const truncateText = (text, maxLength) => {
  return text.length > maxLength ? `${text.slice(0, maxLength)}...` : text;
};

// 开关切换事件
const handleSwitchChange = (item) => {
  console.log(`灯杆 ${item.id} 开关状态: ${item.extra.switchStatus}`);
};

function goLampDetail(data) {
  console.log(data);
  router.push({
    path: '/controlMap-device-detail',
    query: {
      longitude: 113.277,
      latitude: 22.5344,
      id: data.id,
      name: data.name,
    },
  });
}
</script>

<style scoped lang="less">
.cardContent {
  width: 100%;
  height: calc(100% - 50px);
  padding: 10px;
  overflow: hidden; // 去除外层滚动条
}

.ant-list-item {
  margin-bottom: 16px;
}

.ant-list {
  height: 100%;
  overflow-y: auto; // 仅保留 a-list 的竖向滚动条
  overflow-x: hidden; 
}

.entire {
  position: relative;
  width: 100%;
  height: 180px;
  border-radius: 5px;
  box-shadow: 2px 2px 5px 2px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  transition: 0.2s linear;

  &:hover {
    cursor: pointer;
    transform: scale(1.01);
  }

  .card-content-bg1 {
    position: absolute;
    right: -5%;
    height: 100%;
    width: 64.65%;
    top: 0;
    background: linear-gradient(188.4deg, rgba(229, 0, 18, 0.03) 22.94%, rgba(229, 0, 18, 0) 94.62%);
    transform: skew(-15deg);
  }

  .card-workHours {
    position: absolute;
    width: 52%;
    right: -5%;
    height: 23px;
    top: 5px;
    transform: skew(45deg);

    .productStatus {
      margin-left: 6px;
      transform: skew(-45deg);
    }
  }

  .card-state {
    position: absolute;
    top: 30px;
    right: -9px;
    display: flex;
    justify-content: center;
    width: 60px;
    padding: 2px 0;
    transform: skew(45deg);

    .productStatus {
      transform: skew(-45deg);

      .ant-badge-status-dot {
        position: relative;
        top: -1px;
        display: inline-block;
        width: 6px;
        height: 6px;
        vertical-align: middle;
        border-radius: 50%;
      }

      .ant-badge-status-text {
        margin-left: 8px;
        color: #000000d9;
        font-size: 14px;
      }
    }
  }

  .card-switch {
    position: absolute;
    top: 30px;
    right: 5px;
  }

  .box1 {
    flex: 1;
    overflow: hidden;

    .number {
      width: 140px;
      height: 50px;

      .num1 {
        width: 140px;
        height: 30px;
        /* 文本居中 */
        line-height: 30px;
        /* 文本等高 */
        font-size: 12px;
        font-weight: 900;

        .span {
          display: block;
          width: 100% !important;
        }
      }

      .num2 {
        width: 140px;
        height: 20px;
        /* // text-align: center; */
        line-height: 15px;
        font-size: 14px;
      }
    }

    /* 策略 */
    .syrategy {
      height: 70px;
      display: flex;
      text-align: center;
      align-items: center;

      .text {
        float: left;
        width: 70px;

        .text1 {
          height: 40px;
          font-size: 17px;
          /* font-weight: 700; */
          line-height: 40px;
        }

        .text2 {
          height: 30px;
          transition: transform 0.5s ease;
          font-weight: 550;
        }
      }
    }

    /* 这里到kwm都是能耗的 */
    .energy {
      display: flex;
  align-items: center; /* 垂直居中 */


      .nenghao {
        height: 25px;
        line-height: 25px;
        font-size: 14px;

        .value {
          height: 30px;
          line-height: 30px;
        }
      }
    }
  }

  /* 右盒子 */
  .box2 {
    width: 120px;
  flex-shrink: 0;
  float: right;
  margin-top: -100px;
    .mainInfoRight {
      margin-top: 10px;

      .PercentageBr {
        font-size: 22px;
        margin-top: 7px;
        font-weight: 700;
      }
    }
  }
}
</style>