<!--
  * 设备报修单
  *
  * @Author:    yourName
  * @Date:      2025-04-11 11:58:43
  * @Copyright  bdic
-->
<template>
  <a-modal :title="form.id ? '编辑' : '添加'" :width="600" :open="visibleFlag" @cancel="onClose" :maskClosable="false" :destroyOnClose="true">
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 24 }">
      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item label="维修单编号" name="faultNumber">
            <a-input style="width: 50%" v-model:value="form.faultNumber" placeholder="留空则自动填充" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="选择设备" name="deviceId">
            <a-select v-model:value="form.deviceId" placeholder="选择设备" :allowClear="true" @change="handleDeviceIdChange">
              <a-select-option v-for="item in deviceIdOptions" :key="item.value" :value="item.value">
                <div>
                  {{ item.label }}
                  <br />
                  <span style="font-size: 12px; color: #999">{{ item.productName }}</span>
                </div>
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="设备编号" name="deviceId">
            <a-input v-model:value="form.deviceId" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="设备名称" name="deviceName">
            <a-input v-model:value="form.deviceName" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="设备备注名称" name="deviceNoteName" style="width: 100%">
            <a-input v-model:value="deviceInformation.deviceNoteName" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="设备类型" name="deviceType">
            <a-input v-model:value="deviceInformation.deviceType" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="故障类型" name="status">
            <SmartEnumSelect width="100%" v-model:value="form.status" placeholder="故障类型" enum-name="DEVICE_ERROR_STATUS_ENUM" />
          </a-form-item>
        </a-col>

        <a-col :span="24">
          <a-form-item label="故障简述" name="faultRemark">
            <a-textarea v-model:value="form.faultRemark" placeholder="故障简述" />
          </a-form-item>
        </a-col>

        <a-col :span="24">
          <a-form-item label="故障情况照片" name="faultImg">
            <UploadImg :default-file-list="form.faultImg" @change="(fileList) => (form.faultImg = fileList)" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { reactive, ref, nextTick, onMounted } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { maintainFaultApi } from '/@/api/business/maintain/maintain-fault/maintain-fault-api.js';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { iotDeviceApi } from '/@/api/business/iot/device/iot-device-api';
  import SmartEnumSelect from '/@/components/framework/smart-enum-select/index.vue';
  import UploadImg from '/@/components/support/file-upload/index.vue';

  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);
  //设备下拉框数组
  const deviceIdOptions = ref([]);

  // 获取设备下拉框列表
  async function getDeviceIdOptions() {
    try {
      const res = await iotDeviceApi.queryList();
      deviceIdOptions.value = res.data.map((item) => {
        return {
          label: item.deviceName,
          value: item.id,
          productName: item.productName,
          deviceNoteName: item.deviceNoteName,
          deviceType: item.deviceType,
        };
      });
    } catch (err) {
      smartSentry.captureError(err);
    }
  }

  //设备其他信息
  const deviceInformationDefault = {
    deviceNoteName: undefined, //设备备注名称
    deviceType: undefined, //设备类型
  };
  const deviceInformation = reactive({ ...deviceInformationDefault });

  // //处理设备下拉框选择变化
  function handleDeviceIdChange(value) {
    console.log(value);
    const device = deviceIdOptions.value.find((item) => item.value === value);
    console.log(device);
    form.deviceName = device.label;
    deviceInformation.deviceNoteName = device.deviceNoteName;
    deviceInformation.deviceType = device.deviceType;
  }

  function show(rowData) {
    Object.assign(form, formDefault);
    Object.assign(deviceInformation, deviceInformationDefault);
    console.log("1");
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
      handleDeviceIdChange(rowData.deviceId);
    }
    visibleFlag.value = true;
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
    id: undefined, //主键
    faultNumber: undefined, //维修单编号
    deviceId: undefined, //设备id
    deviceName: undefined, //设备名称
    faultRemark: undefined, //故障简述
    status: undefined, //故障类型
    faultImg: undefined, //设备实况照片
  };

  let form = reactive({ ...formDefault });

  const rules = {
    deviceId: [{ required: true, message: '设备id 必填' }],
    deviceName: [{ required: true, message: '设备名称 必填' }],
    faultRemark: [{ required: true, message: '故障简述 必填' }],
    status: [{ required: true, message: '故障类型 必填' }],
  };

  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      if (form.id) {
        await maintainFaultApi.update(form);
      } else {
        await maintainFaultApi.add(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  defineExpose({
    show,
  });
  onMounted(getDeviceIdOptions);
</script>
