<template>
  <a-modal :title="form.id ? '编辑' : '添加'" :width="1200" :open="visibleFlag" @cancel="onClose" :maskClosable="false"
    :destroyOnClose="true">
    <strong style="font-size: 16px; font-weight: bold;">基础信息</strong>
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="投放计划名称" name="mediaCampaignName">
            <a-input v-model:value="form.mediaCampaignName" placeholder="投放计划名称" style="width: 300px;" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="备注" name="remark">
            <a-input v-model:value="form.remark" placeholder="备注" style="width: 300px;" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="投放时间" name="campaignDateRange">
            <a-range-picker :presets="defaultTimeRanges" style="width: 300px; margin-left: 6px"
              v-model:value="campaignTime" format="YYYY-MM-DD" @change="onChangeDate" value-format="YYYY-MM-DD" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="media" tab="素材列表">
        <a-card size="small" :bordered="false" :hoverable="false">
          <div class="smart-margin-bottom10">
            <a-button type="primary" @click="showMaterialModal" class="smart-margin-right10">
              <template #icon>
                <PlusOutlined />
              </template>
              选择素材
            </a-button>
            <a-button @click="handleBatchDelete" type="primary" danger :disabled="selectedRowKeys.length === 0">
              <template #icon>
                <DeleteOutlined />
              </template>
              批量删除
            </a-button>
          </div>

          <a-table size="small" :columns="materialColumns" :data-source="form.materialList" rowKey="mediaId"
            :pagination="false" bordered
            :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }">
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.dataIndex === 'mediaType'">
                {{ MEDIA_TYPE_ENUM.getDesc(record.mediaType) }}
              </template>
              <template v-if="column.dataIndex === 'status'">
                {{ MEDIA_STATUS_ENUM.getDesc(record.status) }}
              </template>
              <template v-if="column.dataIndex === 'playDuration'">
                <a-input-number v-model:value="record.playDuration" :disabled="record.mediaType !== 'IMAGE'" :min="1"
                  style="width: 100%" placeholder="请输入播放时长" />
              </template>
              <template v-if="column.dataIndex === 'playTimes'">
                <a-input-number v-model:value="record.playTimes" :disabled="record.mediaType === 'IMAGE'" :min="1"
                  style="width: 100%" placeholder="请输入播放次数" />
              </template>
              <template v-if="column.dataIndex === 'action'">
                <a-space>
                  <a-button size="small" @click="moveUp(index)" :disabled="index === 0" type="link">上移</a-button>
                  <a-button size="small" @click="moveDown(index)" :disabled="index === form.materialList.length - 1"
                    type="link">下移</a-button>
                  <a-button size="small" danger @click="handleDelete(record.mediaId)" type="link">删除</a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-card>
      </a-tab-pane>
      <a-tab-pane key="device" tab="设备列表">
        <a-card size="small" :bordered="false" :hoverable="false">
          <div class="smart-margin-bottom10">
            <a-button type="primary" @click="showDeviceModal" class="smart-margin-right10">
              <template #icon>
                <PlusOutlined />
              </template>
              选择设备
            </a-button>
            <a-button @click="handleBatchDeleteDevice" type="primary" danger
              :disabled="selectedDeviceKeys.length === 0">
              <template #icon>
                <DeleteOutlined />
              </template>
              批量删除
            </a-button>
          </div>

          <a-table size="small" :columns="deviceColumns" :data-source="form.deviceList" rowKey="deviceId"
            :pagination="false" bordered
            :row-selection="{ selectedRowKeys: selectedDeviceKeys, onChange: onSelectDeviceChange }">
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'deviceType'">
                {{ findDescByValue(record.deviceType, DEVICE_TYPE) }}
              </template>
              <template v-if="column.dataIndex === 'action'">
                <a-button size="small" danger @click="handleDeleteDevice(record.deviceId)" type="link">删除</a-button>
              </template>
            </template>
          </a-table>
        </a-card>
      </a-tab-pane>
      <a-tab-pane key="time" tab="投放时间列表">
        <a-card size="small" :bordered="false" :hoverable="false">
          <div class="smart-margin-bottom10">
            <a-button type="primary" @click="addTimeRange" class="smart-margin-right10">
              <template #icon>
                <PlusOutlined />
              </template>
              添加时间段
            </a-button>
          </div>

          <a-table size="small" :columns="timeColumns" :data-source="form.timeList" :pagination="false" bordered>
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.dataIndex === 'startTime'">
                <a-time-picker v-model:value="record.startTime" :minute-step="10" format="HH:mm:ss"
                  value-format="HH:mm:ss" style="width: 100%" />
              </template>
              <template v-if="column.dataIndex === 'endTime'">
                <a-time-picker v-model:value="record.endTime" :minute-step="10" format="HH:mm:ss"
                  value-format="HH:mm:ss" style="width: 100%" />
              </template>
              <template v-if="column.dataIndex === 'action'">
                <a-button size="small" danger @click="removeTimeRange(index)" type="link">删除</a-button>
              </template>
            </template>
          </a-table>
        </a-card>
      </a-tab-pane>
    </a-tabs>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
    <MediaMaterialModal ref="materialModal" @select="handleSelectMaterial" />
    <DeviceListModal ref="deviceModal" @selectDevices="handleSelectDevices" />
  </a-modal>
</template>

<script setup>
import { reactive, ref, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { mediaCampaignApi } from '/@/api/business/media/campaign/media-campaign-api';
import { smartSentry } from '/@/lib/smart-sentry';
import { defaultTimeRanges } from '/@/lib/default-time-ranges.js';
import MediaMaterialModal from './media-material-modal.vue';
import DeviceListModal from './device-list-modal.vue';
import EmployeeSelect from '/@/components/system/employee-select/index.vue';
import { MEDIA_TYPE_ENUM, MEDIA_STATUS_ENUM } from '/@/constants/business/media/material/media-material-const';
import { DEVICE_TYPE } from '/@/constants/business/iot/device/iot-device-const.js';
import dayjs from 'dayjs';

const emits = defineEmits(['reloadList']);
const visibleFlag = ref(false);
const formRef = ref();
const materialModal = ref();
const deviceModal = ref();
const selectedRowKeys = ref([]);
const selectedDeviceKeys = ref([]);
const activeKey = ref('media')

const formDefault = {
  id: undefined,
  mediaCampaignName: undefined,
  remark: undefined,
  startTime: undefined,
  endTime: undefined,
  materialList: [],
  deviceList:[],
  timeList:[]
};

const form = reactive({ ...formDefault });

const rules = {
  mediaCampaignName: [{ required: true, message: '投放计划名称 必填' }],
};

const materialColumns = [
  { title: '序号', dataIndex: 'playOrder', width: 80, align: 'center' },
  { title: '素材名称', dataIndex: 'mediaName', ellipsis: true },
  { title: '素材类型', dataIndex: 'mediaType', ellipsis: true },
  { title: '播放时长(秒)', dataIndex: 'playDuration', width: 160 },
  { title: '播放次数', dataIndex: 'playTimes', width: 160 },
  { title: '创建时间', dataIndex: 'createTime', ellipsis: true ,width:160},
  { title: '操作', dataIndex: 'action', width: 200 }
];

const deviceColumns = [
  { title: '序号', dataIndex: 'index', width: 80, align: 'center', customRender: ({ index }) => index + 1 },
  { title: '设备名称', dataIndex: 'deviceName', ellipsis: true },
  { title: '备注名称', dataIndex: 'deviceNoteName', ellipsis: true },
  { title: '唯一编号', dataIndex: 'deviceUniqueNo', ellipsis: true },
  { title: '所属产品', dataIndex: 'deviceProductName', ellipsis: true },
  { title: '操作', dataIndex: 'action', width: 100 }
];

const timeColumns = [
  { title: '序号', dataIndex: 'index', width: 80, align: 'center', customRender: ({ index }) => index + 1 },
  { title: '开始时间', dataIndex: 'startTime', width: 200 },
  { title: '结束时间', dataIndex: 'endTime', width: 200 },
  { title: '操作', dataIndex: 'action', width: 100 }
];

// 重置表单
function resetForm() {
  Object.assign(form, formDefault);
  selectedRowKeys.value = [];
  selectedDeviceKeys.value = [];
}

const campaignTime = ref([])

function onChangeDate(dates, dateString) {
  if (dates) {
    form.startTime = dateString[0];
    form.endTime = dateString[1];
  } else {
    form.startTime = undefined;
    form.endTime = undefined;
  }
}

async function show(rowData) {
  resetForm();
  if (rowData) {
    const res = await mediaCampaignApi.getById(rowData.id);
    console.log(res.data)
    Object.assign(form, res.data);
    form.materialList = res.data?.mediaMaterialList || [];
    form.deviceList = res.data?.mediaDeviceList || [];
    form.timeList = res.data?.timeList || [];
    // 设置日期范围
    campaignTime.value = [res.data.startTime, res.data.endTime];
    updatePlayOrder();
  }
  visibleFlag.value = true;
  nextTick(() => formRef.value?.clearValidate());
}

function onClose() {
  resetForm();
  visibleFlag.value = false;
}

async function onSubmit() {
  try {
    await formRef.value.validateFields();
    await save();
  } catch {
    message.error('参数验证错误，请仔细填写表单数据!');
  }
}

async function save() {
  SmartLoading.show();
  try {
    const apiCall = form.id ? mediaCampaignApi.update : mediaCampaignApi.add;
    await apiCall(form);
    message.success('操作成功');
    emits('reloadList');
    onClose();
  } catch (err) {
    smartSentry.captureError(err);
  } finally {
    SmartLoading.hide();
  }
}

// 处理素材选择
function handleSelectMaterial(materials) {
  form.materialList = materials.map((material, index) => ({
    mediaId: material.id,
    mediaName: material.name,
    mediaType: material.type,
    mediaFileKey: material.fileKey,
    playDuration: undefined,
    playTimes: undefined,
    playOrder: index + 1,
    duration: material.duration || 5,
    fileFormat: material.fileFormat || '',
    status: material.status,
    createUserId: material.createUserId,
    createTime: material.createTime
  }));
  
  if (materials.length > 0) {
    message.success(`成功添加 ${materials.length} 个素材`);
  }
}

// 处理设备选择
function handleSelectDevices(devices) {
  form.deviceList = devices.map((device, index) => ({
    deviceId: device.id,
    deviceName: device.deviceName,
    deviceNoteName: device.deviceNoteName,
    deviceUniqueNo: device.uniqueNo,
    deviceProductName: device.productName,
    deviceRegionId: device.regionId,
    deviceRegionName: device.regionName,
  }));
  if (devices.length > 0) {
    message.success(`成功添加 ${devices.length} 个设备`);
  }
}

// 更新播放顺序
function updatePlayOrder() {
  form.materialList.forEach((item, index) => {
    item.playOrder = index + 1;
  });
}

// 删除素材
function handleDelete(mediaId) {
  form.materialList = form.materialList.filter(item => item.mediaId !== mediaId);
  selectedRowKeys.value = selectedRowKeys.value.filter(key => key !== mediaId);
  updatePlayOrder();
  message.success('删除成功');
}

// 批量删除素材
function handleBatchDelete() {
  form.materialList = form.materialList.filter(item => !selectedRowKeys.value.includes(item.mediaId));
  selectedRowKeys.value = [];
  updatePlayOrder();
  message.success('批量删除成功');
}

// 删除设备
function handleDeleteDevice(deviceId) {
  form.deviceList = form.deviceList.filter(item => item.deviceId !== deviceId);
  selectedDeviceKeys.value = selectedDeviceKeys.value.filter(key => key !== deviceId);
  message.success('删除成功');
}

// 批量删除设备
function handleBatchDeleteDevice() {
  form.deviceList = form.deviceList.filter(item => !selectedDeviceKeys.value.includes(item.deviceId));
  selectedDeviceKeys.value = [];
  message.success('批量删除成功');
}

// 上移素材
function moveUp(index) {
  if (index > 0) {
    [form.materialList[index], form.materialList[index - 1]] = 
    [form.materialList[index - 1], form.materialList[index]];
    updatePlayOrder();
  }
}

// 下移素材
function moveDown(index) {
  if (index < form.materialList.length - 1) {
    [form.materialList[index], form.materialList[index + 1]] = 
    [form.materialList[index + 1], form.materialList[index]];
    updatePlayOrder();
  }
}

function onSelectChange(selectedKeys) {
  selectedRowKeys.value = selectedKeys;
}

function onSelectDeviceChange(selectedKeys) {
  selectedDeviceKeys.value = selectedKeys;
}

function showMaterialModal() {
  materialModal.value.show();
}

function showDeviceModal() {
  deviceModal.value.show();
}

// 返回枚举描述
function findDescByValue(value, enumObject) {
  for (const key in enumObject) {
    if (enumObject[key].value === value) {
      return enumObject[key].desc;
    }
  }
  return null;
}

function addTimeRange() {
  form.timeList.push({
    startTime: "",
    endTime: ""
  });
}

function removeTimeRange(index) {
  form.timeList.splice(index, 1);
}

defineExpose({ show });
</script>
