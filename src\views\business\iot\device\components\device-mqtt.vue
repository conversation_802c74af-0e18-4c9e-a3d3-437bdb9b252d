<template>
  <div class="mqtt-info">
    <a-alert
      message="注意：这里仅仅为临时测试所用，密钥有效期为5分钟，真实设备连接参数请按照文档生成。"
      type="info"
      show-icon
      class="mb-20"
    />
    <div class="action-bar">
      <a-button type="primary" @click="getMqttInfo">
        <template #icon><ReloadOutlined /></template>
        更新
      </a-button>
      <a-button type="primary" @click="copyAllData">
        <template #icon><CopyOutlined /></template>
        一键复制
      </a-button>
    </div>
    <a-card>
      <pre class="code-block">{{ formattedData }}</pre>
    </a-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { ReloadOutlined, CopyOutlined } from '@ant-design/icons-vue';
import { iotDeviceApi } from '/@/api/business/iot/device/iot-device-api.js';
import { SmartLoading } from '/@/components/framework/smart-loading';

// 接收父组件传递的路由数据对象
const props = defineProps({
  routeData: Object
});

const mqttInfo = ref({});

const formattedData = computed(() => JSON.stringify(mqttInfo.value, null, 2));

const getMqttInfo = async () => {
  SmartLoading.show();
  try {
    console.log(props.routeData);
    const { data } = await iotDeviceApi.getMqttLinks(props.routeData.id);
    mqttInfo.value = data || {};
    if (data) message.success('更新成功');
  } catch (error) {
    message.error('获取MQTT连接信息失败');
  } finally {
    SmartLoading.hide();
  }
};

const copyAllData = () => {
  try {
    navigator.clipboard.writeText(formattedData.value);
    message.success('复制成功');
  } catch {
    const textarea = document.createElement('textarea');
    textarea.value = formattedData.value;
    document.body.appendChild(textarea);
    textarea.select();
    document.execCommand('copy');
    document.body.removeChild(textarea);
    message.success('复制成功');
  }
};

onMounted(getMqttInfo);
</script>

<style scoped>
.mqtt-info {
  margin: 20px;
}
.mb-20 {
  margin-bottom: 20px;
}
.action-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}
.code-block {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
}
</style>

