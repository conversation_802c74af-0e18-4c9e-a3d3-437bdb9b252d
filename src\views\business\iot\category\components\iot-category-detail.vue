<template>
  <a-card size="small" :bordered="false" :hoverable="true">
    <!-- 信息行 -->
    <a-space style="margin-bottom: 10px">
      <a-button type="none" @click="back"> <left-outlined /> 返回 </a-button>
      <a-divider type="vertical" style="border-color: #dcdfe6" />
      <span class="title">品类信息=>{{ categoryDetail.categoryName }}</span>
    </a-space>
    <a-divider style="margin: 5px 0 10px 0" />
    <a-descriptions :column="1" :bordered="false" size="middle" style="padding-left: 30px">
      <a-descriptions-item label="品类名称">{{ categoryDetail.categoryNameFull }}</a-descriptions-item>
      <a-descriptions-item label="发布状态">{{ getCategoryStatus(categoryDetail.status) }}</a-descriptions-item>
      <a-descriptions-item label="品类说明">{{ categoryDetail.categoryDesc }}</a-descriptions-item>
    </a-descriptions>

    <!-- 标签页 -->
    <a-tabs v-model:activeKey="activeKey" type="card" style="margin-top: 15px">
      <a-tab-pane key="categoryInfo" tab="品类信息">
        <iot-category-info ref="categoryInfo" :category-id="categoryId" :category-data="categoryDetail" />
      </a-tab-pane>
      <a-tab-pane key="categoryFunc" tab="功能定义">
        <iot-category-function ref="categoryFunc" :category-id="categoryId" />
      </a-tab-pane>
    </a-tabs>
  </a-card>
</template>

<script setup>
  import { useRouter, useRoute } from 'vue-router';
  import { useUserStore } from '/@/store/modules/system/user';
  import { ref, onMounted } from 'vue';
  import { iotCategoryApi } from '/@/api/business/iot/category/iot-category-api.js';
  import IotCategoryInfo from './iot-category-info.vue';
  import iotCategoryFunction from './iot-category-function.vue';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { PUBLISH_STATUS_ENUM } from '/@/constants/business/iot/category/iot-category-const';
  import { useCategoryStore } from '/@/store/modules/business/iot/category';

  const activeKey = ref('categoryInfo');
  const categoryDetail = ref({});
  const categoryId = ref('');

  //---------------------------------页面返回-------------------------------------
  const route = useRoute();
  const router = useRouter();
  const categoryStore = useCategoryStore();
  const back = () => {
    router.push({
      path: '/iot/category/list', //返回品类管理页
    });

    //关闭当前标签页
    useUserStore().closeTagNav(route.name, false);
  };

  // 初始化 categoryId
  const initCategoryId = () => {
    const id = route.query.categoryId || categoryStore.getCategoryId();
    if (!id) {
      message.error('未获取到品类ID');
      router.push('/iot/category/list');
      return;
    }

    categoryId.value = id;
    categoryStore.setCategoryId(id);
    getDetail();
  };

  //---------------------------------获取详情数据-------------------------------------
  const getDetail = async () => {
    try {
      SmartLoading.show();
      const param = { id: categoryStore.getCategoryId() };
      const response = await iotCategoryApi.getDetail(param);
      categoryDetail.value = response.data;
    } catch (error) {
      message.error('获取品类详情失败，请稍后重试');
    } finally {
      SmartLoading.hide();
    }
  };
  // 获取发布状态描述
  const getCategoryStatus = (status) => {
    return status === PUBLISH_STATUS_ENUM.published.value ? PUBLISH_STATUS_ENUM.published.desc : PUBLISH_STATUS_ENUM.unpublished.desc;
  };
  // 组件引用
  const categoryInfo = ref(null);
  const categoryFunc = ref(null);

  // 组件挂载时加载数据
  onMounted(() => {
    initCategoryId();
  });
</script>

<style scoped>
  .title {
    color: #606266;
    font-weight: bold;
    font-size: 18px;
  }

  .category-name {
    color: #409eff;
    font-size: 18px;
  }
</style>
