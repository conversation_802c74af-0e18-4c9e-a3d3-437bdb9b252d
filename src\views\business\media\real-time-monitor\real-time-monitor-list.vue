<!--
  * 实时监控
  *
  * @Author:    潘显镇
  * @Date:      2025-06-01 
  * @Copyright  2025 zscbdic
-->
<template>
  <div>
    <a-row :gutter="5">
      <!-- 左侧区域树 -->
      <a-col :span="4">
        <RegionTree @select="onSelectRegion" ref="regionTreeRef" />
      </a-col>

      <!-- 右侧监控列表 -->
      <a-col :span="20">
        <!-- 查询表单 -->
        <a-form class="smart-query-form" style="border-radius: 5px">
          <a-row class="smart-query-form-row">
            <a-form-item label="设备名称" class="smart-query-form-item">
              <a-input style="width: 150px" v-model:value="queryForm.deviceName" placeholder="设备名称" />
            </a-form-item>
            <a-form-item label="备注名称" class="smart-query-form-item">
              <a-input style="width: 150px" v-model:value="queryForm.deviceNoteName" placeholder="备注名称" />
            </a-form-item>
            <a-form-item label="唯一编号" class="smart-query-form-item">
              <a-input style="width: 150px" v-model:value="queryForm.uniqueNo" placeholder="唯一编号" />
            </a-form-item>
            <a-form-item label="产品名称" class="smart-query-form-item">
              <a-input style="width: 150px" v-model:value="queryForm.productName" placeholder="产品名称" />
            </a-form-item>
            <a-form-item label="设备类型" class="smart-query-form-item">
              <SmartEnumSelect width="120px" v-model:value="queryForm.deviceType" placeholder="请选择设备类型"
                enum-name="DEVICE_TYPE_ENUM" />
            </a-form-item>
            <a-form-item class="smart-query-form-item">
              <a-button type="primary" @click="onSearch">
                <template #icon>
                  <SearchOutlined />
                </template>
                查询
              </a-button>
              <a-button @click="resetQuery" class="smart-margin-left10">
                <template #icon>
                  <ReloadOutlined />
                </template>
                重置
              </a-button>
            </a-form-item>
          </a-row>
        </a-form>

        <!-- 监控列表卡片 -->
        <a-card :body-style='{padding: "5px 5px"}'>
          <div style="display: flex;justify-content: space-between">
            <a-typography-title :level="5">
              实时监控
              <span v-if="selectedRegionName && selectedRegionName !== '全部'" class="selected-region">
                - {{ selectedRegionName }}
              </span>
            </a-typography-title>
          </div>

          <a-list :grid="{ gutter: 1, xs: 1, sm: 1, md: 2, lg: 2, xl: 3, xxl: 3 }" :data-source="moniterList"
            :loading="loading">
            <template #renderItem="{ item, index }">
              <a-list-item :style="{ padding: '0px 5px' }">
                <a-card :loading="loading" size="" class="device-card"
                  :style="'background: linear-gradient(rgba(88, 158, 255, 0.1), white)'">
                  <template #title>
                    <a class="detail">{{ item.deviceName }}</a>
                  </template>
                  <!--
                    muted默认静音播放
                    loop默认循环播放
                    controls默认显示播放器控制条
                    autoplay默认自动播放
                    -->
                  <div class="media-container">
                    <video v-if="item.materialsType === 'VIDEO'" :src="item.materialsFileKey" class="media-player"
                      :autoplay="true" :muted="true" :loop="true" :controls="true"></video>
                    <a-image v-else :src="item.materialsFileKey" class="media-player" />
                  </div>

                  <div class="card-content">
                    <span class="span-multiline-ellipsis">备注名称：{{ item.deviceNoteName }}</span>
                    <span class="span-multiline-ellipsis">所属区域：{{ item.deviceRegionName }}</span>
                    <span class="span-multiline-ellipsis">素材名称：{{ item.materialsName }}</span>
                    <span class="span-multiline-ellipsis">
                      素材类型：
                      <span class="media-type-tag video-tag">视频</span>
                    </span>
                    <span class="span-multiline-ellipsis">播放进度：</span>
                    <div class="steps-wrapper">
                      <a-steps :current="item.playStep?.currentIndex"
                        :items="item.playStep?.materialsName.map(name => ({ title: name }))" size="small"
                        label-placement="vertical" class="steps-container"></a-steps>
                    </div>
                  </div>
                </a-card>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, reactive } from 'vue';
import { message } from 'ant-design-vue';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { realTimeMonitorApi } from '/@/api/business/media/real-time-monitor/real-time-monitor-api';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons-vue';
import { DEVICE_TYPE } from '/@/constants/business/iot/device/iot-device-const.js';
import RegionTree from './components/region-tree.vue';
import SmartEnumSelect from '/@/components/framework/smart-enum-select/index.vue';
const moniterList = ref([]);
const loading = ref(false);
const regionTreeRef = ref(null);
const selectedRegionName = ref('');


const formState = {
  regionId: undefined,
  deviceName: undefined,
  deviceNoteName: undefined,
  uniqueNo: undefined,
  productName: undefined,
  deviceType: undefined
}
// 查询表单
const queryForm = reactive({...formState});

// 选择区域回调
const onSelectRegion = (region) => {
  selectedRegionName.value = region.name;
  queryForm.regionId = region.id || '';
  getMoniterList();
};

// 查询按钮点击事件
const onSearch = () => {
  getMoniterList();
};

// 重置查询条件
const resetQuery = () => {
  queryForm.deviceName = undefined;
  queryForm.deviceNoteName = undefined;
  queryForm.uniqueNo = undefined;
  queryForm.productName = undefined;
  queryForm.deviceType = undefined;
  getMoniterList();
};

// 获取实时监控数据
const getMoniterList = async () => {
  try {
    loading.value = true;
    SmartLoading.show();
    // 添加查询参数
    const res = await realTimeMonitorApi.realTimeMonitorList(queryForm);
    if (res.data===1) {
      moniterList.value = res.data;
    } else {
      // 如果没有数据，使用模拟数据进行测试
      moniterList.value = getMockData();
    }
  } catch (error) {
    console.error('获取实时监控数据失败', error);
    message.error('获取实时监控数据失败');
    // 发生错误时使用模拟数据
    moniterList.value = getMockData();
  } finally {
    loading.value = false;
    SmartLoading.hide();
  }
};

// 生成模拟数据
const getMockData = () => {
  return [
    {
      deviceId: '001',
      deviceName: '监控设备001',
      deviceNoteName: '前门监控',
      deviceRegionName: '一楼大厅',
      materialsName: '前门监控视频',
      materialsType: 'VIDEO',
      materialsFileKey: 'https://media.w3.org/2010/05/sintel/trailer.mp4',
      playStep: {
        currentIndex: 1,
        materialsName: ['开场', '主画面', '结尾']
      }
    },
    {
      deviceId: '002',
      deviceName: '监控设备002',
      deviceNoteName: '后门监控',
      deviceRegionName: '二楼走廊',
      materialsName: '后门监控视频',
      materialsType: 'VIDEO',
      materialsFileKey: 'https://media.w3.org/2010/05/sintel/trailer.mp4',
      playStep: {
        currentIndex: 2,
        materialsName: ['序列1', '序列2', '序列3', '序列4', '序列5', '序列6', '序列7', '序列8', '序列9', '序列10']
      }
    },
    {
      deviceId: '003',
      deviceName: '监控设备003',
      deviceNoteName: '侧门监控',
      deviceRegionName: '三楼会议室',
      materialsName: '侧门监控图片',
      materialsType: 'IMAGE',
      materialsFileKey: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
      playStep: {
        currentIndex: 0,
        materialsName: ['图片1', '图片2', '图片3', '图片4']
      }
    }
  ];
};

onMounted(() => {
  getMoniterList();
});

// 组件卸载前停止所有视频播放
onBeforeUnmount(() => {
  const videos = document.querySelectorAll('video');
  videos.forEach(video => {
    video.pause();
  });
});
</script>

<style lang="less" scoped>
.selected-region {
  font-size: 14px;
  color: #666;
  margin-left: 8px;
}

.device-card {
  height: 100%;
  transition: all 0.3s;
}

.detail {
  display: inline-block;
  padding: 5px 10px;
  background-color: rgba(88, 158, 255, 0.1);
  border: 1px solid rgba(88, 158, 255, 0.1);
  border-radius: 8px;
  color: #2c77f1;
  font-size: 16px;
  text-align: center;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.media-container {
  height: 220px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #000;
  overflow: hidden;
  margin: 10px 0;
  border-radius: 8px;

  .media-player {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

}

.card-content {
  margin-top: 12px;
}

.span-multiline-ellipsis {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  max-width: 100%;
  line-height: 1.8;
  max-height: calc(1.8em * 1);
  word-break: break-word;
  font-size: 14px;
  margin-bottom: 8px;
}

.media-type-tag {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.5;
}

.video-tag {
  background-color: #E8F5E9;
  color: #67C23A;
}

.image-tag {
  background-color: #E3F2FD;
  color: #2196F3;
}

.unknown-tag {
  background-color: #ECEFF1;
  color: #909399;
}

.steps-wrapper {
  width: 100%;
  overflow-x: auto;
  margin-bottom: 8px;
}

.steps-container {
  min-width: max-content;
  padding: 0 10px;
}
</style>
