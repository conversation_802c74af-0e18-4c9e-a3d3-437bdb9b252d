<!--
  * 边缘网关表
  *
  * @Author:    文伊仪
  * @Date:      2025-03-27 21:21:21
  * @Copyright  2025 电子科技大学中山学院大数据与智能计算实验室
-->
<template>
  <a-modal :title="form.id ? '编辑' : '新增'" :width="700" :open="visibleFlag" @cancel="onClose" :maskClosable="false"
    :destroyOnClose="true">
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 3 }">
      <a-form-item label="标题" name="label">
        <a-input style="width: 100%" v-model:value="form.label" placeholder="请输入标题" />
      </a-form-item>
      <a-form-item label="类型" name="type">
        <a-input style="width: 100%" v-model:value="form.type" placeholder="请输入类型" />
      </a-form-item>
      <a-form-item label="图标" name="logo">
        <Upload  :defaultFileList="form.logo ? [{ fileUrl: form.logo }] : []" @change="fileChange" :maxUploadSize="1" />
      </a-form-item>
      <a-form-item label="排序号" name="sort">
        <a-input-number style="width: 20%" min="1" v-model:value="form.sort" placeholder="排序号" />
      </a-form-item>
      <a-form-item label="说明" name="explain">
        <SmartWangeditor ref="contentRef" :modelValue="form.contentHtml" :height="300" />
      </a-form-item>
<!--      <a-form-item label="部署时间" name="deployTime">-->
<!--        <a-date-picker v-model:value="form.deployTime" placeholder="选择部署时间" />-->
<!--      </a-form-item>-->
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
import { reactive, ref, nextTick } from 'vue';
import _ from 'lodash';
import { message } from 'ant-design-vue';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { iotGatewayApi } from '/@/api/business/iot/edge/iot-edge-api.js';
import { smartSentry } from '/@/lib/smart-sentry';
import SmartWangeditor from '/@/components/framework/wangeditor/index.vue';
 import Upload from '/@/components/support/file-upload/index.vue';
import dayjs from 'dayjs';
// ------------------------ 事件 ------------------------

const emits = defineEmits(['reloadList']);

// ------------------------ 显示与隐藏 ------------------------
// 是否显示
const visibleFlag = ref(false);

function show(rowData) {
  console.log("rowData", rowData)
  Object.assign(form, formDefault);
  if (rowData && !_.isEmpty(rowData)) {
    Object.assign(form, rowData);
  }else {
    // 新建时设置默认部署时间为当前时间
    form.deployTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
  }

  visibleFlag.value = true;
  nextTick(() => {
    formRef.value.clearValidate();
  });
}

function onClose() {
  Object.assign(form, formDefault);
  visibleFlag.value = false;
}

// ------------------------ 表单 ------------------------

// 组件ref
const formRef = ref();

const formDefault = {
  label: undefined, //标题
  explain: undefined, //说明
  logo: undefined, //图标
  sort: undefined, //排序号
  id: undefined, //id
  type: undefined,
  source: "gateway",
  deployTime:undefined,
  disabled: "false",
  flowData: undefined,
};

let form = reactive({ ...formDefault });

const rules = {
  label: [{ required: true, message: '标题 必填' }],
  sort: [{ required: true, message: '排序号 必填' }],
  type:[{ required: true, message: '类型 必填' }],
};

// 点击确定，验证表单
async function onSubmit() {

  try {
    await formRef.value.validateFields();
    if (!form.deployTime) {
      form.deployTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
    }
    save();
  } catch (err) {
    message.error('参数验证错误，请仔细填写表单数据!');
  }
}

// 新建、编辑API
async function save() {
  SmartLoading.show();
  console.log("form.type", form.type);
  try {
    // console.log("form.id".form.id)
    if (form.id) {
      console.log("编辑")
      await iotGatewayApi.update(form);
    } else {
      console.log("新建")
      await iotGatewayApi.save(form);
    }
    message.success('操作成功');
    emits('reloadList');
    onClose();
  } catch (err) {
    smartSentry.captureError(err);
    message.error('保存失败，请重试！');
  } finally {
    SmartLoading.hide();
  }
}

function fileChange(file) {
   form.logo = file[0]?.fileUrl || "";
 }

defineExpose({
  show,
});
</script>
