
user  nginx;
worker_processes  auto;

error_log  /var/log/nginx/error.log notice;
pid        /var/run/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;

    #gzip  on;

    include /etc/nginx/conf.d/*.conf;

    server {
        listen       80;
        server_name  pioneer.zscbdic.cn:8081;

        location / {
          root   /usr/share/nginx/html/;
          index  index.html index.htm;
        }
        # 缓存文件，提高性能
        location ~* \.(jpg|jpeg|png|gif)$ {
          expires 30d;
        }
        location ~* \.(css|js)$ {
          expires 600;
          add_header Cache-control max-age=800;
        }
        # 通过配置网站服务器进行输出压缩，可以减少http响应传输的数据量，从而提高网站页面的加载速度。
        location ~ .*\.(jpg|gif|png|js)$ {
          gzip on;
          gzip_http_version 1.1;
          gzip_comp_level 6; # 压缩级别，默认1，最高9
          gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif img/png;
        }
        # 后端配置
        location /pioneer-mes {
          #反向代理的java地址
          proxy_pass https://api.pioneer.zscbdic.cn/pioneer-mes;
          # 假设你的后端服务运行在本地的8080端口
          # 传递客户端IP和协议
          proxy_set_header  Host             $host;
          proxy_set_header  X-Real-IP        $remote_addr;
          set $my_proxy_add_x_forwarded_for $proxy_add_x_forwarded_for;
          if ($proxy_add_x_forwarded_for ~* "127.0.0.1"){
            set $my_proxy_add_x_forwarded_for $remote_addr;
          }
          proxy_set_header   X-Forwarded-For $my_proxy_add_x_forwarded_for;   
          #此处是https访问的关键环节
          proxy_redirect off;
          #设置没有缓存[此处很重要，必须设置，不然有些浏览器对get请求会缓存，引发不必要的bug]
          expires -1;
          #一些安全配置
          add_header Set-Cookie "Path=/; HttpOnly; Secure";
          add_header X-Content-Type-Options "nosniff";
          add_header X-XSS-Protection "1; mode=block";
          #设置跨域方法
          # add_header Access-Control-Allow-Origin *;
          add_header X-Frame-Options "ALLOW-FROM *";
          add_header Content-Security-Policy "frame-ancestors *";
        }


    }

}
