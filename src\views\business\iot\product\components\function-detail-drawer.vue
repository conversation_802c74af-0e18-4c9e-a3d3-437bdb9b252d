<template>
  <a-drawer
    :open="isOpen"
    title="查看"
    width="500px"
    :closable="true"
    placement="right"
    @close="onClose"
  >
    <a-form :model="recordData"  :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }"  >
      <a-form-item label="功能类型">
        <a-radio-group v-model:value="recordData.functionType" button-style="solid" disabled>
          <a-radio :value="1">属性</a-radio>
          <a-radio :value="2">命令</a-radio>
          <a-radio :value="3">事件</a-radio>
        </a-radio-group>
      </a-form-item>
      
      <a-form-item label="功能名称">
        <a-input v-model:value="recordData.name" style="width:250px" disabled/>
      </a-form-item>
      
      <a-form-item label="标识符">
        <a-input v-model:value="recordData.identifier" style="width:250px" disabled/>
      </a-form-item> 
      
      <!-- 属性特有字段 -->
      <template v-if="recordData.functionType === 1">
        <a-form-item label="数据类型">
          <a-input v-model:value="recordData.specFunction.fieldType" disabled />
        </a-form-item>
        
        <template v-if="isNumberType(recordData.specFunction.fieldType)">
          <a-form-item label="最小值">
            <a-input v-model:value="recordData.specFunction.specMin" disabled/>
          </a-form-item>
          
          <a-form-item label="最大值">
            <a-input v-model:value="recordData.specFunction.specMax" disabled/>
          </a-form-item>
          
          <a-form-item label="步长">
            <a-input v-model:value="recordData.specFunction.specStep" disabled />
          </a-form-item>
          
          <a-form-item label="单位">
            <a-input v-model:value="recordData.specFunction.specUnitName" disabled />
          </a-form-item>
        </template>
        
        <template v-if="recordData.specFunction.fieldType === 'bool'">
          <a-form-item label="布尔值0">
            <a-input v-model:value="recordData.specFunction.specBoolFalse" disabled />
          </a-form-item>
          
          <a-form-item label="布尔值1">
            <a-input v-model:value="recordData.specFunction.specBoolTrue" disabled />
          </a-form-item>
        </template>
        
        <a-form-item label="访问权限">
          <a-radio-group v-model:value="recordData.accessMode" disabled>
            <a-radio value="r">只读</a-radio>
            <a-radio value="rw">读写</a-radio>
          </a-radio-group>
        </a-form-item>
      </template>
      
      <!-- 命令特有字段 -->
      <template v-if="recordData.functionType === 2">
        <a-form-item label="调用方式">
          <a-radio-group v-model:value="recordData.callType" disabled>
            <a-radio value="sync">同步</a-radio>
            <a-radio value="async">异步</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="输入参数">
          <a-table :columns="columns" :data-source="recordData.specInput" :pagination="false":row-key="(record) => record.id" bordered>
             <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'action'">
                    <a-button type="link" @click="showDataModel(record)">
                        <eye-outlined />
                        查看
                    </a-button>
                </template>
             </template>   
          </a-table>
        </a-form-item>
        <a-form-item label="输出参数">
          <a-table :columns="columns" :data-source="recordData.specOutput" :pagination="false":row-key="(record) => record.id" bordered>
             <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'action'">
                    <a-button type="link" @click="showDataModel(record)">
                        <eye-outlined />
                        查看
                    </a-button>
                </template>
             </template>   
          </a-table>
        </a-form-item>
      </template>
      
      <!-- 事件特有字段 -->
      <template v-if="recordData.functionType === 3">
        <a-form-item label="事件类型">
          <a-radio-group v-model:value="recordData.eventType" disabled>
            <a-radio value="info">信息</a-radio>
            <a-radio value="alert">告警</a-radio>
            <a-radio value="error">故障</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="输出参数">
           <a-table :columns="columns" :data-source="recordData.specOutput" :pagination="false":row-key="(record) => record.id" bordered>
             <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'action'">
                    <a-button type="link" @click="showDataModel(record)">
                        <eye-outlined />
                        查看
                    </a-button>
                </template>
             </template>   
           </a-table>
        </a-form-item>
      </template>
      
      <a-form-item label="是否必选">
        <a-radio-group v-model:value="recordData.required" disabled>
          <a-radio :value="'1'">必选</a-radio>
          <a-radio :value="'0'">可选</a-radio>
        </a-radio-group>
      </a-form-item>
      
      <a-form-item label="描述">
        <a-textarea v-model:value="recordData.functionDesc" :rows="4" disabled/>
      </a-form-item>
    </a-form>
  </a-drawer> 
  <functionDetailParamModel ref="showDataModelRef"/>
</template>

<script setup>
import { ref,reactive } from 'vue';
import functionDetailParamModel from './function-detail-param-model.vue'
// 记录数据
const recordData = reactive({});
const isOpen = ref(false);

// 判断是否是数值类型
const isNumberType = (fieldType) => {
  return ['int32', 'float', 'double'].includes(fieldType);
};

// 关闭抽屉
const onClose = () => {
  isOpen.value = false;
  Object.assign(recordData, {});
};

const columns = [
   {
        title: '参数名称',
        dataIndex: 'name',
    },
    {
        title: '标识符',
        dataIndex: 'identifier',
    },
    {
        title: '数据类型',
        dataIndex:'fieldType',
    },
    {
        title: '操作',
        dataIndex: 'action',
    }
]

// 显示抽屉
const show = (record) => {
  Object.assign(recordData, record);
  console.log(recordData);
  isOpen.value = true;
};

const showDataModelRef = ref(null)
function showDataModel(data){
showDataModelRef.value.show(data);
}

// 暴露方法给父组件
defineExpose({
  show
});
</script> 