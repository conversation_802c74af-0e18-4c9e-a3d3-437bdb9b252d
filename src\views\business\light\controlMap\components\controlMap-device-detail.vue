<!--
  * 控制地图-设备详情看板
  *
  * @Author:    骆伟林
  * @Date:      2025-03-26 17:51:27
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
  <div class="total">
    <div id="container" ref="mapContainer"></div>
    <div class="topReturn"><a-button style="margin: 5px" @click="onClose">返回</a-button></div>
    <div class="lampDetailsLeft">
      <div class="view">
        <video id="videoPlayer" :src="lampData.videoUrl" controls autoplay preload="auto" muted loop width="100%" height="100%"></video>
        <div class="lampDetai">
          <a-divider class="divider"><span>设备概况</span></a-divider>
          <a-descriptions bordered :column="1">
            <a-descriptions-item :span="1" label="设备名称">
              {{ lampData.deviceNoteName }}
            </a-descriptions-item>
            <a-descriptions-item :span="1" label="备注名称">
              {{ lampData.deviceNoteName }}
            </a-descriptions-item>
            <a-descriptions-item :span="1" label="所在区域">
              {{ lampData.regionName ? lampData.regionName : '暂无区域' }}
            </a-descriptions-item>
            <a-descriptions-item :span="1" label="设备状态">
              {{ lampData.deviceStatus === 0 ? '未激活' : lampData.deviceStatus === 1 ? '已激活' : lampData.deviceStatus === 2 ? '离线' : lampData.deviceStatus === 3 ? '在线' : lampData.deviceStatus === 4 ? '禁用' : '未知状态' }}
            </a-descriptions-item>
            <a-descriptions-item :span="1" label="激活时间">
              {{ lampData.activeTime === null ? '未激活' : lampData.activeTime }}
            </a-descriptions-item>
            <a-descriptions-item :span="1" label="创建时间">
              {{ lampData.createTime }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
      </div>
    </div>
    <LampDetailsRight :lampData="lampData" />
    <LampChart class="lampChart" :lampData="lampData" />
  </div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue';
  import AMapLoader from '@amap/amap-jsapi-loader';
  import { useRoute } from 'vue-router';
  import { router } from '/@/router';
  import streetLampIcon from '/@/assets/images/light/icons8-路灯-64.png';
  import LampChart from './controlMap-device-lampChart.vue';
  import LampDetailsRight from './controlMap-device-lamp.vue';
  import { useUserStore } from '/@/store/modules/system/user';
  import { lightApi } from '/@/api/business/light/light';

  const userStore = useUserStore();
  const emit = defineEmits(['getArea']);
  const route = useRoute();
  // 定义 props
  const props = defineProps({
    longitude: {
      type: Number,
      default: undefined,
    },
    latitude: {
      type: Number,
      default: undefined,
    },
  });
  function onClose() {
    userStore.closePage(route, router, '/controlMap');
    router.push({ path: '/controlMap' });
  }

  let lampData = ref({
    id: route.query.id,
    deviceSecret: route.query.deviceSecret,
    uniqueNo: route.query.uniqueNo,
  });
  const lightMarkers = [];
  const address = ref('');
  let map = null;
  let mouseTool = null;
  let polygon = null; // 用于存储绘制的多边形
  let currentOverlay = null;
  let AMapInstance = null;

  // 初始化地图
  let initMap = () => {
    const container = document.getElementById('container');
    if (!container) {
      console.error('Map container div not exist');
      return;
    }
    window._AMapSecurityConfig = {
      securityJsCode: 'fd83369091f64a0f25572251e0c9eae5',
    };

    AMapLoader.load({
      key: '1778f4eaad1a5a43d9b04ef0c9690b3f', // 高德地图 Key
      version: '2.0',
      plugins: ['AMap.MouseTool', 'AMap.ControlBar', 'AMap.ToolBar'],
    })
      .then((AMap) => {
        AMapInstance = AMap;
        map = new AMap.Map('container', {
          center: [route.query.longitude || 116.333926, route.query.latitude || 39.997245], // 地图中心点
          zoom: 17,
          pitch: 50,
          rotation: -15,
          viewMode: '3D',
          zooms: [2, 20],
          rotateEnable: true,
          pitchEnable: true,
        });
        // 初始化鼠标工具
        mouseTool = new AMap.MouseTool(map);
        // 监听绘制完成事件
        mouseTool.on('draw', (event) => {
          if (event.obj instanceof AMap.Polygon) {
            polygon = event.obj;
            currentOverlay = event.obj;
          }
        });

        // 添加工具条控件
        const toolBar = new AMap.ToolBar({
          position: {
            left: '40px',
            top: '110px',
          },
        });
        map.addControl(toolBar);

        // 添加比例尺控件
        const controlBar = new AMap.ControlBar({
          position: {
            right: '20%',
            top: '30px',
          },
        });
        map.addControl(controlBar);

        // 设置地图中心点
        map.setCenter([route.query.longitude, route.query.latitude]);
        map.setZoom(15);
        // 如果有路灯信息，添加标记
        if (route.query.longitude && route.query.latitude) {
          console.log(111);

          addLightMarker({ longitude: route.query.longitude, latitude: route.query.latitude });
        }
      })
      .catch((e) => {
        console.error('地图加载失败:', e);
      });
  };

  const addLightMarker = (lightInfo) => {
    if (!map || !AMapInstance || !lightInfo.longitude || !lightInfo.latitude) return;
    const markerContent = document.createElement('div');
    markerContent.className = 'custom-light-marker';

    markerContent.innerHTML = `
    <div style="position: relative;">
      <img src="${streetLampIcon}" style="width: 50px; height: 50px; cursor: pointer;">
    </div>
  `;
    const marker = new AMapInstance.Marker({
      position: [lightInfo.longitude, lightInfo.latitude],
      content: markerContent,
      offset: new AMapInstance.Pixel(-15, -40),
    });

    map.add(marker);
    lightMarkers.push({ longitude: lightInfo.longitude, latitude: lightInfo.latitude, marker: marker }); // 存储对象
    map.setCenter([lightInfo.longitude, lightInfo.latitude]);
  };
  // const videoPlayer = ref();
  onMounted(async () => {
    address.value = props.address;
    initMap();
    console.log(route.query);

    const res = await lightApi.queryDetail({
      id: route.query.id,
    });
    console.log(res);
    
    lampData.value = res.data;
  });
  onUnmounted(() => {
    if (map) {
      map.destroy();
      map = null;
    }
  });
</script>

<style scoped lang="less">
  .total {
    position: relative;
  }
  #container {
    width: 100%;
    height: 100vh;
  }
  .input-card {
    margin-top: 10px;
  }
  .custom-light-marker {
    position: relative;
    cursor: pointer;
  }
  .lampDetailsRight {
    background-color: rgba(255, 255, 255, 0.7);
    background-image: url('/@/assets/images/light/lamp.png');
    background-size: 350%;
    background-position: center;
    background-repeat: no-repeat;
  }
  .lampDetailsLeft {
    position: absolute;
    left: 0;
    top: 0;
    width: 19%;
    height: 100vh;
    z-index: 5;
    background-color: rgba(255, 255, 255, 0.7);
  }
  .lampChart {
    position: absolute;
    bottom: 120px;
    left: 20%;
    width: 60%;
    height: 180px;
    z-index: 5;
    background-color: rgba(255, 255, 255, 0.7);
  }
  .topReturn {
    position: absolute;
    top: 0;
    left: 20%;
    width: 60%;
    height: 40px;
    z-index: 5;
    background-color: rgba(255, 255, 255, 0.7);
  }
  .view {
    height: 200px;
    z-index: 5;
  }
  .divider {
    height: 2px;
    background-color: #eee;
    margin-bottom: 50px;

    span {
      display: block;
      border: 1px #eee solid;
      padding: 10px;
      background-color: #fff;
      border-radius: 10px;
      font-weight: 600;
      color: #1b9aee;
    }
  }
  .lampDetai {
    margin-top: 20%;
  }
</style>
