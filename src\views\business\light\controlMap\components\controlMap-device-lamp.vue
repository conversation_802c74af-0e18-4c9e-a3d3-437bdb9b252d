<!--
  * 控制地图-设备路灯组件
  *
  * @Author:    骆伟林
  * @Date:      2025-03-27 17:51:27
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
  <div class="lampDetailsRight">
    <div class="lampDetailsRightSwitch">
      <span>开关</span>
      <a-switch
        style="margin-left: 5px"
        v-model:checked="switchStatus"
        checked-children="开"
        un-checked-children="关"
        @change="changeSwitch"
        :loading="isLoadingAndDis"
        :disabled="isLoadingAndDis"
      />
    </div>
    <div class="lampDetailsRightSlider">
      <span style="font-size: 18px; font-weight: 700">灯杆亮度</span>
      <a-slider
        style="margin-top: 15px"
        class="slider"
        :disabled="isSliderDis"
        v-model:value="sliderValue"
        :min="0"
        :max="100"
        :marks="marks"
        vertical
        :step="20"
        @afterChange="afterChangeTest"
      >
        <template #mark="{ label, point }">
          <template v-if="point === 100">
            <strong>{{ label }}</strong>
          </template>
          <template v-else>{{ label }}</template>
        </template>
      </a-slider>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import { lightApi } from '/@/api/business/light/light';
const props = defineProps({
  lampData: {
    type: Object,
    default: undefined,
  },
});
console.log(props.lampData);
const switchStatus = ref();
const isLoadingAndDis = ref(false);
const isSliderDis = ref(false);
const sliderValue = ref(0);
const marks = ref({
  0: '0',
  20: '20',
  40: '40',
  60: '60',
  80: '80',
  100: {
    style: {
      color: '#f50',
    },
    label: '100',
  },
});

async function changeSwitch() {
  console.log( switchStatus.value);
  const data=switchStatus.value===true?'switch_on':'switch_off';
  console.log(sliderValue.value);
  await lightApi.SwitchLamp({
    id: props.lampData.id,
    controlType: data,
    brightness:sliderValue.value,
 });
  await queryData();
}

async function afterChangeTest() {
console.log(sliderValue.value);

  await lightApi.changeLightBrightness({
    id: props.lampData.id,
    brightness: sliderValue.value,
    controlType:'set_light'
  });
}

async function queryData(){
  const lampData = await lightApi.queryDetail(props.lampData);
  console.log(lampData);
  
  switchStatus.value=lampData.data.data.switchState;
  sliderValue.value=lampData.data.data.brightness;
}  
onMounted(async () => {
await queryData();
});
</script>

<style scoped lang="less">
.lampDetailsRight{
  position: absolute;
  right: 0;
  top: 0;
  width: 19%;
  height: 100vh;
  z-index: 5;
  background-color: rgba(255, 255, 255, 0.7);
  background-image: url('/@/assets/images/light/lamp.png');
  background-size: 200%;
  background-position: center; 
  background-repeat: no-repeat; 
  .lampDetailsRightSwitch {
        position: absolute;
        left: 64%;
        top: 20%;
        width: 120px;
        display: flex;
        align-items: center;
        // background: skyblue;
        // background: #000;
      }
      .lampDetailsRightSlider {
        position: absolute;
        left: 70%;
        top: 50%;
        transform: translateY(-50%);
        min-width: 10%;
        height: 300px;

        /deep/ .ant-slider-vertical {
        margin-top: 5px;
        margin-left: 0px;
        width: 25px; /* 减小滑块的宽度 */
      }

      /deep/ .ant-slider-rail {
        width: 12px; /* 减小轨道的宽度 */
        background-color: rgba(0, 0, 0, 0.1);
        border-radius: 6px; /* 调整圆角 */
      }

      /deep/ .ant-slider-track {
        width: 12px; /* 减小痕迹的宽度 */
        border-radius: 5px; /* 调整圆角 */
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
        background-color: #409eff;
      }

      /deep/ .ant-slider-mark {
        margin-left: 12px; /* 调整标记的左边距 */
        font-size: 18px;
        span {
          width: 16px;
          height: 16px;
        }
      }

      /deep/ .ant-slider-step {
        width: 10px; /* 减小步长的宽度 */

        /deep/ .ant-slider-dot {
          width: 18px;
          height: 18px;
        }

        span {
          width: 18px;
          height: 10px;
          border-radius: 9px; /* 调整圆角 */
          border: 3px solid #f0f0f0;
        }
      }

      /deep/ .ant-slider-handle {
        width: 18px;
        height: 18px;
        &::after {
          width: 14px;
          height: 14px;
          inset-block-start: 0;
          inset-inline-start: 2px; /* 调整内边距 */
        }
      }

      .slider {
        width: 10px; /* 减小滑块的宽度 */
      }
      }
}
</style>