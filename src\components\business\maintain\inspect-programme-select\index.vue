<template>
  <a-select 
    v-model:value="selectValue" 
    :style="`width: ${width}`" 
    :placeholder="props.placeholder"
    show-search
    :allow-clear="true"
    :filter-option="filterOption"
    @change="onChange"
    :options="programmeOptions"
    option-label-prop="label"
  >
  </a-select>
</template>

<script setup>
import { inspectProgrammeApi } from '../../../../api/business/maintain/inspect-programme/inspect-programme-api';
import { onMounted, ref, watch } from 'vue';
import { smartSentry } from '/@/lib/smart-sentry';

const props = defineProps({
  value: {
    type: [String, Number],
    default: undefined,
  },
  width: {
    type: String,
    default: '150px',
  },
  placeholder: {
    type: String,
    default: '请选择巡检方案',
  },
});

const emit = defineEmits(['update:value', 'change']);

// 当前选中的值
const selectValue = ref(props.value);
// 完整的方案列表数据
const programmeList = ref([]);
// 选择器选项
const programmeOptions = ref([]);

// 过滤选项方法
const filterOption = (input, option) => {
  return option.label.toLowerCase().includes(input.toLowerCase());
};

// 获取巡检方案列表
async function fetchProgrammeList() {
  try {
    const response = await inspectProgrammeApi.queryList();
    programmeList.value = response.data || [];
    
    // 转换数据格式为选择器需要的格式
    programmeOptions.value = programmeList.value.map(item => ({
      value: item.id,
      label: item.programmeName,
    }));
  } catch (error) {
    smartSentry.captureError(error);
    console.error('获取巡检方案列表失败:', error);
  }
}

// 监听value变化
watch(
  () => props.value,
  (newValue) => {
    selectValue.value = newValue;
  }
);

// 选择变化事件
function onChange(value) {
  emit('update:value', value);
  const selectedProgramme = programmeList.value.find(item => item.id === value);
  emit('change', selectedProgramme);
}

// 组件挂载时获取数据
onMounted(() => {
  fetchProgrammeList();
});
</script>