<!--
  * 控制地图
  *
  * @Author:    骆伟林
  * @Date:      2025-03-24 17:51:27
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
    <div class="map-container">
    <div id="container" ref="mapContainer"></div>
    <a-button @click="toggleDrawer" type="primary" class="toggle-button">
      <MenuUnfoldOutlined />搜索路灯
    </a-button>
    <a-dropdown class="tree-control">
      <template #overlay>
        <a-menu>
          <a-menu-item key="1" @click="filter('around')" >
            <a-tree
            v-model:checkedKeys="checkedKeys"
            :tree-data="treeData"
            checkable
            defaultExpandAll
            @check="handleCheck"
          >
          </a-tree>
          </a-menu-item>
        </a-menu>
      </template>
      <a-button>
        配置地图展示
        <DownOutlined />
      </a-button>
    </a-dropdown>
    <searchDrawer v-if="showDrawer" class="search-drawer" @getTarget="getTarget"/>
  </div>

</template>
<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
import AMapLoader from '@amap/amap-jsapi-loader';
import searchDrawer from './components/controlMap-search.vue';
import streetLampIcon from '/@/assets/images/light/icons8-路灯-64.png';
import lampIcon from '/@/assets/images/logo/wiselume-logo.png';
import { router } from '/@/router';
import { lightApi } from '/@/api/business/light/light';

const treeData = ref([
  {
    title: '显示周围路灯',
    key: 'around',
    children: [],
  },
  {
    title: '显示选择路灯',
    key: 'single',
    children: [],
  },
  {
    title: '显示区域',
    key: 'region',
    children: [],
  },
]);
const checkedKeys = ref(['around', 'single', 'region']); 

const handleCheck = (checkedKeys, { checked, node }) => {
  const key = node.eventKey;
  if (key === 'around') {
    showFilter.value.around = checked;
    filter('around');
  } else if (key === 'single') {
    showFilter.value.single = checked;
    filter('single');
  } else if (key === 'region') {
    showFilterRegion.value = checked;
    filterRegion();
  }
};



let map = null;
let AMapInstance = null;
let mouseTool = null;
const showDrawer = ref(false);
const showFilter = ref( {
  around:true,
  single:true
});
const showFilterRegion = ref(true);
let polygon = null;
let currentOverlay = null;
let lightMarker = ref(null);
let lightMarkers = [];

// 初始化地图
const initMap = () => {
  const container = document.getElementById('container');
  if (!container) {
    console.error('Map container div not exist');
    return;
  }
  window._AMapSecurityConfig = {
    securityJsCode: 'fd83369091f64a0f25572251e0c9eae5',
  };

  AMapLoader.load({
    key: '1778f4eaad1a5a43d9b04ef0c9690b3f',
    version: '2.0',
    plugins: [
      'AMap.MouseTool',
      'AMap.ToolBar',
      'AMap.Scale',
      'AMap.HawkEye',
      'AMap.ControlBar',
      'AMap.Geometry',
      'AMap.AutoComplete',
      'AMap.PlaceSearch',
      'AMap.Marker',
      'AMap.InfoWindow'
    ],
  })
    .then((AMap) => {
      AMapInstance = AMap;
      map = new AMap.Map('container', {
        center: [113.275,22.5344],
        zoom: 17,
        viewMode: '2D',
        pitch: 30,
      });

      mouseTool = new AMap.MouseTool(map);
      mouseTool.on('draw', (event) => {
        if (event.obj instanceof AMap.Polygon) {
          polygon = event.obj;
          currentOverlay = event.obj;
        }
      });

      const scale = new AMap.Scale();  //实例化比例尺控件
      map.addControl(scale);           //添加比例尺控件

      const toolBar = new AMap.ToolBar({
        position: {
          top: '110px',
          right: '40px',           //对象定位控件进行定位操作
        },
      });
      map.addControl(toolBar);

      if (parsedLatAndLon.value.length > 0) {
        drawExistingPolygon(parsedLatAndLon.value, regionData,regionInit.value);
      }

      // 如果有路灯信息，添加标记
      if (lightMarker.value && lightMarker.value.longitude && lightMarker.value.latitude) {
        addLightMarker(lightMarker.value);
      }
       map.on('zoomend', calculateMapRadius);
       map.on('moveend', handleMapMoveEnd);
       calculateMapRadius();
    })
    .catch((e) => {
      console.error('地图加载失败:', e);
    });
};
// 113.390345,22.527096 113.276946,22.534067
let center=[113.276946, 22.534067]
const handleMapMoveEnd = () => {
  if (map) {
    const cent = map.getCenter();
    center=[cent.getLng(),cent.getLat()]
    getInitData();
  }
};

let radius= ref(0);
const calculateMapRadius = () => {
  if (!map || !AMapInstance) return;
  const resolution2 = map.getResolution();
const pixelDistance2 = Math.max(map.getSize().width, map.getSize().height) / 2;
const realDistance = resolution2 * pixelDistance2;
radius.value=realDistance/1000
  clearLightMarkers()
  getInitData();
};

const addLightMarker = (lightInfo, flag) => {
  if (!map || !AMapInstance || !lightInfo.longitude || !lightInfo.latitude) return;
  if (flag === 'singleInit') {
    clearLightMarkers('singleInit');
  }
  const markerContent = document.createElement('div');
  markerContent.className = 'custom-light-marker';

  markerContent.innerHTML = `
    <div style="position: relative;">
      <img src="${streetLampIcon}" style="width: 50px; height: 50px; cursor: pointer;">
    </div>
  `;

  markerContent.addEventListener('click', () => {
    showInfoWindow(lightInfo, marker.getPosition());
  });

  const marker = new AMapInstance.Marker({
    position: [lightInfo.longitude, lightInfo.latitude],
    content: markerContent,
    offset: new AMapInstance.Pixel(-15, -40)
  });
  marker.lightInfo = lightInfo;
  map.add(marker);
  lightMarkers.push(marker);
  if (flag === 'singleInit') {
    map.setCenter([lightInfo.longitude, lightInfo.latitude]);
  }
};

const clearLightMarkers = (flag, data) => {
  if (flag === 'single' || flag === 'around') {
    if (Array.isArray(data)) {
      data.forEach(item => {
        if (item && item.remove) {
          item.remove();
        } else {
          console.error('Invalid marker object:', item);
        }
      });
    } else {
      console.error('Data is not an array:', data);
    }
  } else {
    lightMarkers.forEach(marker => {
      if (marker && marker.remove) {
        marker.remove();
      } else {
        console.error('Invalid marker object:', marker);
      }
    });
    lightMarkers = [];
  }
}

const showLightMarkers = (flag, data) => {
  if (data && data.length > 0) {
    data.forEach(item => {
      if (item && item.setMap) {
        item.setMap(map);
      }
    });
  }
};

const hideLightMarkers = (flag, data) => {
  console.log(data);

  if (data && data.length > 0) {
    data.forEach(item => {
      if (item && item.setMap) {
        item.setMap(null);
      }
    });
  }
};
// 显示区域多边形
const showPolygon = () => {
  if (parsedLatAndLon.value.length > 0) {
    drawExistingPolygon(parsedLatAndLon.value, regionData);
  }
};

// 隐藏区域多边形
const hidePolygon = () => {
  if (polygon) {
    map.remove(polygon);
    polygon = null;
  }
};

//路灯详情跳转
function lightDetail(data){
  router.push({
      path: '/controlMap-device-detail',
      query: {
        id:data.id,
        longitude:data.longitude,
        latitude: data.latitude,
        deviceSecret:data.deviceSecret,
        uniqueNo:data.uniqueNo
      },
    });
}

//区域详细跳转
function regionDetail(data){
  router.push({
      path: '/controlMap-region-detail',
      query: {
        regionId:data.id,
        regionName:data.name,
        regionAddress:data.address,
      },
    });
}

let infoWindow = null;
const showInfoWindow = (lightInfo, position) => {
  if (infoWindow) {
    infoWindow.close();
  }
  const infoWindowContent = document.createElement('div');
  infoWindowContent.innerHTML = `
    <div>
      <div style="width: 40px; height: 40px;">
        <img style="float:left; width: 40px; height: 40px;" src="${lampIcon}" />
      </div>
      <div>
        <b>设备名称：${lightInfo.deviceName || '路灯'}</b>
        <p>设备类型 : ${lightInfo.deviceType}</p>
        <div style="color: #2b8cbe;cursor: pointer;float:right;" class="detail">详情</div>
      </div>
    </div>
  `;

const detailButton = infoWindowContent.querySelector('.detail');
if(detailButton){
  detailButton.addEventListener('click', () => {
    lightDetail(lightInfo);
  });
}

  infoWindow = new AMapInstance.InfoWindow({
    content: infoWindowContent,
    offset: new AMapInstance.Pixel(10, -40)
  });

  infoWindow.open(map, position);
};

//回显区域
function drawExistingPolygon(path, regionData,regionInit) {
  // hidePolygon();
  if (map && path.length > 0) {
    polygon = new AMapInstance.Polygon({
      path: path,
      strokeColor: '#2b8cbe',
      strokeOpacity: 1,
      strokeWeight: 1,
      strokeStyle: 'dashed',
      fillColor: '#1791fc',
      fillOpacity: 0.5,
    });

    // 创建信息窗口
    const infoWindowContent = document.createElement('div');
    infoWindowContent.innerHTML = `
      <div>
      <div style="width: 40px; height: 40px;">
        <img style="float:left; width: 40px; height: 40px;" src="${lampIcon}" />
      </div>
      <div>
        <h3>区域：${regionData.name || '区域'}</h3>
        <p>区域地址 : ${regionData.address}</p>
        <p>联系人 : ${regionData.contact}</p>
        <p>电话 : ${regionData.phone}</p>
        <div style="color: #2b8cbe;cursor: pointer;float:right;" class="detail">详情</div>
      </div>
    </div>
    `;
  const detailButton = infoWindowContent.querySelector('.detail');
if(detailButton){
  detailButton.addEventListener('click', () => {
    regionDetail(regionData);
  });
}
    const infoWindow = new AMapInstance.InfoWindow({
      content: infoWindowContent,
      offset: new AMapInstance.Pixel(10, -40)
    });

    // 绑定点击事件
    polygon.on('click', (event) => {
      infoWindow.open(map, event.lnglat);
    });

    map.add(polygon);
    currentOverlay = polygon;
    if(regionInit===true){
   map.setFitView([polygon]);
    }

  }
}

const parsedLatAndLon = computed(() => {
  try {
    const parsed = JSON.parse(latAndLon.value.replace(/\(/g, '[').replace(/\)/g, ']'));
    return parsed.map(([lat, lng]) => [lng, lat]);
  } catch (e) {
    return [];
  }
});

let latAndLon = ref();
let longitude = null;
let latitude = null;
let deviceList = ref();
let searchType = ref('');
let regionData = undefined

let regionInit = ref(false)
function getTarget(target) {
  showFilter.value.single=true
  if (infoWindow) {
    infoWindow.close();
  }

  if (target.searchType === 'device') {
    regionData=[]
    // 移除事件监听，避免触发 calculateMapRadius
    // map.off('moveend', handleMapMoveEnd);
    // map.off('zoomend', calculateMapRadius);
    lightMarker.value = target.item;
    flag.value = 'singleInit';
    addLightMarker(lightMarker.value, flag.value);
  } else {
    regionData=target.item
    latAndLon.value = target.item.latAndLon;
    longitude = target.item.longitude;
    latitude = target.item.latitude;
    deviceList.value = target.deviceList;
    regionInit.value=true
    initMap();
    getInitData()
  }
}

const initLampData = ref([])
let initLampDataStor=[]

let flag=ref('init')
const previousRegionData = ref([]);
async function getInitData() {
  const res = await lightApi.getDeviceByCoordinate({
    longitude: center[0],
    latitude: center[1],
    radius: radius.value,
    unit: 'km',
    limit: 500
  });
  initLampData.value = res.data;
  initLampDataStor = res.data;
  addInitialLightMarkers();
  const regionRes = await lightApi.getRegionByCoordinate({
    longitude: center[0],
    latitude: center[1],
    radius: radius.value,
    unit: 'km',
    limit: 500
  });
  if (regionRes.data && regionRes.data.length > 0) {
  if (JSON.stringify(regionRes.data) !== JSON.stringify(previousRegionData.value)) {
    regionData = regionRes.data;
    previousRegionData.value = regionRes.data;
    regionRes.data.forEach(region => {
      latAndLon.value = region.latAndLon;
      regionInit.value=false
      drawExistingPolygon(parsedLatAndLon.value, region,regionInit.value);
    });
  }
}
}
const addInitialLightMarkers = () => {
  if (!map || !AMapInstance || !initLampData.value || initLampData.value.length === 0) return;
  clearLightMarkers();
  flag.value='init'
  initLampData.value.forEach(lightInfo => {
    addLightMarker(lightInfo,flag.value );
  });
};


onMounted(() => {
  initMap();

});

onUnmounted(() => {
  if (map) {
    map.off('moveend', handleMapMoveEnd);
    map.off('zoomend', calculateMapRadius);
    if (mouseTool) {
      mouseTool.off('draw');
    }
    map.destroy();
    map = null;
  }
});

const toggleDrawer = () => {
  showDrawer.value = !showDrawer.value;
   showFilterRegion.value=!showFilterRegion.value
};
let hiddenMarkersSingle = ref([]);
let hiddenMarkersAround = ref([]);
function filter(data) {
  let filteredData = undefined;
  if (data === 'around') {
    filteredData = [];
    if (lightMarker.value && lightMarker.value.id !== undefined) {
      filteredData = initLampData.value.filter(item => item.id !== lightMarker.value.id);
    } else {
      filteredData = initLampData.value;
    }
    if (showFilter.value.around) {
      if (hiddenMarkersAround.value.length > 0) {
        showLightMarkers('around', hiddenMarkersAround.value);
        hiddenMarkersAround.value = [];
      } else {
        const markersToShow = lightMarkers.filter(marker =>
          filteredData.some(item => item.id === marker.lightInfo.id)
        );
        showLightMarkers('around', markersToShow);
      }
    } else {
      const markersToRemove = lightMarkers.filter(marker =>
        filteredData.some(item => item.id === marker.lightInfo.id)
      );
      hideLightMarkers('around', markersToRemove);
      hiddenMarkersAround.value = markersToRemove;
    }
  } else if (data === 'single') {
    filteredData = [];
    if (lightMarker.value) {
      filteredData.push(lightMarker.value);
    }
    if (showFilter.value.single) {
      if (hiddenMarkersSingle.value.length > 0) {
        showLightMarkers('single', hiddenMarkersSingle.value);
        hiddenMarkersSingle.value = [];
      } else {
        const markerToShow = lightMarkers.find(marker =>
          marker.lightInfo.id === lightMarker.value.id
        );
        if (markerToShow) {
          showLightMarkers('single', [markerToShow]);
        }
      }
    } else {
      const markerToRemove = lightMarkers.find(marker =>
        marker.lightInfo.id === lightMarker.value.id
      );
      if (markerToRemove) {
        hideLightMarkers('single', [markerToRemove]);
        hiddenMarkersSingle.value = [markerToRemove];
      }
    }
  }
}
const filterRegion=() =>{
  // showFilterRegion.value=!showFilterRegion.value
  console.log(showFilterRegion.value);

  if(showFilterRegion.value){
    showPolygon ()
  }else{
    hidePolygon()
  }
}

</script>

<style scoped>
.map-container {
  position: relative;
  width: 100%;
  height: 100vh;
}

#container {
  width: 100%;
  height: 100%;
}

.toggle-button {
  position: absolute;
  top: 10px;
  left: 20px;
}

.search-drawer {
  position: absolute;
  top: 5%;
  left: 0;
  width: 400px;
  height: 80%;
  background-color: white;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
}
.detail{
  color: #2b8cbe;
  cursor: pointer;
  float:right;
}
.tree-control {
  position: absolute;
  top: 0;
  right: 20px;
  z-index: 5;
}
</style>