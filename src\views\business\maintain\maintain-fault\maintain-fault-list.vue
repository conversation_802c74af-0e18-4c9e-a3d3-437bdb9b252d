<!--
  * 设备报修单
  *
  * @Author:    yourName
  * @Date:      2025-04-11 11:58:43
  * @Copyright  bdic
-->
<template>
    <!---------- 查询表单form begin ----------->
    <a-form class="smart-query-form">
        <a-row class="smart-query-form-row">
            <a-form-item label="维修单编号" class="smart-query-form-item">
                <a-input style="width: 200px" v-model:value="queryForm.fault_number" placeholder="维修单编号" />
            </a-form-item>
            <a-form-item class="smart-query-form-item">
                <a-button type="primary" @click="onSearch">
                    <template #icon>
                        <SearchOutlined />
                    </template>
                    查询
                </a-button>
                <a-button @click="resetQuery" class="smart-margin-left10">
                    <template #icon>
                        <ReloadOutlined />
                    </template>
                    重置
                </a-button>
            </a-form-item>
        </a-row>
    </a-form>
    <!---------- 查询表单form end ----------->

    <a-card size="small" :bordered="false" :hoverable="true">
        <!---------- 表格操作行 begin ----------->
        <a-row class="smart-table-btn-block">
            <div class="smart-table-operate-block">
                <a-button @click="showForm({})" type="primary" size="small">
                    <template #icon>
                        <PlusOutlined />
                    </template>
                    新建
                </a-button>
                <a-button @click="confirmBatchDelete" type="primary" danger size="small" :disabled="selectedRowKeyList.length == 0">
                    <template #icon>
                        <DeleteOutlined />
                    </template>
                    批量删除
                </a-button>
            </div>
            <div class="smart-table-setting-block">
                <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
            </div>
        </a-row>
        <!---------- 表格操作行 end ----------->

        <!---------- 表格 begin ----------->
        <a-table
            size="small"
            :dataSource="tableData"
            :columns="columns"
            rowKey="id"
            bordered
            :loading="tableLoading"
            :pagination="false"
            :row-selection="{ selectedRowKeys: selectedRowKeyList, onChange: onSelectChange }"
        >
            <template #bodyCell="{ text, record, column }">
                <!-- 图片部分 -->
                <template v-if="column.dataIndex === 'faultImg'">
                    <div class="image-preview-container">
                        <file-preview :file-list="!_.isEmpty(record.faultImg) ? [record.faultImg[0]] : []" type="picture" :width="80"/>
                    </div>
                </template>
                <!-- 审核意见 -->
                <template v-if="column.dataIndex==='checkOpinion'">
                    {{Object.values(AUDIT_STATUS_ENUM).find(item => item.value === Number(record.checkOpinion)).desc}}
                </template>
                <template v-if="column.dataIndex === 'faultHandleStatus'">
                  {{ $smartEnumPlugin.getDescByValue('ERROR_STATUS',  record.faultHandleStatus) }}
                </template>
                <template v-if="column.dataIndex === 'action'">
                    <div class="smart-table-operate">
                        <a-dropdown>
                            <a class="ant-dropdown-link" @click.prevent>
                                审核
                                <DownOutlined />
                            </a>
                            <template #overlay>
                                <a-menu @click="({ key }) => onAudit(record, key)">
                                    <a-menu-item v-for="item in AUDIT_STATUS_ENUM" :key="item.value" :value="item.value">
                                        {{ item.desc }}
                                    </a-menu-item>
                                </a-menu>
                            </template>
                        </a-dropdown>
                        <a-button @click="showForm(record)" type="link">编辑</a-button>
                        <a-button @click="onDelete(record)" danger type="link">删除</a-button>
                    </div>
                </template>
            </template>
        </a-table>
        <!---------- 表格 end ----------->

        <div class="smart-query-table-page">
            <a-pagination
                showSizeChanger
                showQuickJumper
                show-less-items
                :pageSizeOptions="PAGE_SIZE_OPTIONS"
                :defaultPageSize="queryForm.pageSize"
                v-model:current="queryForm.pageNum"
                v-model:pageSize="queryForm.pageSize"
                :total="total"
                @change="queryData"
                @showSizeChange="queryData"
                :show-total="(total) => `共${total}条`"
            />
        </div>

        <MaintainFaultForm  ref="formRef" @reloadList="queryData"/>

    </a-card>
</template>
<script setup>
    import { reactive, ref, onMounted } from 'vue';
    import _ from 'lodash';
    import { message, Modal } from 'ant-design-vue';
    import { SmartLoading } from '/@/components/framework/smart-loading';
    import { maintainFaultApi } from '../../../../api/business/maintain/maintain-fault/maintain-fault-api';
    import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
    import { smartSentry } from '/@/lib/smart-sentry';
    import TableOperator from '/@/components/support/table-operator/index.vue';
    import MaintainFaultForm from './maintain-fault-form.vue';
    import filePreview from '/@/components/support/file-preview/index.vue';
    import {AUDIT_STATUS_ENUM} from '/@/constants/business/maintain-fault/maintain-fault-const'

    // ---------------------------- 表格列 ----------------------------

    const columns = ref([
        {
            title: '维修单编号',
            dataIndex: 'faultNumber',
            ellipsis: true,
        },
        {
            title: '设备名称',
            dataIndex: 'deviceName',
            ellipsis: true,
        },
        {
            title: '测试区域',
            dataIndex: 'regionName',
            ellipsis: true,
        },
        {
            title: '故障简述',
            dataIndex: 'faultRemark',
            ellipsis: true,
        },
        {
            title: '故障情况照片',
            dataIndex: 'faultImg',
            // width:100,
            align: 'center',
            ellipsis: true,

        },
        {
          title: '处理状态',
          dataIndex: 'faultHandleStatus',
          align: 'center',
          ellipsis: true,
        },
        {
            title: '报修人名称',
            dataIndex: 'reporter',
            ellipsis: true,
        },
        {
            title: '审核意见',
            dataIndex: 'checkOpinion',
            ellipsis: true,
        },
        {
            title: '审核人',
            dataIndex: 'auditor',
            ellipsis: true,
        },
        {
            title: '操作',
            dataIndex: 'action',
            fixed: 'right',
            width: 140,
        },
    ]);

    // 未处理
    const UN_HANDLE = "0";

    // 处理中
    const HANDLE = "1";

    // 已完成
    const COMPLETE = "2";
    // ---------------------------- 查询数据表单和方法 ----------------------------

    const queryFormState = {
        fault_number: undefined, //维修单编号
        pageNum: 1,
        pageSize: 10,
    };
    // 查询表单form
    const queryForm = reactive({ ...queryFormState });
    // 表格加载loading
    const tableLoading = ref(false);
    // 表格数据
    const tableData = ref([]);
    // 总数
    const total = ref(0);

    // 重置查询条件
    function resetQuery() {
        let pageSize = queryForm.pageSize;
        Object.assign(queryForm, queryFormState);
        queryForm.pageSize = pageSize;
        queryData();
    }

    // 搜索
    function onSearch(){
      queryForm.pageNum = 1;
      queryData();
    }

    // 查询数据
    async function queryData() {
        tableLoading.value = true;
        try {
            let queryResult = await maintainFaultApi.queryPage(queryForm);
            tableData.value = queryResult.data.list;
            total.value = queryResult.data.total;
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            tableLoading.value = false;
        }
    }
    onMounted(queryData);
    // ---------------------------- 审核 ----------------------------
  async function onAudit(record, key) {
    let param = {
      id: record.id,
      checkOpinion: key,
    };
    try {
      await maintainFaultApi.audit(param);
      message.success('审核成功');
      queryData();
    } catch (e) {
      smartSentry.captureError(e);
    }
  }

    // ---------------------------- 添加/修改 ----------------------------
    const formRef = ref();

    function showForm(data) {
        formRef.value.show(data);
    }

    // ---------------------------- 单个删除 ----------------------------
    //确认删除
    function onDelete(data){
        Modal.confirm({
            title: '提示',
            content: '确定要删除选吗?',
            okText: '删除',
            okType: 'danger',
            onOk() {
                requestDelete(data);
            },
            cancelText: '取消',
            onCancel() {},
        });
    }

    //请求删除
    async function requestDelete(data){
        SmartLoading.show();
        try {
            let deleteForm = {
                goodsIdList: selectedRowKeyList.value,
            };
            await maintainFaultApi.delete(data.id);
            message.success('删除成功');
            queryData();
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            SmartLoading.hide();
        }
    }

    // ---------------------------- 批量删除 ----------------------------

    // 选择表格行
    const selectedRowKeyList = ref([]);

    function onSelectChange(selectedRowKeys) {
        selectedRowKeyList.value = selectedRowKeys;
    }

    // 批量删除
    function confirmBatchDelete() {
        Modal.confirm({
            title: '提示',
            content: '确定要批量删除这些数据吗?',
            okText: '删除',
            okType: 'danger',
            onOk() {
                requestBatchDelete();
            },
            cancelText: '取消',
            onCancel() {},
        });
    }

    //请求批量删除
    async function requestBatchDelete() {
        try {
            SmartLoading.show();
            await maintainFaultApi.batchDelete(selectedRowKeyList.value);
            message.success('删除成功');
            queryData();
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            SmartLoading.hide();
        }
    }
</script>
<style scoped>
.image-preview-container {
    width: 80%;
    height: 80%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin: 0 auto;
  }
</style>
