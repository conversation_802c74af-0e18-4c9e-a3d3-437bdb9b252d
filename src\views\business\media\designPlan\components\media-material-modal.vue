<template>
  <a-modal v-model:open="visible" title="选择媒体素材" width="800px" @cancel="handleCancel">
    <a-form layout="inline">
      <a-form-item label="素材名称">
        <a-input v-model:value="queryForm.name" placeholder="素材名称" allowClear />
      </a-form-item>
      <a-form-item label="素材类型">
        <a-select v-model:value="queryForm.type" allowClear placeholder="请选择素材类型" style="width: 150px"
          :options="MEDIA_TYPE_ENUM.getOptions()">
        </a-select>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="onSearch">查询</a-button>
        <a-button style="margin-left: 8px" @click="resetQuery">重置</a-button>
      </a-form-item>
    </a-form>

    <a-table :dataSource="mediaList" :columns="columns" :pagination="false" :rowKey="record => record.id" size="small"
      style="margin-top: 16px" :row-selection="{
        type: 'radio',
        selectedRowKeys: selectedRowKeys,
        onChange: onSelectChange
      }">
      <template #bodyCell="{ column, text }">
        <template v-if="column.dataIndex === 'type'">
          {{ MEDIA_TYPE_ENUM.getDesc(text) }}
        </template>
      </template>
    </a-table>

    <a-pagination v-model:current="queryForm.pageNum" v-model:pageSize="queryForm.pageSize" :total="total"
      :pageSizeOptions="PAGE_SIZE_OPTIONS" showSizeChanger showQuickJumper @change="loadMediaList"
      :show-total="(total) => `共${total}条`" style="margin-top: 16px; text-align: right" />

    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleOk" :disabled="!selectedItem">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';
import { mediaMaterialApi } from '/@/api/business/media/material/media-material-api';
import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
import { MEDIA_TYPE_ENUM } from '/@/constants/business/media/material/media-material-const'

const emit = defineEmits(['select']);
const visible = ref(false);
const mediaList = ref([]);
const selectedItem = ref(null);
const selectedRowKeys = ref([]);
const total = ref(0);

const columns = [
  { title: '素材名称', dataIndex: 'name', ellipsis: true },
  { title: '素材类型', dataIndex: 'type', ellipsis: true },
  { title: '创建时间', dataIndex: 'createTime', width: 150 }
];

const queryForm = reactive({
  status: 'APPROVED',
  name: '',
  pageNum: 1,
  pageSize: 10
});

const showForm = () => {
  visible.value = true;
  selectedItem.value = null;
  selectedRowKeys.value = [];
  loadMediaList();
};

const onSelectChange = (selectedKeys, selectedRows) => {
  selectedRowKeys.value = selectedKeys;
  selectedItem.value = selectedRows[0];
};

const loadMediaList = async () => {
  try {
    const result = await mediaMaterialApi.queryPage(queryForm);
    if (result.data) {
      mediaList.value = result.data.list.filter(item => item.fileKey);
      total.value = result.data.total;
    }
  } catch (error) {
    console.error('获取媒体列表错误:', error);
    mediaList.value = [];
    total.value = 0;
  }
};

const onSearch = () => {
  queryForm.pageNum = 1;
  loadMediaList();
};

const resetQuery = () => {
  queryForm.name = '';
  queryForm.pageNum = 1;
  loadMediaList();
};

const handleCancel = () => {
  visible.value = false;
  selectedItem.value = null;
  selectedRowKeys.value = [];
};

const handleOk = () => {
  if (!selectedItem.value) {
    message.warning('请先选择一个媒体文件');
    return;
  }
  emit('select', selectedItem.value);
  handleCancel();
};

defineExpose({ showForm });
</script>