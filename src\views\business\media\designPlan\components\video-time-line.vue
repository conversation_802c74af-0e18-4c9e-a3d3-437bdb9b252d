<template>
    <div class="video-timeline" ref="timelineEl">
        <div class="timeline-container">
            <!-- 轨道标签区域 -->
            <div class="track-labels">
                <div class="ruler-placeholder"></div>
                <div v-for="(track, i) in tracks" :key="i" class="track-label">{{ track.label }}</div>
            </div>

            <div class="timeline-content">
                <!-- 时间刻度 -->
                <div class="timeline-ruler" @click="onRulerClick">
                    <div v-for="mark in timeMarks" :key="mark.time" class="time-mark"
                        :style="{ left: `${mark.position}px` }">
                        <div class="mark-line"></div>
                        <div class="mark-label">{{ mark.label }}</div>
                    </div>
                </div>

                <!-- 指针 -->
                <div class="playhead" :style="{ left: `${playheadPosition}px` }" @mousedown.stop="onPlayheadMouseDown">
                </div>

                <!-- 轨道区域 -->
                <div class="tracks-container">
                    <div v-for="(track, i) in tracks" :key="i" class="track">
                        <div class="track-content" @mousedown="onTrackMouseDown($event, i)"
                            :style="{ width: `${timelineWidth}px` }">
                            <div v-for="clip in track.clips" :key="clip.id" class="clip"
                                :class="{ 'active': selectedClipId === clip.id }" :style="{
                                    left: `${secondsToPixels(clip.startTime)}px`,
                                    width: `${secondsToPixels(clip.duration)}px`,
                                    backgroundColor: clipColors[clip.type] || '#999'
                                }" @mousedown.stop="onClipMouseDown($event, clip, i)">
                                <div class="clip-name">{{ clip.name }}</div>
                                <div class="resize-handle left"
                                    @mousedown.stop="onResizeHandleMouseDown($event, clip, 'left')"></div>
                                <div class="resize-handle right"
                                    @mousedown.stop="onResizeHandleMouseDown($event, clip, 'right')"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';

const props = defineProps({
    duration: { type: Number, default: 60 }, // 总时长 (秒)
    currentTime: { type: Number, default: 0 }, // 当前播放时间 (秒)
    pixelsPerSecond: { type: Number, default: 100 }, // 每秒对应的像素宽度
});

const emit = defineEmits(['time-update','clip-select', 'clip-move', 'clip-resize']);

const timelineEl = ref(null); // 对根DOM元素的引用
const selectedClipId = ref(null); // 当前选中的片段ID
const tracks = ref([ // 轨道数据
    { type: 'VIDEO', clips: [], label: '视频' },
    { type: 'IMAGE', clips: [], label: '图片' },
    { type: 'TEXT', clips: [], label: '文字' }
]);

const clipColors = { // 不同类型片段的颜色
    'VIDEO': '#4285f4',
    'IMAGE': '#ea4335',
    'TEXT': '#fbbc05'
};

// 拖拽状态管理对象
const dragState = {
    isDragging: false, // 是否正在拖拽
    dragType: null,    // 拖拽类型: 'clip', 'resize', 'playhead'
    dragData: null,    // 拖拽相关数据 (如被拖拽的片段对象, 原始位置等)
    startX: 0          // 拖拽开始时的鼠标X坐标
};

// 计算指针的像素位置
const playheadPosition = computed(() => Math.round(props.currentTime * props.pixelsPerSecond));
// 计算时间轴内容的总宽度 (像素)
const timelineWidth = computed(() => secondsToPixels(props.duration));
// 计算时间刻度标记数组
const timeMarks = computed(() => {
    const marks = [];
    const interval = 5; // 每5秒一个刻度标记
    for (let time = 0; time <= props.duration; time += interval) {
        marks.push({
            time,
            position: secondsToPixels(time), // 刻度的像素位置
            label: time.toFixed(0) + 's'     // 刻度显示的文本
        });
    }
    return marks;
});

// 将秒数转换为像素值
const secondsToPixels = (s) => Math.round(s * props.pixelsPerSecond);
// 将像素值转换为秒数
const pixelsToSeconds = (p) => parseFloat((p / props.pixelsPerSecond).toFixed(3));
// 获取鼠标点击位置相对于时间轴内容区域左侧的像素偏移
const getTimelineOffset = (x) => {
    const rect = timelineEl.value.querySelector('.timeline-content').getBoundingClientRect();
    return Math.max(0, x - rect.left);
};

// 当点击时间轴刻度尺时触发
function onRulerClick(e) {
    const time = Math.min(props.duration, pixelsToSeconds(getTimelineOffset(e.clientX)));
    emit('time-update', time); // 更新当前时间
}

// 当在轨道空白处按下鼠标时触发
function onTrackMouseDown(e) {
    const time = pixelsToSeconds(getTimelineOffset(e.clientX));
    emit('time-update', Math.min(props.duration, time)); // 更新当前时间
}

// 当在片段上按下鼠标时触发 (开始拖动片段)
function onClipMouseDown(e, clip, trackIndex) {
    e.preventDefault(); // 阻止默认事件，如文本选择
    dragState.isDragging = true;
    dragState.dragType = 'clip';
    dragState.startX = e.clientX;
    dragState.dragData = { clip, trackIndex, originalStartTime: clip.startTime };
    selectedClipId.value = clip.id; // 设置当前选中的片段
    emit('clip-select', clip); // 通知父组件片段被选中
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
}

// 当在片段的调整大小手柄上按下鼠标时触发 (开始调整片段大小)
function onResizeHandleMouseDown(e, clip, edge) {
    e.preventDefault();
    dragState.isDragging = true;
    dragState.dragType = 'resize';
    dragState.startX = e.clientX;
    dragState.dragData = { 
        clip, 
        edge, 
        originalStartTime: clip.startTime, 
        originalDuration: clip.duration,
        maxDuration: clip.type === 'VIDEO' ? clip.originalDuration : Infinity // 视频类型使用原始时长，其他类型无限制
    };
    selectedClipId.value = clip.id;
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
}

// 当在指针上按下鼠标时触发 (开始拖动指针)
function onPlayheadMouseDown(e) {
    e.preventDefault();
    dragState.isDragging = true;
    dragState.dragType = 'playhead';
    dragState.startX = e.clientX;
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
}

// 处理鼠标移动事件 (在拖拽过程中持续触发)
function handleMouseMove(e) {
    if (!dragState.isDragging) return; 

    if (dragState.dragType === 'playhead') {
        const offset = getTimelineOffset(e.clientX);
        const newTime = Math.max(0, Math.min(props.duration, pixelsToSeconds(offset)));
        emit('time-update', newTime); // 更新指针时间
        return;
    }
    // 计算鼠标从拖拽开始点移动的距离 (转换为时间单位)
    const deltaTime = pixelsToSeconds(e.clientX - dragState.startX);
    const clip = dragState.dragData.clip;
    const maxDuration = dragState.dragData.maxDuration; // 视频本身长度

    if (dragState.dragType === 'clip') {
        // 移动片段
        clip.startTime = Math.max(0, Math.min(props.duration - clip.duration, dragState.dragData.originalStartTime + deltaTime));
        emit('clip-move', clip);
    } else if (dragState.dragType === 'resize') {
        if (dragState.dragData.edge === 'right') {
            // 限制最大长度不超过原始视频长度
            const newDuration = Math.max(1, dragState.dragData.originalDuration + deltaTime);
            clip.duration = Math.min(newDuration, maxDuration);
        } else {
            // 调整左边缘，同时保证总长度不超过原始视频长度
            const newStartTime = Math.max(0, dragState.dragData.originalStartTime + deltaTime);
            const maxPossibleDuration = dragState.dragData.originalDuration - deltaTime;
            const newDuration = Math.min(maxPossibleDuration, maxDuration);
            
            clip.duration = Math.max(1, newDuration);
            clip.startTime = newStartTime;
        }
        emit('clip-resize', clip);
    }
}

// 处理鼠标按键松开事件 (结束拖拽)
function handleMouseUp() {
    if (!dragState.isDragging) return;
    // 重置拖拽状态
    dragState.isDragging = false;
    dragState.dragType = null;
    dragState.dragData = null;
    // 移除全局事件监听器
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
}

// 向指定轨道添加片段 (由父组件调用)
function addClip(i, clip) {
    if (i >= 0 && i < tracks.value.length) {
        tracks.value[i].clips.push(clip);
    }
}

// 根据ID删除片段 (由父组件调用)
function removeClip(id) {
    for (const track of tracks.value) {
        const i = track.clips.findIndex(c => c.id === id);
        if (i !== -1) return track.clips.splice(i, 1)[0]; // 找到并删除，返回被删除的片段
    }
    return null; // 未找到
}

// 组件卸载前执行
onUnmounted(() => {
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
});

// 暴露给父组件的方法
defineExpose({
    addClip,
    removeClip,
});
</script>

<style scoped>
.video-timeline {
    position: relative;
    width: 100%;
    background: #1e1e1e;
    color: #fff;
    user-select: none;
    overflow-x: auto;
}

.timeline-container {
    display: flex;
}

.track-labels {
    width: 80px;
    flex-shrink: 0;
    background: #2a2a2a;
}

.ruler-placeholder {
    height: 24px;
    border-bottom: 1px solid #333;
}

.track-label {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #333;
    font-size: 12px;
}

.timeline-content {
    flex: 1;
    position: relative;
}

.timeline-ruler {
    position: relative;
    height: 24px;
    background: #2a2a2a;
    border-bottom: 1px solid #333;
    cursor: pointer;
}

.time-mark {
    position: absolute;
    height: 100%;
    pointer-events: none;
}

.mark-line {
    width: 1px;
    height: 8px;
    background: #555;
}

.mark-label {
    font-size: 10px;
    color: #aaa;
    transform: translateX(-50%);
    white-space: nowrap;
}

.playhead {
    position: absolute;
    top: 0;
    width: 1px;
    height: 100%;
    background: #1677ff;
    z-index: 10;
    pointer-events: auto;
    cursor: col-resize;
}

.playhead::before,
.playhead::after {
    content: '';
    position: absolute;
    left: -3px;
    width: 0;
    height: 0;
    border-left: 3px solid transparent;
    border-right: 3px solid transparent;
}

.playhead::before {
    top: 0;
    border-top: 5px solid #1677ff;
}

.playhead::after {
    bottom: 0;
    border-bottom: 5px solid #1677ff;
}

.tracks-container {
    min-height: 180px;
}

.track {
    height: 60px;
    border-bottom: 1px solid #333;
}

.track-content {
    position: relative;
    height: 100%;
    background: #222;
    cursor: pointer;
}

.clip {
    position: absolute;
    height: 70%;
    top: 15%;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    cursor: pointer;
}

.clip.active {
    outline: 2px solid #fff;
    z-index: 2;
}

.clip-name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px;
}

.resize-handle {
    position: absolute;
    width: 6px;
    height: 100%;
    top: 0;
    background: rgba(255, 255, 255, 0.2);
}

.resize-handle.left {
    left: 0;
    cursor: w-resize;
}

.resize-handle.right {
    right: 0;
    cursor: e-resize;
}
</style>