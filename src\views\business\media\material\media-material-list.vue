<!--
  * 素材管理
  *
  * @Author:    谢志豪
  * @Date:      2025-04-05 15:10:25
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="素材名称" class="smart-query-form-item">
        <a-input style="width: 200px" v-model:value="queryForm.name" placeholder="素材名称" />
      </a-form-item>
      <a-form-item label="素材类型" class="smart-query-form-item">
        <a-select v-model:value="queryForm.type" allowClear placeholder="请选择素材类型"
          :options="MEDIA_TYPE_ENUM.getOptions()" style="width: 150px">
        </a-select>
      </a-form-item>
      <a-form-item label="审核状态" class="smart-query-form-item">
        <a-select v-model:value="queryForm.status" allowClear placeholder="请选择审核状态"
          :options="MEDIA_STATUS_ENUM.getOptions()" style="width: 150px">
        </a-select>
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="onSearch">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="showForm" type="primary" size="small">
          <template #icon>
            <PlusOutlined />
          </template>
          新建
        </a-button>
        <a-button @click="confirmBatchDelete" type="primary" danger size="small"
          :disabled="selectedRowKeyList.length == 0">
          <template #icon>
            <DeleteOutlined />
          </template>
          批量删除
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <!---------- 表格 begin ----------->
    <a-table size="small" :dataSource="tableData" :columns="columns" rowKey="id" bordered :loading="tableLoading"
      :pagination="false" :row-selection="{ selectedRowKeys: selectedRowKeyList, onChange: onSelectChange }">
      <template #bodyCell="{ text, record, column }">
        <!-- 预览列 -->
        <template v-if="column.dataIndex === 'preview'">
          <!-- 图片预览 -->
          <div v-if="record.type === 'IMAGE' && record.fileKey && record.fileKey.length > 0">
            <FilePreview :fileList="record.fileKey" type="picture" />
          </div>
          <!-- 视频预览 -->
          <div v-else-if="record.type === 'VIDEO' && record.fileKey && record.fileKey.length > 0">
            <a-button type="link" @click="showVideoPreview(record.fileKey[0].fileUrl)">预览视频</a-button>
          </div>
        </template>

        <!-- 使用字典时 注释解开并把下面的'dict'修改成自己的字典字段名即可 有多个字典字段就复制多份同理修改 不然不显示字典 -->
        <!-- 方便修改tag的颜色 orange green purple success processing error default warning -->
        <!-- <template v-if="column.dataIndex === 'dict'">
                <a-tag color="cyan">
                  {{ text && text.length > 0 ? text.map((e) => e.valueName).join(',') : '暂无' }}
                </a-tag>
              </template> -->
        <template v-if="column.dataIndex === 'type'">
          {{ MEDIA_TYPE_ENUM.getDesc(text) }}
        </template>
        <template v-if="column.dataIndex === 'status'">
          {{ MEDIA_STATUS_ENUM.getDesc(text) }}
        </template>
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button v-if="record.status === 'PENDING' || record.status === 'REJECTED'" @click="onSubmit(record)"
              type="link" size="small">提交</a-button>
            <a-button v-if=" record.status === 'SUBMITTED'"
              @click="showReviewModal(record)" type="link" size="small">审核</a-button>
            <a-button v-if="record.status === 'APPROVED'" @click="onUnapprove(record)" type="link"
              size="small">反审核</a-button>
            <a-button v-if="record.status === 'PENDING' || record.status === 'REJECTED'" @click="showForm(record)" type="link" size="small">编辑</a-button>
            <a-button @click="onDelete(record)" danger type="link" size="small">删除</a-button>
          </div>
        </template>
      </template>
    </a-table>
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination showSizeChanger showQuickJumper show-less-items :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize" v-model:current="queryForm.pageNum" v-model:pageSize="queryForm.pageSize"
        :total="total" @change="queryData" @showSizeChange="queryData" :show-total="(total) => `共${total}条`" />
    </div>
    <!-- 视频预览模态框 -->
    <a-modal v-model:open="videoModalVisible" title="视频预览" :footer="null" width="800px" @cancel="handleCloseVideo">
        <video :src="currentVideoUrl" controls class="preview-video" ref="videoRef"></video>
    </a-modal>

    <MediaMaterialForm ref="formRef" @reloadList="queryData" />
    <ReviewMaterialModal ref="reviewModal" @reviewSuccess="queryData" />
  </a-card>
</template>
<script setup>
import { reactive, ref, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { mediaMaterialApi } from '/@/api/business/media/material/media-material-api';
import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
import { smartSentry } from '/@/lib/smart-sentry';
import TableOperator from '/@/components/support/table-operator/index.vue';
import { SearchOutlined, ReloadOutlined, PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import { MEDIA_TYPE_ENUM, MEDIA_STATUS_ENUM } from '/@/constants/business/media/material/media-material-const'
import MediaMaterialForm from './components/media-material-form.vue';
import ReviewMaterialModal from './components/review-material-modal.vue';
import FilePreview from '/@/components/support/file-preview/index.vue'; // 图片预览组件

// ---------------------------- 表格列 ----------------------------

const columns = ref([
  // {
  //   title: '主键ID',
  //   dataIndex: 'id',
  //   ellipsis: true,
  // },
  {
    title: '素材名称',
    dataIndex: 'name',
    ellipsis: true,
  },
  {
    title: '素材类型',
    dataIndex: 'type',
    ellipsis: true,
  },
  {
    title: '预览',
    dataIndex: 'preview',
    width: 120,
  },
  {
    title: '文件格式',
    dataIndex: 'fileFormat',
    ellipsis: true,
  },
  // {
  //   title: '素材文件流',
  //   dataIndex: 'fileStream',
  //   ellipsis: true,
  // },
  {
    title: '审核状态',
    dataIndex: 'status',
    ellipsis: true,
  },
  // {
  //   title: '备注',
  //   dataIndex: 'remark',
  //   ellipsis: true,
  // },
  {
    title: '上传人',
    dataIndex: 'createUserName',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    ellipsis: true,
  },
  // {
  //   title: '修改人',
  //   dataIndex: 'updateUserId',
  //   ellipsis: true,
  // },
  // {
  //   title: '修改时间',
  //   dataIndex: 'updateTime',
  //   ellipsis: true,
  // },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 180,
  },
]);

// ---------------------------- 查询数据表单和方法 ----------------------------

const queryFormState = {
  name: undefined, //素材名称
  type: undefined, //素材类型
  status: undefined, //审核状态
  pageNum: 1,
  pageSize: 10,
  sortItemList: [{ column: 'create_time', isAsc: false }],
};
// 查询表单form
const queryForm = reactive({ ...queryFormState });
// 表格加载loading
const tableLoading = ref(false);
// 表格数据
const tableData = ref([]);
// 总数
const total = ref(0);

// 重置查询条件
function resetQuery() {
  let pageSize = queryForm.pageSize;
  Object.assign(queryForm, queryFormState);
  queryForm.pageSize = pageSize;
  queryData();
}

// 搜索
function onSearch() {
  queryForm.pageNum = 1;
  queryData();
}

// 查询数据
async function queryData() {
  tableLoading.value = true;
  try {
    let queryResult = await mediaMaterialApi.queryPage(queryForm);
    tableData.value = queryResult.data.list;
    total.value = queryResult.data.total;
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    tableLoading.value = false;
  }
}

onMounted(queryData);

// ---------------------------- 添加/修改 ----------------------------
const formRef = ref();

function showForm(data) {
  formRef.value.show(data);
}

// ---------------------------- 单个删除 ----------------------------
//确认删除
function onDelete(data) {
  Modal.confirm({
    title: '提示',
    content: '确定要删除选吗?',
    okText: '删除',
    okType: 'danger',
    onOk() {
      requestDelete(data);
    },
    cancelText: '取消',
    onCancel() { },
  });
}

//请求删除
async function requestDelete(data) {
  SmartLoading.show();
  try {
    let deleteForm = {
      goodsIdList: selectedRowKeyList.value,
    };
    await mediaMaterialApi.delete(data.id);
    message.success('删除成功');
    queryData();
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    SmartLoading.hide();
  }
}

// ---------------------------- 批量删除 ----------------------------

// 选择表格行
const selectedRowKeyList = ref([]);

function onSelectChange(selectedRowKeys) {
  selectedRowKeyList.value = selectedRowKeys;
}

// 批量删除
function confirmBatchDelete() {
  Modal.confirm({
    title: '提示',
    content: '确定要批量删除这些数据吗?',
    okText: '删除',
    okType: 'danger',
    onOk() {
      requestBatchDelete();
    },
    cancelText: '取消',
    onCancel() { },
  });
}

//请求批量删除
async function requestBatchDelete() {
  try {
    SmartLoading.show();
    await mediaMaterialApi.batchDelete(selectedRowKeyList.value);
    message.success('删除成功');
    queryData();
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    SmartLoading.hide();
  }
}

// ---------------------------- 审核和反审核 ----------------------------
const reviewModal = ref();

// 显示审核模态框
function showReviewModal(record) {
  reviewModal.value.show(record);
}

// 提交
async function onSubmit(record) {
  SmartLoading.show();
  try {
    await mediaMaterialApi.submittedMaterial(record.id);
    message.success('提交成功');
    queryData();
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    SmartLoading.hide();
  }
}

// 反审核
function onUnapprove(record) {
  Modal.confirm({
    title: '提示',
    content: '确定要反审核该素材吗?',
    okText: '确定',
    okType: 'primary',
    onOk() {
      requestUnapprove(record);
    },
    cancelText: '取消',
    onCancel() { },
  });
}

// 请求反审核
async function requestUnapprove(record) {
  SmartLoading.show();
  try {
    await mediaMaterialApi.unapprovedMaterial(record.id);
    message.success('反审核成功');
    queryData();
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    SmartLoading.hide();
  }
}

// ---------------------------- 视频预览 ----------------------------
const videoModalVisible = ref(false);
const currentVideoUrl = ref('');
const videoRef = ref(null);

// 显示视频预览
function showVideoPreview(url) {
  currentVideoUrl.value = url;
  videoModalVisible.value = true;
}

// 关闭视频预览时处理
function handleCloseVideo() {
  if (videoRef.value) {
    videoRef.value.pause();
  }
}

</script>

<style scoped>
.preview-video {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
</style>
