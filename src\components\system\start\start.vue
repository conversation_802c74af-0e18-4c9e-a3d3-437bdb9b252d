<template>
  <div class="main-container">
    <!-- 背景图片的容器 -->
    <div class="background-div" :style="{backgroundImage: `linear-gradient(to bottom, rgba(0, 0, 50, 0.8), rgba(0, 0, 50, 0.5)), url(${backgroundImage1})`}"></div>

    <!-- 内容区 -->
    <div class="main-content">
      <!-- 两个等高等宽的盒子 -->
      <div class="box">
        <div class="small-box">
            <div class="content">
                <h1>智 慧 灯 杆 一 体 化 平 台</h1>
                <p>Intelligent Street Lamp Integration Platform</p>
                <router-link to="/login"><button class="login-btn">登录</button></router-link>
            </div>
        </div>
      </div>
      <div class="box">
          <div class="container">
              <header class="header">
                  <h1>智 慧 灯 杆 一 体 化 平 台</h1>
                  <p>Intelligent Street Lamp Integration Platform</p>
              </header>
              <div class="categories">
                  <div class="category" @click="indexchange(1)" :class="{'is-active': index === 1}" >
                      <h3 class="category-box">主管部门</h3>
                      <p>Competent Department</p>
                  </div>
                  <div class="category" @click="indexchange(2)" :class="{'is-active': index === 2}" >
                      <h3 class="category-box">运营企业</h3>
                      <p>Operating Enterprise</p>
                  </div>
                  <div class="category" @click="indexchange(3)" :class="{'is-active': index === 3}" >
                      <h3 class="category-box">公共平台</h3>
                      <p>Public Platform</p>
                  </div>
              </div>
              <div style="display: flex; margin-top: 40px;justify-content: center;" v-if="index === 1">
                  <div class="feature-box">
                      <div class="icon">
                          <img :src="notice" alt="Icon"/>
                      </div>
                      <div class="text-content">
                          <h3>公告管理</h3>
                          <p>信息发布·即时通知</p>
                      </div>
                  </div>
                  <div class="feature-box">
                      <div class="icon">
                          <img :src="Audit" alt="Icon" />
                      </div>
                      <div class="text-content">
                          <h3>审核中心</h3>
                          <p>流程审批·效率优化</p>
                      </div>
                  </div>
                  <div class="feature-box">
                      <div class="icon">
                          <img :src="operationImage" alt="Icon" />
                      </div>
                      <div class="text-content">
                          <h3>运营中心</h3>
                          <p>全局控制·运维高效</p>
                      </div>
                  </div>
                  <div class="feature-box">
                      <div class="icon">
                          <img :src="VisualizedManagementImg" alt="Icon" />
                      </div>
                      <div class="text-content">
                          <h3>AI可视化管理中心</h3>
                          <p>智能分析·直观展示</p>
                      </div>
                  </div>
                  <div class="feature-box">
                      <div class="icon">
                          <img :src="map2" alt="Icon" />
                      </div>
                      <div class="text-content">
                          <h3>路灯地图</h3>
                          <p>精准定位·路灯导航</p>
                      </div>
                  </div>
              </div>
              <div style="display: flex; margin-top: 40px;justify-content: center;" v-if="index === 2">
                  <div class="feature-box">
                      <div class="icon">
                          <img :src="illuminatingImage" alt="Icon"/>
                      </div>
                      <div class="text-content">
                          <h3>智慧照明</h3>
                          <p>灵活调控·节能智能</p>
                      </div>
                  </div>
                  <div class="feature-box">
                      <div class="icon">
                          <img :src="VideoImage" alt="Icon"/>
                      </div>
                      <div class="text-content">
                          <h3>视频监控</h3>
                          <p>实时监控·安全保障</p>
                      </div>
                  </div>
                  <div class="feature-box">
                      <div class="icon">
                          <img :src="IotImage" alt="Icon"/>
                      </div>
                      <div class="text-content">
                          <h3>物联中心</h3>
                          <p>设备联网·中心调控</p>
                      </div>
                  </div>
                  <div class="feature-box">
                      <div class="icon">
                          <img :src="DeviceImage" alt="Icon"/>
                      </div>
                      <div class="text-content">
                          <h3>设备管理</h3>
                          <p>设备维护·状态监测</p>
                      </div>
                  </div>
                  <div class="feature-box">
                      <div class="icon">
                          <img :src="map2" alt="Icon"/>
                      </div>
                      <div class="text-content">
                          <h3>路灯地图</h3>
                          <p>精准定位·路灯导航</p>
                      </div>
                  </div>
              </div>
              <div style="display: flex; margin-top: 40px;justify-content: center;" v-if="index === 3">
                  <div class="feature-box">
                      <div class="icon">
                          <img :src="map2" alt="Icon"/>
                      </div>
                      <div class="text-content">
                          <h3>路灯地图</h3>
                          <p>精准定位·路灯导航</p>
                      </div>
                  </div>
                  <div class="feature-box">
                      <div class="icon">
                          <img :src="ShareImage" alt="Icon"/>
                      </div>
                      <div class="text-content">
                          <h3>数据共享平台</h3>
                          <p>信息互联资源共享</p>
                      </div>
                  </div>
              </div>
          </div>
          <div class="footer">
              <p>Copyright 2021-2024 电子科技大学中山学院 大数据与智能计算实验室</p>
              <p>Email: <a href="#"><EMAIL></a></p>
          </div>
      </div>
    </div>
  </div>
</template>

<script setup>
    import bgImage1 from '/@/assets/images/start/bg1.jpg';
    import ShareImage from '/@/assets/images/start/Sharing.png';
    import map2 from '/@/assets/images/start/map2.png';
    import DeviceImage from '/@/assets/images/start/Device .png';
    import IotImage from '/@/assets/images/start/iot.png';
    import VideoImage from '/@/assets/images/start/video.png';
    import illuminatingImage from '/@/assets/images/start/illuminating.png';
    import VisualizedManagementImg from '/@/assets/images/start/Visualized management.png';
    import operationImage from '/@/assets/images/start/operation.png';
    import Audit from '/@/assets/images/start/Audit.png'
    import notice from '/@/assets/images/start/notice.png'
    import { ref } from 'vue';
    import { loginRouters } from '/@/router/system/login';
    const index = ref(1);
    const backgroundImage1 = bgImage1;

    function indexchange(num) {
      index.value = num;
    }
</script>
 <style scoped>
    a{
        color: #fff;
        text-decoration: none;
    }
    .main-container {
      position: relative;
      width: 100%;
      height: 100vh; /* 高度占满视口 */
      overflow-y: auto; /* 如果内容超出视口，启用滚动条 */
      margin: 0;
      padding: 0;
    }
   .background-div {
        /* width: 100%;
        height: 100vh;
        background: fixed;
        background-size: cover;
        background-position: center; */
        position: fixed; /* 始终固定 */
        top: 0;
        left: 0;
        width: 100%;
        height: 100%; /* 占满整个屏幕 */
        background-size: cover; /* 背景图片自适应 */
        z-index: 0; /* 放在最底层 */
    }
    .main-content {
      position: relative;
      
      height: 100vh; /* 模拟内容超出屏幕时可以滚动 */
      padding: 20px;
    }
   .box{
    width: 100%;
    height: 100vh;
    display: flex; /* 启用Flex布局 */
    justify-content: center; /* 水平居中 */
    align-items: center; /* 垂直居中 */
   }
   .small-box {
        width: 600px; /* 定义小盒子的宽度 */
        height: 300px; /* 定义小盒子的高度 */
        display: flex; /* 使用flex布局 */
        justify-content: center; /* 水平居中 */
        align-items: center; /* 垂直居中 */
    }
    /* 内部内容样式 */
    .content {
    text-align: center; /* 文本居中 */
    color: white; /* 字体颜色 */
    }

    /* 标题样式 */
    .content h1 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    }

    /* 副标题样式 */
    .content p {
    font-size: 1.2rem;
    margin-bottom: 35px;
    word-spacing: 10px;
    letter-spacing: 5px;
    }

    /* 按钮样式 */
    .login-btn {
    padding: 0.8rem 1.6rem;
    font-size: 1rem;
    color: white;
    background-color: transparent;
    border: 2px solid white; 
    border-radius: 5px;
    cursor: pointer;
    }


    .login-btn:hover {
    background-color: rgba(255, 255, 255, 0.2); 
    }

    body {
    font-family: Arial, sans-serif;
    /* background-color: #f4f4f4; */
    color: #333;
    margin: 0;
    padding: 0;
}

.container {
    width: 80%;
    font-family: Arial, sans-serif;
    color: #fff;
    text-align: center;
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
    /* background-color: red; */
}

/* 顶部标题样式 */
.header h1 {
  font-size: 2.5rem;
  margin-bottom: 20px;
}

.header p {
  font-size: 1.2rem;
  word-spacing: 10px;
  letter-spacing: 5px;
  margin-bottom: 50px;
}

/* 分类区域布局 */
.categories {
  display: flex;
  justify-content: space-around;
  margin-bottom: 40px;
}

.category {
  text-align: center;
  background:#072e6f;
  border-radius: 50px;
  /* width: 120%; */
  width: 300px;
  height: 60px;
  padding: 10px 20px;
}

.is-active {
  background: #2569c0;
  transition: 0.3s;
}


.category p {
  font-size: 1rem;
  color: #c0c8ea;
}

/* 特性区域布局 */
.feature-box {
  display: flex;
  background-color: rgba(0, 0, 0, 0.6); /* 半透明背景 */
  border-radius: 10px;
  /* padding: 10px; */
  margin: 20px;
  width: 200px;
  color: #fff;
  font-family: 'Arial', sans-serif;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.5);
}

.icon img {
  width: 50px; 
  height: 50px;
  border-radius: 50%; 
  margin: 10px;
}



.text-content h3 {
  margin-top: 15px;
  text-align: left;
  font-size: 12px;
  font-weight: bold;
}

.text-content p {
  font-size: 12px;
  color: gray; 
}

/* 底部 */
.footer {
  position: absolute;
  bottom: -100vh;
  width: 100%;
  padding: 10px;
  color: white;
  text-align: center;
  font-size: 14px;
}

.footer a {
  color: #00f;
  text-decoration: none;
}

.footer a:hover {
  text-decoration: underline;
}
</style>