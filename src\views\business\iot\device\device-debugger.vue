<template>
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="所属产品" class="smart-query-form-item">
        <iot-product-select v-model:value="connetMessage.productId" @change="onProductChange" />
      </a-form-item>
      <a-form-item label="选择设备" class="smart-query-form-item">
        <a-input placeholder="选择设备" @click="showDeviceDebuggerList()" v-model:value="connetMessage.deviceName"
          allow-clear />
      </a-form-item>
      <a-form-item label="mqtt地址" class="smart-query-form-item">
        <a-input placeholder="请输入mqtt地址" v-model:value="mqttBroker" style="width: 300px" />
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="onConnect" class="smart-margin-left10" :loading="connecting"
          :disabled="isConnected">开始调试</a-button>
        <a-button danger @click="onStop" class="smart-margin-left10" :disabled="!isConnected">关闭调试</a-button>
        <a-button @click="onModel" class="smart-margin-left10">物模型TSL</a-button>
      </a-form-item>
    </a-row>
  </a-form>

  <a-row v-if="isConnected">
    <a-col :span="8">
      <a-card>
        <a-tabs v-model:activeKey="debuggerActiveKey" type="card">
          <a-tab-pane key="down" tab="下行指令调试">
            <a-tabs v-model:activeKey="downActiveKey" type="line">
              <a-tab-pane key="propertyDown" tab="属性调试">
                <propertyUp @report="onPropertyDown" :modelValue="modelValue" mode="down" />
              </a-tab-pane>
              <a-tab-pane key="commandDown" tab="命令调用">
                <simulator-command ref="simulatorCommandRef" :modelValue="modelValue" @sendCommand="onCommandDown" />
              </a-tab-pane>
            </a-tabs>
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </a-col>
    <a-col :span="16">
      <a-card>
        <mqtt-table :connetMessageData="connetMessageData" :deviceId="connetMessage.deviceId" />
      </a-card>
    </a-col>
  </a-row>
  <a-row v-else>
    <a-col :span="24">
      <a-result title="设备调试器" sub-title="设备调试器可以调试已与云端建立 MQTT 连接的真实设备，上报数据，接收云端控制指令，以及物模型通信等完整过程">
        <template #icon>
          <img style="width: 200px; height: 200px" :src="leftBg" />
        </template>
        <template #extra>
          <a-row>
            <a-alert type="info" show-icon style="margin: 0 auto;">
              <template #message>
                <span>设备调试器仅用于调试在线设备，若需模拟离线设备请移步</span>
                <router-link to="./deviceSimulator">设备模拟</router-link>
              </template>
            </a-alert>
          </a-row>
          <br>
          <a-row>
            <a-col :span="24">
              <a-button type="primary" @click="onConnect" :loading="connecting">启动设备调试器</a-button>
            </a-col>
          </a-row>
        </template>
      </a-result>
    </a-col>
  </a-row>
  <device-debugger-list ref="deviceDebuggerListRef" @selectData="onSelectData" />
  <modelTslForm ref="modelTslFormRef" :modelValue="modelValue" />
</template>

<script setup>
import iotProductSelect from '/@/components/business/iot/iot-product/iot-product-select/index.vue';
import leftBg from '/@/assets/images/login/left-bg2.png';
import mqttTable from './components/mqttTable.vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import MqttConfig from '/@/utils/mqtt/MqttConfig';
import propertyUp from './components/property-up.vue';
import { ref, reactive, watch } from 'vue';
import deviceDebuggerList from './components/device-debugger-list.vue';
import simulatorCommand from './components/simulatorCommand.vue';
import { iotProductApi } from '/@/api/business/iot/product/iot-product-api';
import { smartSentry } from '/@/lib/smart-sentry';
import modelTslForm from './components/modelTsl-form.vue';
import mqtt from 'mqtt';

// 面板初始化
const debuggerActiveKey = ref('down');
const downActiveKey = ref('propertyDown');

const connetMessageState = {
  productId: undefined,
  productKey: undefined,
  deviceName: undefined,
  deviceId: undefined,
  deviceSecret: undefined,
  tenantId: undefined
}

const connetMessage = reactive({ ...connetMessageState });

function onProductChange(product) {
  connetMessage.productKey = product?.productKey;
}

// 设备模态框
const deviceDebuggerListRef = ref(null);
function showDeviceDebuggerList() {
  if (!connetMessage.productKey) {
    message.warning("请先选择产品");
    return;
  }
  deviceDebuggerListRef.value.showForm(connetMessage.productKey);
}

const modelTslFormRef = ref(null);
// 物模型
function onModel() {
  if (!isConnected.value) {
    message.warn("请先开启调试器")
    return;
  }
  modelTslFormRef.value.showForm();
}

// 设备选择
function onSelectData(data) {
  connetMessage.deviceName = data[0]?.deviceName;
  connetMessage.deviceId = data[0]?.id;
  connetMessage.deviceSecret = data[0]?.deviceSecret;
  connetMessage.tenantId = data[0]?.tenantId;
}

const connetMessageData = ref([]);
const messageType = ref('连接成功');

// MQTT 连接配置
const mqttBroker = ref('wss://mqtt.iot.wisedataacademic.com');
const publish_Topic = 'iot/my_pub';
const subscribe_Topic = 'iot/my_sub';
const mqttClient = ref(null);
const isConnected = ref(false);
const connecting = ref(false);

// 发布 MQTT 消息，确保用字符串
function publishMessage(topic, payload, type) {
  if (!mqttClient.value || !isConnected.value) {
    message.warning("请先连接MQTT服务器");
    return false;
  }

  // 确保发送字符串
  const payloadStr = typeof payload === 'string' ? payload : JSON.stringify(payload);

  // 添加到消息列表
  connetMessageData.value.push({
    message: `connection is active, topic:${topic}, ${payloadStr}`,
    updateTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    type: type
  });

  // 发送消息
  mqttClient.value.publish(topic, payloadStr, (err) => {
    if (err) {
      message.error(`发布失败: ${err.message}`);
      return false;
    }
  });

  return true;
}

// 处理下行属性设置
function onPropertyDown(reportData) {
  // 构造标准的物模型属性设置消息
  const commandObj = {
    method: "thing.service.property.set",
    id: Date.now().toString(),
    params: {}
  };

  if (reportData.type === 'single') {
    commandObj.params[reportData.identifier] = reportData.value;
  } else if (reportData.type === 'batch') {
    commandObj.params = reportData.properties;
  }

  // 发送下行消息
  const success = publishMessage(subscribe_Topic, commandObj, '属性下行');
  if (success) {
    message.success('属性已下发');
  }
}

// 处理下行命令调用
function onCommandDown(command) {
  const success = publishMessage(subscribe_Topic, command, '命令下行');
  if (success) {
    message.success('命令已下发');
  }
}

async function onConnect() {
  if (!connetMessage.productId) {
    message.warning("请选择产品");
    return;
  }
  if (!connetMessage.deviceName) {
    message.warning("请选择设备");
    return;
  }
  if (!connetMessage.deviceSecret) {
    message.warning("设备密钥不能为空");
    return;
  }

  try {
    connecting.value = true;
    // 创建MQTT配置
    const mqttConfig = new MqttConfig(connetMessage);
    const mqttOptions = mqttConfig.getMqttOptions();

    // 创建 MQTT 客户端实例 
    mqttClient.value = await mqtt.connect(mqttBroker.value, mqttOptions);

    // 连接成功
    mqttClient.value.on('connect', function () {
      isConnected.value = true;
      connecting.value = false;
      message.success("连接成功");

      const connectMsg = `productKey=${connetMessage.productKey}, deviceName=${connetMessage.deviceName}`;
      publishMessage(publish_Topic, connectMsg, messageType.value);

      // 订阅主题
      mqttClient.value.subscribe(subscribe_Topic, (err) => {
        if (err) {
          message.error(`订阅失败: ${err.message}`);
        }
      });
    });

    // 连接错误
    mqttClient.value.on('error', (err) => {
      connecting.value = false;
      message.error(`连接错误: ${err.message}`);
      onStop();
    });

    // 接收消息
    mqttClient.value.on('message', (topic, message) => {
      connetMessageData.value.push({
        message: `connection is active, topic:${topic}, ${message.toString()}`,
        updateTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        type: "接收消息"
      });
    });

  } catch (error) {
    connecting.value = false;
    message.error(`连接失败: ${error.message}`);
  }
}

// 停止连接
function onStop() {
  if (mqttClient.value?.connected) {
    mqttClient.value.end();
    mqttClient.value = null;
    message.success("已断开连接");
  }
  isConnected.value = false;
  connetMessageData.value = [];
}

const modelValue = ref({});

async function getModel() {
  try {
    const res = await iotProductApi.queryProductDataByKey(connetMessage.productKey);
    modelValue.value = res.data;
  } catch (error) {
    smartSentry.captureError(error);
  }
}

watch(() => connetMessage.productKey, (newVal) => {
  if (newVal) {
    getModel();
  }
});
</script>