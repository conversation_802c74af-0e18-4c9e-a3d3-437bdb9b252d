<template>
  <a-select v-model:value="selectValue" :style="`width: ${width}`" :placeholder="props.placeholder" show-search
    :showSearch="true" :allowClear="true" :filter-option="filterOption" @change="onChange" :options="deviceList"
    option-label-prop="label">
  </a-select>
</template>

<script setup>
import { iotDeviceApi } from '/@/api/business/iot/device/iot-device-api';
import { onMounted, reactive, ref, watch } from 'vue';
import { smartSentry } from '/@/lib/smart-sentry';

const props = defineProps({
  value: {
    type: [String, Number],
    default: undefined,
  },
  width: {
    type: String,
    default: '150px',
  },
  placeholder: {
    type: String,
    default: '请选择设备',
  },
});

const emit = defineEmits(['update:value', 'change']);
// 选项列表
const deviceList = ref([]);
// 完整的数据列表
const fullDataList = ref([]);
const selectValue = ref(props.value);

const filterOption = (input, option) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

const formState = {
  pageNum: 1,
  pageSize: 500,
}
const form = reactive({ ...formState })

async function queryDeviceList() {
  try {
    const res = await iotDeviceApi.queryPage(form);
    fullDataList.value = res.data.list;
    deviceList.value = fullDataList.value.map((item) => ({
      value: item.id,
      label: item.deviceName,
    }));
  } catch (error) {
    smartSentry.captureError(error);
  }
}

// 监听 value 变化
watch(
  () => props.value,
  (newValue) => {
    selectValue.value = newValue;
  },
);

function onChange(value) {
  emit('update:value', value);
  emit('change', fullDataList.value.find((item) => item.id === value));
}

onMounted(() => {
  queryDeviceList();
});
</script>