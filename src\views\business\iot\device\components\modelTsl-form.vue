<template>
  <a-modal :title="'物模型'" :open="visibleFlag" @cancel="onClose" :width="650" :footer="null">
    <div class="modelTsl-form">
      物模型是云端对设备功能的抽象描述，涵盖了设备的属性、服务和事件。物联网平台通过物的描述语言，即 TSL（Thing Specification Language），以 JSON 格式表达这一模型。开发者可以利用 TSL
      构建并上报设备数据。完整的物模型可用于云端应用的开发，而精简版的物模型则可结合设备端SDK用于设备的开发工作。
    </div>
    <a-row>
      <a-select v-model:value="blockId" :options="blockOptions" @change="blockChange" 
        style="width: 50%;margin: 10px 0 20px 0" placeholder="请选择模块" />
    </a-row>
    <code-editor :value="code" :json="true" theme="nord" height="400px" :readonly="true" />
  </a-modal>
</template>

<script setup>
import { ref, watch } from 'vue';
import CodeEditor from '/@/components/business/code-editor/index.vue';
import { validatenull } from '/@/utils/validate';

const visibleFlag = ref(false);
const blockId = ref('');
const blockOptions = ref([]);

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
});
const code = ref('');

// 监听模型数据变化
watch(() => props.modelValue, (newVal) => {
  code.value = JSON.stringify(newVal);
  loadBlockOptions();
}, { deep: true });

// 加载模块选项
function loadBlockOptions() {
  blockOptions.value = props.modelValue.blocks.map(block => ({
    value: block.id,
    label: block.blockName
  }));
  
  // 默认选中第一个模块
  if (blockOptions.value.length > 0) {
    blockId.value = blockOptions.value[0].value;
    blockChange(blockId.value);
  }
}

// 模块选择变化
function blockChange(value) {
  if (validatenull(value)) {
    return;
  }
  
  const block = props.modelValue.blocks.find(item => item.id === value);
  if (block) {
    // 筛选该模块下的所有命令、属性和事件
    const blockData = {
      block: block,
      properties: props.modelValue.properties.filter(item => item.blockId === value),
      commands: props.modelValue.commands.filter(item => item.blockId === value),
      events: props.modelValue.events.filter(item => item.blockId === value)
    };
    
    code.value = JSON.stringify(blockData, null, 2);
  }
}

const onClose = () => {
  visibleFlag.value = false;
};

// 暴露方法供父组件调用
function showForm() {
  visibleFlag.value = true;
}

defineExpose({ showForm });
</script>

<style lang="less" scoped>
.modelTsl-form {
  background: #f0f0f1;
  color: #999ca1;
  font-size: 16px;
  padding: 10px;
  margin: 10px 0;
}
</style>