<template>
  <a-card size="small" style="margin:0 10px;">
    <div class="tree-header">时间选择</div>
    <a-tree
    :treeData="treeData"
    :fieldNames="{ title: 'title', key: 'key', children: 'children'}"
    v-model:expandedKeys="expandedKeys"
    v-model:selectedKeys="selectedKeys"
    style="height: 90vh;"
    @select="handleSelect"
    class="tree-time"
    />
  </a-card>
</template>

<script setup>
  import { ref, onMounted } from 'vue';
  import dayjs from 'dayjs';

  const emit = defineEmits(['update:timeValue']);
  const treeData = ref([]);
  const selectedKeys = ref([]);
  const expandedKeys = ref([]);
  // -------------------获取树时间----------------------
  const generateTimeTree = () => {
    const currentDate = dayjs();
    const currentYear = currentDate.year();
    const currentMonth = currentDate.month() + 1; // dayjs月份从0开始，需要+1
    const years = [currentYear, currentYear - 1, currentYear - 2]; // 倒序排列年份
    treeData.value = years.map(year => {
      // 如果是当前年份，只显示到上个月；否则显示全年
      const monthCount = year === currentYear ? currentMonth  : 12;
      const months = Array.from({ length: monthCount }, (_, i) => {
        const monthNum = monthCount - i; // 倒序排列月份
        return {
          title: `${monthNum}月`,
          key: `${year}-${monthNum}`, 
          children: []
        };
      });
      return {
        title: `${year}年`,
        key: `year-${year}`,
        children: months
      };
    });

    // 默认展开当前年份
    expandedKeys.value = [`year-${currentYear}`];
    
    // 默认选择上个月
    const lastMonth = currentMonth === 1 ? 12 : currentMonth ;
    const lastMonthYear = currentMonth === 1 ? currentYear - 1 : currentYear;
    selectedKeys.value = [`${lastMonthYear}-${lastMonth}`];
    // 触发默认选择
    handleSelect(selectedKeys.value);
  };

  const handleSelect = (selectedKeys) => {
    if(selectedKeys.length > 0 && selectedKeys) {
      const key = selectedKeys[0];
      if (key.includes('year-')) {
        // 如果选择的是年份，返回 YYYY 格式
        const year = key.split('-')[1];
        emit('update:timeValue', year);
      } else {
        // 如果选择的是月份，返回 YYYY-MM 格式
        const [year, month] = key.split('-');
        const formattedMonth = month.padStart(2, '0');
        emit('update:timeValue', `${year}-${formattedMonth}`);
      }
    }
  }

  onMounted(() => {
    generateTimeTree();
  });
</script>

<style scoped>
:deep(.ant-tree) {
  background: transparent;
}
.tree-header {
  font-size: 16px;
  font-weight: bold;
  padding: 8px 0;
  margin-bottom: 8px;
  border-bottom: 1px solid #e8e8e8;
}
.tree-time {
 padding: 0 10px;
}
</style>