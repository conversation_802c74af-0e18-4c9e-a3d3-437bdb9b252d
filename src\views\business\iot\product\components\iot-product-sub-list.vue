<template>
    <a-button type="primary" @click="goToAddDevice" style="margin: 10px">
        <template #icon>
            <plus-outlined />
        </template>
        前往新增设备
    </a-button>

    <a-table :columns="columns" :data-source="deviceList" :pagination="false" :row-key="(record) => record.id" bordered>
        <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'operation'">
                <a-button type="link" @click="goToDeviceDetail(record)">
                    <EyeOutlined/>
                        查看
                </a-button>
            </template>
            <template v-if="column.key === 'deviceStatus'">
                <a-tag :color="record.deviceStatus === 1 ? 'green' : 'red'">
                    {{ record.deviceStatusName }}
                </a-tag>
            </template>
        </template>
    </a-table>

    <div style="margin-top: 16px; text-align: right">
        <a-pagination show-size-changer show-quick-jumper :current="queryForm.current" :page-size="queryForm.size"
            :total="total" @change="handlePageChange" @showSizeChange="handleSizeChange"
            :show-total="(total) => `共${total}条`" />
    </div>
</template>

<script setup>
import { ref, watch, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { PlusOutlined } from '@ant-design/icons-vue';
import { iotProductApi } from '/@/api/business/iot/product/iot-product-api.js';
import { EyeOutlined } from '@ant-design/icons-vue';
const router = useRouter();
const props = defineProps({
  productInfo: {
    type: Object,
    default: () => {},
  }
});

const deviceList = ref([]);
const total = ref(0);

const queryForm = reactive({
  current: 1,
  size: 10,
  productKey: ''
});

const columns = [
  {
    title: '#',
    dataIndex: 'seq',
    width: 60,
  },
  {
    title: '设备名称',
    dataIndex: 'deviceName',
  },
  {
    title: '设备密钥',
    dataIndex: 'deviceSecret',
  },
  {
    title: '设备所属产品',
    dataIndex: 'productName',
  },
  {
    title: '设备类型',
    dataIndex: 'deviceType',
  },
  {
    title: '设备状态',
    dataIndex: 'deviceStatus',
    key: 'deviceStatus',
  },
  {
    title: '最后上线时间',
    dataIndex: 'lastOnlineTime',
  },
  {
    title: '操作',
    key: 'operation',
    width: 100,
  }
];

const loadDeviceList = async () => {
  if (!props.productInfo || !props.productInfo.productKey) return;
  
  queryForm.productKey = props.productInfo.productKey;
  
  try {
    const res = await iotProductApi.getSubList(queryForm);
    if (res && res.data) {
      // 添加序号
      deviceList.value = res.data.records.map((item, index) => ({
        ...item,
        seq: (queryForm.current - 1) * queryForm.size + index + 1
      }));
      total.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('加载设备列表失败', error);
  }
};

const handlePageChange = (page) => {
  queryForm.current = page;
  loadDeviceList();
};

const handleSizeChange = (current, size) => {
  queryForm.current = 1;
  queryForm.size = size;
  loadDeviceList();
};

const goToAddDevice = () => {
  router.push({
    path: '/iot/device/list',
    query: {
      productKey: props.productInfo.productKey,
      productId: props.productInfo.id
    }
  });
};

const goToDeviceDetail = (record) => {
  router.push({
    path: '/iot/device/detail',
    query: {
      productKey: record.productKey,
      deviceName: record.deviceName,
      id: record.id
    }
  });
};

watch(() => props.productInfo, (newVal) => {
  if (newVal && newVal.productKey) {
    queryForm.current = 1;
    loadDeviceList();
  }
}, { immediate: true, deep: true });

onMounted(() => {
  if (props.productInfo && props.productInfo.productKey) {
    loadDeviceList();
  }
});
</script>
