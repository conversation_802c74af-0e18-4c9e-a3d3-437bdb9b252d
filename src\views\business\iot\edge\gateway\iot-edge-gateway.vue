<!--
  * 边缘网关
  *
  * @Author:   文伊仪
  * @Date:      2025-03-27 21:21:21
  * @Copyright  2025 电子科技大学中山学院大数据与智能计算实验室
-->

<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="标题" class="smart-query-form-item">
        <a-input style="width: 200px" placeholder="请输入标题" v-model:value="queryForm.label" />
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="onSearch">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>

  <a-card size="small" :bordered="false" :hoverable="true">
    <!-- 表格操作行 begin -->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="showForm" type="primary" size="small">
          <template #icon>
            <PlusOutlined />
          </template>
          新建
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
        <!-- 右边个各种小功能标签 -->
      </div>
    </a-row>

    <!---------- 表格操作行 end ----------->

    <a-list :grid="{ gutter: 10, xs: 1, sm: 1, md: 2, lg: 2, xl: 3, xxl: 4 }" :data-source="data" :loading="initLoading">
      <template #renderItem="{ item, index }">
        <a-list-item>
          <a-card hoverable :loading="loading" size="default" :style="item.disabled === false
              ? 'background: linear-gradient(rgba(88, 158, 255, 0.1), white)'
  : item.disabled === true
                ? 'background: linear-gradient(rgba(128, 128, 128, 0.1), white)'
                : 'background: linear-gradient(rgba(128, 128, 128, 0.1), white)'
            ">
            <template #title>
              <a class="detail">{{ item.label }}</a>
            </template>
            <template #extra>
              <div style="width: inherit; position: absolute; right: 30px; top: 15px"><span
                  style="font-weight: bold;">No</span> {{ index + 1 }}
              </div>
              <a-checkbox v-model:checked="item.checked" style="position: absolute; right: 5px; top: 3px" />
              <!--  @change="onSelectChange(item)" -->
            </template>
            <div style="height: 100%; width: 100%; display: flex">
              <div style="flex: 2; display: flex; flex-direction: column">
                <span class="span-multiline-ellipsis">来源：{{ item.source }}</span>
                <span class="span-multiline-ellipsis">类型： {{ item.type }}</span>
                <span class="span-multiline-ellipsis">是否禁用
                  <a-switch :checked="item.disabled === true" checked-children="是" un-checked-children="否"
                    @change="switchChange(item)" /></span>
                <span class="span-multiline-ellipsis">排序号：
                  <a-input-number v-model:value="item.sort" min="1" style="margin-left: 8px; width: 80px;"
                    @change="switchChangeOrder(item)" />
                </span>
                <span class="span-multiline-ellipsis">部署时间：{{ item.deployTime }}</span>
<!--                <a-form-item label="部署时间" name="deployTime">-->
<!--                  <a-date-picker v-model:value="form.deployTime" placeholder="选择部署时间" />-->
<!--                </a-form-item>-->
              </div>
              <div style="flex: 1; display: flex; justify-content: center; align-items: center; z-index: 1;">
                  <img src="/@/assets/images/gateway/icon4.svg" alt="" style="max-width: 150px; max-height: 150px; object-fit: contain" />
              </div>
            </div>
            <template #actions>
              <span style="color: #108ee9" @click="showDetail(item.id)"><setting-outlined key="setting" /> 配置</span>
              <span style="color: #108ee9" @click="showForm(item)">
                <FormOutlined /> 编辑
              </span>
              <span style="color: #f56c6c" @click="onDelete(item)">
                <DeleteOutlined /> 删除
              </span>
            </template>
          </a-card>
        </a-list-item>
      </template>
    </a-list>
    <div class="smart-query-table-page">
      <a-pagination showSizeChanger showQuickJumper show-less-items :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize" v-model:current="queryForm.pageNum" v-model:pageSize="queryForm.pageSize"
        :total="total" @change="queryData" @showSizeChange="queryData" :show-total="(total) => `共${total}条`" />
    </div>

    <edgeGatewayForm ref="formRef" @reloadList="queryData" />


  </a-card>
</template>
<script setup>
import { reactive, ref, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { smartSentry } from '/@/lib/smart-sentry';
import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
import TableOperator from '/@/components/support/table-operator/index.vue';
import { iotGatewayApi } from '/@/api/business/iot/edge/iot-edge-api.js';
import edgeGatewayForm from './components/edge-gateway-form.vue'; 
import {useIotStore} from '/@/store/modules/business/iot/iot.js';
// ---------------------------- 表格列 ----------------------------
const columns = []//
const initLoading = ref(false);
const loading = ref(false);
const state = reactive({
  checked: true,
});

// ---------------------------- 查询数据表单和方法 ----------------------------
const queryFormState = {
  pageNum: 1,
  pageSize: 9,
   label: undefined,
  sort: undefined,
  id: undefined,
  type: undefined,
  source: "gateway",
  disabled: undefined,
  flowData: undefined,
  deployTime:undefined
};
// 查询表单form
const queryForm = reactive({ ...queryFormState });
// 表格数据
const data = ref([]);
// 总数
const total = ref(0);
// 重置查询条件
function resetQuery() {
  let pageSize = queryForm.pageSize;
  Object.assign(queryForm, queryFormState);
  queryForm.pageSize = pageSize;
  queryData();
}

// 搜索
function onSearch() {
  console.log(queryForm, "查询")
  queryForm.pageNum = 1;
  queryData();
}

// 查询数据
async function queryData() {
  initLoading.value = true;
  loading.value = true;
  try {
  let queryResult = await iotGatewayApi.list(queryForm);
    console.log(queryResult);
    total.value = queryResult.data.total;
    data.value = queryResult.data.records;

  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    initLoading.value = false;
    loading.value = false;
  }
}
onMounted(queryData);

// ---------------------------- 添加/修改 ----------------------------
const formRef = ref();
function showForm(data) {
  console.log("打开from表单")
  formRef.value.show(data);
}

// ---------------------------- 编辑 ----------------------------
async function switchChangeOrder(data) {
 
  try {
     await iotGatewayApi.update(data);
    message.success('操作成功');
    queryData();
  } catch (err) {
    smartSentry.captureError(err);
  }
}
// ---------------------------- 禁用 ----------------------------
async function switchChange(data) {
  if (!data.id) {
    return;
  }
  try {
    data.disabled = data.disabled === true ? false : true;
    await iotGatewayApi.disabled(data);
    message.success('操作成功');
    queryData();
  } catch (err) {
    smartSentry.captureError(err);
  }
}
// ---------------------------- 单个删除 ----------------------------
//确认删除
function onDelete(data) {
  Modal.confirm({
    title: '提示',
    content: '确定要删除选吗?',
    okText: '删除',
    okType: 'danger',
    onOk() {
      requestDelete(data);
    },
    cancelText: '取消',
    onCancel() { },
  });
}

//请求删除
async function requestDelete(data) {
  console.log("调用删除接口")
  SmartLoading.show();
  try {
    let arr=[];
    arr.push(data.id)
    await iotGatewayApi.remove(arr);
    message.success('删除成功');
    queryData();
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    SmartLoading.hide();
  }
}

// 正确获取 token 的方式
const iotStore = useIotStore(); // 获取 store 实例
iotStore.getIotToken();  // 调用方法获取 token 
const token = iotStore.iotToken; // 获取 token 值

// 然后在 showDetail 函数中使用
function showDetail(id) {
    const baseUrl = 'http://node-red.iot.wisedataacademic.com:1880/#flow/';
    const fullUrl = `${baseUrl}${id}?token=${token}`;
    // console.log("打印网站地址1", fullUrl,id);
    window.open(fullUrl);
    // console.log("打印网站地址2","1",token);
}

</script>

<style scoped lang="less">
:deep(.ant-card-body) {
  padding: 10px 20px;
}

.scroll-container {
  height: 580px;
  /* 设置容器的高度 */
  overflow-y: auto;
  /* 启用 y 轴滚动 */
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 8px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.multiline-ellipsis {
  display: -webkit-box;
  /* 使用弹性盒子布局 */
  -webkit-box-orient: vertical;
  /* 垂直排列 */
  -webkit-line-clamp: 1;
  /* 显示两行 */
  overflow: hidden;
  /* 隐藏溢出内容 */
  text-overflow: ellipsis;
  /* 显示省略号 */
  width: 160px;
  /* 设置固定宽度 */
  color: #555;
}

.span-multiline-ellipsis {
     // display: flex;
     // width: 230px;
     // margin: 5px;
     display: -webkit-box;          /* Flexbox 模式 */
     -webkit-box-orient: vertical; /* 设置盒子为垂直方向 */
     overflow: hidden;             /* 隐藏多余内容 */
     text-overflow: ellipsis;      /* 增加省略号 */
     -webkit-line-clamp: 1;        /* 限制显示2行，多出的内容隐藏 */
     max-width: 100%;              /* 设置最大宽度 */
     line-height: 1.5;             /* 设置行高（根据需要调整） */
     max-height: calc(1.5em * 2);  /* 与行高和行数匹配 */
     word-break: break-word;       /* 防止单词溢出容器 */
     font-size: 0.8em;             /* 设置字体大小 */
     margin-bottom: 10px;           /* 增加下边距 */
   }

   .detail{
     display: inline-block;
     padding: 5px 10px;
     background-color: rgba(88, 158, 255, 0.1); /* 设置背景颜色为淡蓝色 */
     border: 1px solid rgba(88, 158, 255, 0.1); /* 边框颜色 */
     border-radius: 8px; /* 圆角 */
     color: #2c77f1; /* 字体颜色 */
     font-size: 16px; /* 字体大小 */
     text-align: center; /* 文字居中 */
     font-weight: bold; /* 加粗字体 */
     box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
   }
</style>