<!--
  * 控制地图-设备路灯图表
  *
  * @Author:    骆伟林
  * @Date:      2025-03-27 17:51:27
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
  <div class="lampChart" ref="InboundChartData"></div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted, nextTick } from 'vue';
  import { lightApi } from '/@/api/business/light/light';
  import * as echarts from 'echarts';

  const props = defineProps({
    lampData: {
      type: Object,
      default: undefined,
    },
  });

  // 格式化日期函数
  const formatDate = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // 获取日期范围
  function getDateRange() {
    const today = new Date();
    const sevenDaysAgo = new Date(today);
    sevenDaysAgo.setDate(today.getDate() - 7);

    const dateBegin = formatDate(sevenDaysAgo);
    const dateEnd = formatDate(today);

    return { dateBegin, dateEnd };
  }

  const InboundChartData = ref(null);
  let InboundChartInstance = null;

  // 图表分析
  async function initInboundChart() {
    if (InboundChartData.value) {
      const { dateBegin, dateEnd } = getDateRange();
      const res = await lightApi.energyConsumeLastWeek({
        lampIds: [props.lampData.id],
        dateBegin: dateBegin,
        dateEnd: dateEnd,
      });
      console.log(res);
      
      const time = [];
      let energyConsumption = [];
      const sevenDaysAgo = new Date(dateBegin);
      const today = new Date(dateEnd);
      for (let date = new Date(sevenDaysAgo); date <= today; date.setDate(date.getDate() + 1)) {
        const formattedDate = formatDate(new Date(date));
        time.push(formattedDate);
      }
      energyConsumption = res.length > 0 ? res.map((item) => item.y) : [0, 0, 0, 0, 0, 0, 0];
      InboundChartInstance = echarts.init(InboundChartData.value);
      const option = {
        title: {
          text: '近七天能耗',
        },
        tooltip: {
          trigger: 'category',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985',
            },
          },
        },
        grid: {
          left: '2%', // 减少左边距
          right: '10%', // 减少右边距
          bottom: '3%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: time,
          },
        ],
        yAxis: [
          {
            type: 'value',
            name: 'KWH',
            nameTextStyle: {
              color: '#666',
              align: 'right',
            },
          },
        ],
        series: [
          {
            name: '入库',
            type: 'line',
            smooth: true,
            stack: 'Total',
            lineStyle: {
              color: '#73c0de',
              width: 3,
            },
            itemStyle: {
              color: '#73c0de',
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(115, 192, 222, 0.7)' },
                  { offset: 1, color: 'rgba(115, 192, 222, 0.1)' },
                ],
              },
            },
            emphasis: {
              focus: 'series',
            },
            data: energyConsumption,
          },
        ],
      };
      InboundChartInstance.setOption(option);
    }
  }

  // 组件挂载时初始化图表
  onMounted(async () => {
    await nextTick();
    initInboundChart();
  });

  // 组件卸载时销毁图表
  onUnmounted(() => {
    if (InboundChartInstance && InboundChartInstance.dispose) {
      InboundChartInstance.dispose();
      InboundChartInstance = null; // 清除引用
    }
  });
</script>

<style scoped>
  .lampChart {
    position: absolute;
    bottom: 0;
    left: 10%; /* 调整左边距 */
    width: 100%; /* 增加宽度 */
    height: 180px;
    z-index: 5;
    background-color: rgba(255, 255, 255, 0.7);
  }
</style>
