<!--
  * 照明策略
  *
  * @Author:    李帅兵
  * @Date:      2025-03-28 21:36:15
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
  <a-modal
      :title="form.id ? '编辑' : '添加'"
      :width="600"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }" >
      <a-form-item label="标题"  name="label">
          <a-input style="width: 100%" v-model:value="form.label" placeholder="标题" />
        </a-form-item>
        <a-form-item label="类型"  name="type">
          <a-input style="width: 100%" v-model:value="form.type" placeholder="类型" />
        </a-form-item>
        <a-form-item label="来源"  name="source">
          <a-select v-model:value="form.source" style="width: 100%" placeholder="请选择来源">
            <a-select-option value="rule">用户自建</a-select-option>
            <a-select-option value="sys">系统自带</a-select-option>
            </a-select>
        </a-form-item>
        <a-form-item label="排序号"  name="sort">
          <a-input-number style="width: 100%" v-model:value="form.sort" placeholder="排序号" />
        </a-form-item>
        <a-form-item label="是否禁用"  name="disabled">
          <a-switch :checked="form.disabled === 1"checked-children="否"un-checked-children="是"@change="switchChange"/>
        </a-form-item>
        <a-form-item label="图标"  name="logo">
          <FileUpload
          :defaultFileList="form.logo"
            v-model:value="form.logo"
            :folder="FILE_FOLDER_TYPE_ENUM.COMMON.value"
            buttonText="上传图标"
            listType="picture-card"
            maxUploadSize="1"
             @change="form.logo=$event"
          />
        </a-form-item>
        <a-form-item label="简介"  name="info">
          <a-textarea style="width: 100%" v-model:value="form.info" placeholder="请输入引导说明" />
        </a-form-item>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { reactive, ref, nextTick } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { lightStrategyApi } from '/@/api/business/light/strategy/light-strategy-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  import FileUpload from '/@/components/support/file-upload/index.vue';
  import { FILE_FOLDER_TYPE_ENUM } from '/@/constants/support/file-const';
function switchChange(checked) {
 console.log(checked);
 console.log(form.disabled);
 
  form.disabled = checked===true? 1 : 0;
}
  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show(rowData) {
  Object.assign(form, formDefault);
  if (rowData && !_.isEmpty(rowData)) {
    Object.assign(form, rowData);
  }
  // 使用字典时把下面这注释修改成自己的字典字段 有多个字典字段就复制多份同理修改 不然打开表单时不显示字典初始值
  // if (form.status && form.status.length > 0) {
  //   form.status = form.status.map((e) => e.valueCode);
  // }
  
  visibleFlag.value = true;
  nextTick(() => {
    formRef.value.clearValidate();
  });
}

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
      id: undefined, //id
      type: undefined, //类型
      info: undefined, //简介
      logo: undefined,
      env:undefined,
      label: undefined, //标题
      disabled: 1, //是否禁用
      sort: undefined, //排序号
  };

  let form = reactive({ ...formDefault });

  const rules = {
      type: [{ required: true, message: '类型 必填' }],
      label: [{ required: true, message: '标题 必填' }],
      disabled: [{ required: true, message: '是否禁用 必填' }],
      sort: [{ required: true, message: '排序号 必填' }],
  };


  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      if (form.id) {
        await lightStrategyApi.update(form);
      } else {
        await lightStrategyApi.add(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  defineExpose({
    show,
  });
</script>
