<template>
    <div class="map-left-graph-container">
      <div class="map-graph-title">
        <span>环形仪表盘 / 设备在线率</span>
      </div>
      <div class="map-graph-content">
        <div ref="chartContainer" class="map-chart-container"></div>
      </div>
      <div class="map-status-breakdown">
        <div class="map-status-title">
          <span>状态分布饼状图</span>
        </div>
        <div ref="pieContainer" class="map-pie-container"></div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted, watch, nextTick, onBeforeUnmount } from 'vue';
  import * as echarts from 'echarts';
  
  const props = defineProps({
    deviceList: {
      type: Array,
      default: () => []
    }
  });
  
  const chartContainer = ref(null);
  const pieContainer = ref(null);
  let gaugeChart = null;
  let pieChart = null;
  
  // 计算设备状态数据
  const calculateDeviceStatus = (devices) => {
    const statusCount = {
      online: 0,
      offline: 0,
      fault: 0,
      unknown: 0
    };
    
    if (!devices || devices.length === 0) {
      return {
        statusCount,
        total: 0,
        onlineRate: 0
      };
    }
    
    devices.forEach(device => {
      if (device.deviceStatus === 1) {
        statusCount.online++;
      } else if (device.deviceStatus === 0) {
        statusCount.offline++;
      } else if (device.deviceStatus === 2) {
        statusCount.fault++;
      } else {
        statusCount.unknown++;
      }
    });
    
    const total = devices.length;
    const onlineRate = total > 0 ? (statusCount.online / total * 100).toFixed(2) : 0;
    
    return {
      statusCount,
      total,
      onlineRate
    };
  };
  
  const initGaugeChart = () => {
    if (!chartContainer.value) return;
    
    const { onlineRate } = calculateDeviceStatus(props.deviceList);
    
    gaugeChart = echarts.init(chartContainer.value);
    const option = {
      series: [
        {
          type: 'gauge',
          startAngle: 90,
          endAngle: -270,
          pointer: {
            show: false
          },
          progress: {
            show: true,
            overlap: false,
            roundCap: true,
            clip: false,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: '#1e90ff'
                  },
                  {
                    offset: 1,
                    color: '#73c0de'
                  }
                ]
              }
            }
          },
          axisLine: {
            lineStyle: {
              width: 20
            }
          },
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: false
          },
          data: [
            {
              value: onlineRate,
              name: '在线率',
              title: {
                show: true,
                offsetCenter: ['0%', '-30%'],
                fontSize: 14,
                color: '#333'
              },
              detail: {
                valueAnimation: true,
                formatter: '{value}%',
                fontSize: 20,
                offsetCenter: ['0%', '0%'],
                color: '#1e90ff'
              }
            }
          ]
        }
      ]
    };
    
    gaugeChart.setOption(option);
  };
  
  const initPieChart = () => {
    if (!pieContainer.value) return;
    
    const { statusCount } = calculateDeviceStatus(props.deviceList);
    
    pieChart = echarts.init(pieContainer.value);
    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'horizontal',
        bottom: 0,
        left: 'center',
        itemWidth: 12,
        itemHeight: 10,
        textStyle: {
          fontSize: 10
        }
      },
      series: [
        {
          name: '设备状态',
          type: 'pie',
          radius: ['40%', '65%'],
          center: ['50%', '42%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 6,
            borderColor: '#fff',
            borderWidth: 1
          },
          label: {
            show: false
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 12,
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: [
            { value: statusCount.online, name: '在线', itemStyle: { color: '#67C23A' } },
            { value: statusCount.offline, name: '离线', itemStyle: { color: '#909399' } },
            { value: statusCount.fault, name: '故障', itemStyle: { color: '#F56C6C' } },
            { value: statusCount.unknown, name: '未知', itemStyle: { color: '#E6A23C' } }
          ]
        }
      ]
    };
    
    pieChart.setOption(option);
  };
  
  const updateCharts = () => {
    const { onlineRate, statusCount } = calculateDeviceStatus(props.deviceList);
    
    if (gaugeChart) {
      gaugeChart.setOption({
        series: [
          {
            data: [
              {
                value: onlineRate
              }
            ]
          }
        ]
      });
    } else {
      nextTick(() => {
        initGaugeChart();
      });
    }
    
    if (pieChart) {
      pieChart.setOption({
        series: [
          {
            data: [
              { value: statusCount.online, name: '在线', itemStyle: { color: '#67C23A' } },
              { value: statusCount.offline, name: '离线', itemStyle: { color: '#909399' } },
              { value: statusCount.fault, name: '故障', itemStyle: { color: '#F56C6C' } },
              { value: statusCount.unknown, name: '未知', itemStyle: { color: '#E6A23C' } }
            ]
          }
        ]
      });
    } else {
      nextTick(() => {
        initPieChart();
      });
    }
  };
  
  // 监听设备列表变化
  watch(() => props.deviceList, (newVal, oldVal) => {
    if (newVal && newVal.length > 0) {
      nextTick(() => {
        updateCharts();
      });
    }
  }, { deep: true, immediate: true });
  
  // 响应窗口大小变化
  const handleResize = () => {
    if (gaugeChart) gaugeChart.resize();
    if (pieChart) pieChart.resize();
  };
  
  onMounted(() => {
    nextTick(() => {
      initGaugeChart();
      initPieChart();
    });
    window.addEventListener('resize', handleResize);
  });
  
  // 组件卸载时移除事件监听
  onBeforeUnmount(() => {
    window.removeEventListener('resize', handleResize);
    if (gaugeChart) {
      gaugeChart.dispose();
      gaugeChart = null;
    }
    if (pieChart) {
      pieChart.dispose();
      pieChart = null;
    }
  });
  
  defineExpose({
    updateCharts
  });
  </script>
  
  <style scoped>
  .map-left-graph-container {
    background: white;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    padding: 8px; 
    display: flex;
    flex-direction: column;
    width: 300px; 
    max-height: 450px;
    opacity: 0.95; 
  }
  
  .map-graph-title, .map-status-title {
    font-weight: bold;
    font-size: 14px; 
    padding-bottom: 5px; 
    border-bottom: 1px solid #f0f0f0;
  }
  
  .map-chart-container {
    height: 180px;
    width: 100%;
  }
  
  .map-status-breakdown {
    margin-top: 8px; 
  }
  
  .map-pie-container {
    height: 180px; 
    width: 100%;
  }
  </style>