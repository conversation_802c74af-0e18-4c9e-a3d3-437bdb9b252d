/**
 * 设备故障状态 枚举
 *
 * @Author:    linwj
 * @Date:      2025-04-14
 * @Copyright  中山睿数信息技术有限公司 2025
 */

export const DEVICE_ERROR_STATUS_ENUM = {
  WARNING: {
    value: 'warning',
    desc: '警告',
    color:  'red',
  },
  ALERT: {
    value: 'alert',
    desc: '预警',
    color:  'orange',
  },
  ADVICE: {
    value: 'advice',
    desc: '建议',
    color:  'green',
  },
  INFO: {
    value: 'info',
    desc: '消息',
    color:  'blue',
  },

  getValue(value){
    if (value ==='warning'){
      return DEVICE_ERROR_STATUS_ENUM.WARNING;
    }else if(value ==='alert'){
      return DEVICE_ERROR_STATUS_ENUM.ALERT;
    }else if (value ==='advice'){
      return DEVICE_ERROR_STATUS_ENUM.ADVICE;
    }else if (value ==='info'){
      return DEVICE_ERROR_STATUS_ENUM.INFO;
    }
  }
};
