/**
 * 导出文件相关工具函数
 */

/**
 * 将对象导出为JSON文件
 * @param {Object} data 要导出的数据对象
 * @param {String} filename 文件名称
 */
export const exportJson = (data, filename) => {
  // 将对象转换为JSON字符串
  const jsonString = JSON.stringify(data, null, 2);
  
  // 创建Blob对象
  const blob = new Blob([jsonString], { type: 'application/json' });
  
  // 创建下载链接
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = filename;
  
  // 触发下载
  document.body.appendChild(link);
  link.click();
  
  // 清理
  document.body.removeChild(link);
  URL.revokeObjectURL(link.href);
};

/**
 * 从URL获取数据并导出为JSON文件
 * @param {String} url API地址
 * @param {String} filename 文件名称
 */
export const exportJsonFromUrl = async (url, filename) => {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`导出失败: ${response.status}`);
    }
    
    const data = await response.json();
    exportJson(data, filename);
    return true;
  } catch (error) {
    console.error('导出文件失败:', error);
    return false;
  }
};

export default {
  exportJson,
  exportJsonFromUrl
};