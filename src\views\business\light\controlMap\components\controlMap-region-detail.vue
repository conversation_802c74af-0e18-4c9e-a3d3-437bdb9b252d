<!--
  * 控制地图-区域详情看板
  *
  * @Author:    骆伟林
  * @Date:      2025-03-24 17:51:27
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
  <a-button @click="onClose" style="float: right; margin-top: 10px; margin-right: 10px" type="primary">返回</a-button>
  <a-form class="smart-query-form">
    <a-card>
      <a-descriptions bordered :column="2">
        <a-descriptions-item :span="1" label="区域名称">
          {{ route.query.regionName }}
        </a-descriptions-item>
        <a-descriptions-item :span="1" label="区域地址">
          {{ route.query.regionAddress }}
        </a-descriptions-item>
        <a-descriptions-item :span="1" label="设备总数">
          {{ lampData.deviceCount }}
        </a-descriptions-item>
        <a-descriptions-item :span="1" label="路灯数量">
          {{ lampData.lightCount }}
        </a-descriptions-item>
        <a-descriptions-item :span="1" label="在线率"> {{ lampData.onlineRate }}% </a-descriptions-item>
        <a-descriptions-item :span="1" label="故障率"> {{ lampData.faultRate }}% </a-descriptions-item>
      </a-descriptions>
    </a-card>
  </a-form>
  <a-card size="small" :bordered="false" style="margin-top: 5px">
    <template #title>区域路灯列表</template>
    <a-row class="smart-query-form-row">
      <a-col :span="5">
        <a-form-item label="路灯名称" class="smart-query-form-item">
          <a-input v-model:value="queryForm.deviceName" placeholder="请输入路灯名称" />
        </a-form-item>
      </a-col>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="onSearch">
          <template #icon><SearchOutlined /></template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon><ReloadOutlined /></template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="openLight('switch_on')" type="primary" size="small" :disabled="selectedRowKeyList.length == 0">
          <template #icon>
            <BulbOutlined />
          </template>
          一键开灯
        </a-button>
        <a-button @click="openLight('switch_off')" danger type="primary" size="small" :disabled="selectedRowKeyList.length == 0">
          <template #icon>
            <ApiOutlined />
          </template>
          一键关灯
        </a-button>
        <div style="display: inline-flex; align-items: center; width: 250px">
          <span style="margin-right: 10px; font-size: 14px">亮度：</span>
          <a-slider
            :disabled="isSliderDis"
            v-model:value="sliderValue"
            :min="0"
            :max="100"
            :marks="marks"
            :step="20"
            @afterChange="afterChangeTest"
            style="flex: 1"
          >
            <template #mark="{ label, point }">
              <template v-if="point === 100">
                <strong>{{ label }}</strong>
              </template>
              <template v-else>{{ label }}</template>
            </template>
          </a-slider>
        </div>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
      </div>
    </a-row>
    <a-row class="smart-table-btn-block">
      <a-checkbox v-model:checked="isAllSelected" @change="(e) => toggleSelectAll(e.target.checked)"> 全选 </a-checkbox>
    </a-row>
    <a-list ref="listContainer" :data-source="cardData" :grid="{ gutter: 16, column: 3 }" style="height: calc(100vh - 200px); overflow-y: auto">
      <template #renderItem="{ item }">
        <a-list-item>
          <template #extra>
            <a-checkbox v-model:checked="item.checked" style="position: absolute; left: 60px; top: 10px" @change="onSelectChange(item)"
          /></template>
          <deviceCard :lampList="[item]" />
        </a-list-item>
      </template>
      <template #loadMore>
        <div v-if="tableLoading" style="text-align: center; padding: 10px">
          <a-spin />
        </div>
        <div v-else-if="!hasMoreData && cardData.length > 0" style="text-align: center; padding: 10px">没有更多数据了</div>
      </template>
    </a-list>
  </a-card>

  <a-card size="small" :bordered="false" style="margin-top: 5px">
    <template #title>其他设备列表</template>
    <a-row class="smart-table-btn-block">
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="null" :refresh="queryDataOther" />
      </div>
    </a-row>
    <a-list
      ref="otherListContainer"
      :data-source="otherCardData"
      :grid="{ gutter: 16, column: 3 }"
      style="height: calc(100vh - 200px); overflow-y: auto"
    >
      <template #renderItem="{ item }">
        <a-list-item>
          <deviceCardOther :lampList="[item]" />
        </a-list-item>
      </template>
      <template #loadMore>
        <div v-if="tableLoading" style="text-align: center; padding: 10px">
          <a-spin />
        </div>
        <div v-else-if="!hasMoreData && otherCardData.length > 0" style="text-align: center; padding: 10px">没有更多数据了</div>
      </template>
    </a-list>
  </a-card>
</template>

<script setup>
  import { ref, onMounted, onUnmounted, reactive, nextTick, computed } from 'vue';
  import { useRoute } from 'vue-router';
  import { router } from '/@/router';
  import { useUserStore } from '/@/store/modules/system/user';
  import deviceCard from '../components/controlMap-region-card.vue';
  import deviceCardOther from '../components/controlMap-region-card-other.vue';
  import { lightApi } from '/@/api/business/light/light';
  import TableOperator from '/@/components/support/table-operator/index.vue';
  import { message } from 'ant-design-vue';
  const isAllSelected = ref(false);
  function toggleSelectAll(checked) {
    cardData.value.forEach((item) => {
      item.checked = checked;
    });

    if (checked) {
      selectedRowKeyList.value = cardData.value.map((item) => item.id);
    } else {
      selectedRowKeyList.value = [];
    }

    isAllSelected.value = checked;
  }
  const sliderValue = ref(0);
  const isSliderDis = computed(() => selectedRowKeyList.value.length === 0);
  const marks = {
    0: '0%',
    20: '20%',
    40: '40%',
    60: '60%',
    80: '80%',
    100: '100%',
  };
  function afterChangeTest(value) {
    brightness.value = value;
    if (selectedRowKeyList.value.length > 0) {
      openLight('set_light', value);
    }
  }
  const route = useRoute();
  let brightness = ref(undefined);
  const lampData = ref({});
  async function openLight(data) {
    console.log(data);

    if (data === 'switch_off') {
      brightness.value = 0;
      sliderValue.value = 0;
    }
    if (data === 'switch_on') {
      brightness.value = 60;
      sliderValue.value = 60;
    }

    try {
      await lightApi.controllerLamp({
        lampIds: selectedRowKeyList.value,
        controlType: data,
        brightness: brightness.value,
        switchFlag: true,
      });

      message.success('路灯调节操作成功');
      brightness.value = undefined;
      selectedRowKeyList.value = [];
      isAllSelected.value = false;
      await queryData();
    } catch (error) {
      console.error('操作失败:', error);
      message.error('路灯调节操作失败');
    }
  }

  // 选择表格行
  const selectedRowKeyList = ref([]);
  let selected = [];

  function onSelectChange(item) {
    const index = selected.findIndex((select) => select.id === item.id);
    if (selected[index].checked) {
      selected[index].checked = false;
      selectedRowKeyList.value = selectedRowKeyList.value.filter((id) => id !== item.id);
    } else {
      selected[index].checked = true;
      selectedRowKeyList.value.push(item.id);
    }
    isAllSelected.value = selected.every((i) => i.checked);
  }
  const userStore = useUserStore();

  // 分页相关
  const currentPgNum = ref(1);
  const hasMoreData = ref(true);
  const cardData = ref([]);
  const otherCardData = ref([]);
  const tableLoading = ref(false);
  const listContainer = ref(null);
  const otherListContainer = ref(null);

  // 查询表单
  const queryForm = reactive({
    pageNum: 1,
    pageSize: 500,
    regionId: route.query.regionId,
    regionName: route.query.regionName,
    deviceName: undefined,
    queryType: 'lamp',
  });

  // 查询数据
  async function queryData() {
    if (tableLoading.value) return;
    tableLoading.value = true;
    try {
      const result = await lightApi.regionDeviceInfo({
        ...queryForm,
      });
      cardData.value = result.data.list;
      selected = [];
      cardData.value.forEach((item) => {
        item.checked = false;
        selected.push({
          id: item.id,
          checked: false,
        });
      });
      const otherResultRes = await lightApi.regionDeviceInfo({
        ...queryForm,
        deviceName: undefined,
        queryType: 'other',
      });

      otherCardData.value = otherResultRes.data.list;
      hasMoreData.value = result.data.list.length >= queryForm.pageSize;
    } catch (e) {
      console.error('查询失败:', e);
    } finally {
      tableLoading.value = false;
    }
  }

  // 滚动加载处理
  const handleScroll = () => {
    const scrollElement = listContainer.value?.$el?.querySelector('.ant-list');
    if (!scrollElement || !hasMoreData.value || tableLoading.value) return;

    const { scrollTop, scrollHeight, clientHeight } = scrollElement;
    if (scrollHeight - (scrollTop + clientHeight) < 100) {
      loadMoreData();
    }
  };

  // 加载更多数据
  function loadMoreData() {
    currentPgNum.value += 1;
    queryData();
  }

  // 搜索
  function onSearch() {
    currentPgNum.value = 1;
    queryData();
  }

  // 重置
  function resetQuery() {
    queryForm.name = '';
    currentPgNum.value = 1;
    queryForm.deviceName = undefined;
    queryData();
  }

  // 返回
  function onClose() {
    userStore.closePage(route, router, '/controlMap');
    router.push({ path: '/controlMap' });
  }

  onMounted(async () => {
    const res = await lightApi.queryRegionLightSummaryByRegionId(route.query.regionId);
    lampData.value = res.data;
    queryData();

    nextTick(() => {
      const scrollElement = listContainer.value?.$el?.querySelector('.ant-list');
      if (scrollElement) {
        scrollElement.addEventListener('scroll', handleScroll);
      }
    });
  });

  onUnmounted(() => {
    const scrollElement = listContainer.value?.$el?.querySelector('.ant-list-items');
    if (scrollElement) {
      scrollElement.removeEventListener('scroll', handleScroll);
    }
  });
</script>

<style lang="less" scoped></style>
