<!--
  * 详情
  *
  * @Author:    骆伟林
  * @Date:      2025-04-07 21:36:15
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
  <a-card size="big" :bordered="false" :hoverable="true">
    <template #title>{{ route.query.isEdit==='false' ? '新增照明场景' : '编辑照明场景' }} </template>
    <template #extra>
      <a-space>
        <a-button @click="submit"  type="primary">提交</a-button>
        <a-button @click="onClose" type="primary">返回</a-button>
      </a-space>
    </template>
    <a-form ref="formRef" :rules="rules" :model="basicInfo" :label-col="{ span: 7 }" :wrapper-col="{ span: 11 }"> 
      <a-divider class="divider"><span>基本信息</span></a-divider>
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="标题" name="label">
            <a-input style="width: 100%" v-model:value="basicInfo.label"placeholder="标题"/>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="来源" name="source">
            <a-select v-model:value="basicInfo.source" style="width: 100%" placeholder="请选择来源">
            <a-select-option value="rule">用户自建</a-select-option>
            <a-select-option value="sys">系统自带</a-select-option>
</a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="简介" name="info">
            <a-textarea style="width: 100%" v-model:value="basicInfo.info"placeholder="简介"/>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="备注" name="remark">
            <a-textarea style="width: 100%" v-model:value="basicInfo.remark"placeholder="备注"/>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="是否禁用" name="disabled">
            <a-switch :checked="basicInfo.disabled === 1"checked-children="否"un-checked-children="是"@change="switchChange"/>
          </a-form-item>
        </a-col>
      </a-row>

      <a-divider class="divider"><span  style="margin-bottom: 20px;">照明配置</span></a-divider>
      <listConfigure @dataSource="getDataSource" :strategyList="strategyList"></listConfigure>
    </a-form>
  </a-card>
</template>

<script setup>
import { reactive, ref, nextTick, onMounted, computed, watch } from 'vue';
import { useRoute } from 'vue-router';
import { router } from '/@/router';
import { lightSceneApi } from '/@/api/business/light/scene/light-scene-api.js';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { smartSentry } from '/@/lib/smart-sentry';
// import listConfigure from './light-scene-configure.vue';
import { message } from 'ant-design-vue';
import listConfigure from './light-scene-configure.vue';
import { useUserStore } from '/@/store/modules/system/user';
import { fromPairs } from 'lodash';
function switchChange(checked) {
  basicInfo.value.disabled = checked===false? 1 : 0;
}

const userStore = useUserStore();
let dataSource=ref([])
function getDataSource(data){
  dataSource.value=data
}

const route = useRoute();
const basicInfo=ref({
    label: undefined,
    info: undefined,
    remark: undefined,
    disabled: 1,
    createTime:undefined,
    source:undefined
  }
)

const rules = {
      info: [{ required: true, message: '简介 必填' }],
      label: [{ required: true, message: '标题 必填' }],
      disabled: [{ required: true, message: '是否禁用 必填' }],
      id: [{ required: true, message: 'id 必填' }],
      remark: [{ required: true, message: '备注 必填' }],
  };

const strategyList=ref([])

  let form = reactive({
    basicInfo: {},
    strategyList:[]
  });
function onClose(){
  userStore.closePage(route, router, '/light-scene-list');
  router.push({path: '/light-scene-list',});
}
function submit(){
  form.basicInfo = basicInfo.value; 
  dataSource.value.map((item,index)=>{
    form.strategyList.push({
      id:basicInfo.value.id,
      createUserId:item.strategies[0].createUserId,
      updateUserId:item.strategies[0].updateUserId,
      createTime:null,
      updateTime:null,
      tenantId:item.strategies[0].tenantId,
      sceneId:basicInfo.value.id,
      strategyId:Number(item.strategies[0].id),
      strategyType:item.strategies[0].type,
      strategyLabel:item.strategies[0].label,
      startTime:item.createTime,
      endTime:item.endTime,
      recordStatus:item.strategies[0].recordStatus
    })
  })
  console.log(form);
  save()
}

async function  save() {
  try {
    if (basicInfo.value.id) {
      await lightSceneApi.set(form);
      } else {
        await lightSceneApi.add(basicInfo.value);
        const res=await lightSceneApi.queryPage(
          {
            pageNum:1,
            pageSize:500,
          }
        );
        console.log(res.data.list[res.data.list.length-1]);
        basicInfo.value.id=res.data.list[res.data.list.length-1].id;
        form.strategyList.forEach(item=>{ 
          item.sceneId=basicInfo.value.id;})
        await lightSceneApi.set(form);
      }
      message.success('操作成功');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  
}
onMounted(async() => {
  if (route.query.isEdit==='false') {
    return
  }
  else{
  const res=await lightSceneApi.detail(route.query.id)
  console.log(res);
  form=res.data
  basicInfo.value=res.data.basicInfo
  strategyList.value=res.data.strategyList
  }

});
</script>

<style lang="less" scoped>
.divider {
  height: 2px;
  background-color: #eee;
  margin-bottom: 25px;
  margin-top: 25px;
  span {
    display: block;
    border: 1px #eee solid;
    padding: 10px;
    background-color: #fff;
    border-radius: 10px;
    font-weight: 600;
    color: #1b9aee;
  }
}
</style>