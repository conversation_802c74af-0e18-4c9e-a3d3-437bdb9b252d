<!--
  * 品类表
  *
  * @Author:    戴松挺
  * @Date:      2025-03-23 17:17:51
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
  <a-modal :title="form.id ? '编辑' : '添加'" :width="700" :open="visibleFlag" @cancel="onClose" :maskClosable="false"
    :destroyOnClose="true">
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }">
      <a-form-item label="品类名称" name="categoryName">
        <a-input style="width: 100%" v-model:value="form.categoryName" placeholder="品类名称" />
      </a-form-item>
      <a-form-item label="品类行业" name="categoryIndustry">
        <DictSelect keyCode="1" placeholder="请输入品类行业" v-model:value="form.categoryIndustry" width="100%" />
      </a-form-item>
      <a-form-item label="品类场景" name="categoryScene">
        <DictSelect keyCode="2" placeholder="请输入品类场景" v-model:value="form.categoryScene" width="100%" />
      </a-form-item>
      <a-form-item label="品类图标" name="logo">
        <!-- <upload /> -->
        <FileUpload :defaultFileList="form.logo ? [{ fileUrl: form.logo }] : []" @change="fileChange" max-upload-size="1" />
      </a-form-item>
      <a-form-item label="发布状态" name="status">
        <!-- <a-input-number style="width: 100%" v-model:value="form.status" placeholder="数据状态，0：未发布，1：发布" /> -->
        <a-radio-group v-model:value="form.status">
          <a-radio :value="0">未发布</a-radio>
          <a-radio :value="1">发布</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="排序号" name="sort">
        <a-input-number style="width: 100%" v-model:value="form.sort" placeholder="排序号" />
      </a-form-item>
      <a-form-item label="品类说明" name="categoryDesc">
        <a-textarea style="width: 100%" v-model:value="form.categoryDesc" placeholder="请输入品类说明" />
      </a-form-item>
      <a-form-item label="引导说明" name="categoryGuide">
        <!-- <a-textarea style="width: 100%" v-model:value="form.categoryGuide" placeholder="请输入引导说明" /> -->
        <SmartWangeditor ref="contentRef" :modelValue="form.contentHtml" :height="300" />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { reactive, ref, nextTick } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { iotCategoryApi } from '/@/api/business/iot/category/iot-category-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  import FileUpload from '/@/components/support/file-upload/index.vue';
  import { FILE_FOLDER_TYPE_ENUM } from '/@/constants/support/file-const';
  import DictSelect from '/@/components/support/dict-select/index.vue';// 字典下拉框
  import SmartWangeditor from '/@/components/framework/wangeditor/index.vue';


  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show(rowData) {
    console.log("rowData", rowData)
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
    }
    // 使用字典时把下面这注释修改成自己的字典字段 有多个字典字段就复制多份同理修改 不然打开表单时不显示字典初始值
    // if (form.status && form.status.length > 0) {
    //   form.status = form.status.map((e) => e.valueCode);
    // }
    visibleFlag.value = true;
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
      categoryIndustry: undefined, //品类行业
      categoryScene: undefined, //品类场景
      categoryName: undefined, //品类名称
      categoryDesc: undefined, //品类说明
      categoryGuide: undefined, //引导说明
      logo: undefined, //品类图标
      sort: undefined, //排序号
      status: undefined, //数据状态，0：未发布，1：发布
      id: undefined, //id
  };

  let form = reactive({ ...formDefault });

  const rules = {
      categoryIndustry: [{ required: true, message: '品类行业 必填' }],
      categoryScene: [{ required: true, message: '品类场景 必填' }],
      categoryName: [{ required: true, message: '品类名称 必填' }],
      status: [{ required: true, message: '数据状态，0：未发布，1：发布 必填' }],
  };

  function fileChange(file) {
    form.logo = file[0]?.fileUrl || "";
  }
  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      // console.log("form.id".form.id)
      if (form.id) {
        console.log("编辑")
        await iotCategoryApi.update(form);
      } else {
        console.log("新建")
        await iotCategoryApi.add(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  defineExpose({
    show,
  });
</script>
