/**
 * 设备表 枚举
 *
 * @Author:    李帅兵
 * @Date:      2025-03-21 22:21:07
 * @Copyright  中山睿数信息技术有限公司 2025
 */

export const DEVICE_TYPE = {
  DIRECT: {
    value: 'device',
    desc: '设备',
  },
  GATEWAY: {
    value: 'gateway',
    desc: '网关设备',
  },
};

export const DEVICE_STATUS_ENUM = {
  NOT_ACTIVE: {
    value: 0,
    desc: '未激活',
    color: 'gray',
  },
  ONLINE: {
    value: 1,
    desc: '在线',
    color: 'green',
  },
  OFFLINE: {
    value: 2,
    desc: '离线',
    color: 'orange',
  },
  STOP_USING: {
    value: 4,
    desc: '停用',
    color: 'red',
  },
};
// 是否启用
export const DEVICE_ENUM = {
  yes: {
    value: 1,
    desc: '否',
  },
  no: {
    value: 2,
    desc: '是',
  },
};

/**
 * 根据设备类型value获取对应的描述
 * @param {string} value - 设备类型值
 * @returns {string} 返回设备类型描述，如果未找到则返回空字符串
 */
export const getDeviceTypeDesc = (value) => {
  const type = Object.values(DEVICE_TYPE).find((item) => item.value === value);
  return type?.desc || '';
};
//根据设备状态value获取对应的描述
export const getDeviceStatusEnumDesc = (value) => {
  const type = Object.values(DEVICE_STATUS_ENUM).find((item) => item.value === value);
  return {
    desc: type?.desc || '',
    color: type?.color || '',
  };
};
//根据设备启用状态value获取对应的描述
export const getDeviceEnumDesc = (value) => {
  const type = Object.values(DEVICE_ENUM).find((item) => item.value === value);
  return type?.desc || '';
};

export default {
  DEVICE_TYPE,
  DEVICE_STATUS_ENUM,
  DEVICE_ENUM,
};
