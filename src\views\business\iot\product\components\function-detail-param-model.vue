<!--
  * 功能参数模态框
  *
  * @Author:    潘显镇
  * @Date:      2025-05-01
-->
<template>
  <a-modal
    :title="'参数详情'"
    :width="500"
    :open="visible"
    @cancel="onClose"
    :destroyOnClose="true"
  >
    <a-form :model="form" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" disabled>
      <a-form-item label="参数名称">
        <a-input v-model:value="form.name" placeholder="请输入参数名称" />
      </a-form-item>
      <a-form-item label="标识符">
        <a-input v-model:value="form.identifier" placeholder="请输入标识符" />
      </a-form-item>
      <a-form-item label="数据类型">
        <a-input :value="`${form.fieldType}:${dataTypeDesc}`"  />
      </a-form-item>
      <a-form-item label="最小值">
        <a-input-number v-model:value="form.specMin" placeholder="请输入最小值" style="width: 100%" />
      </a-form-item>
      <a-form-item label="最大值">
        <a-input-number v-model:value="form.specMax" placeholder="请输入最大值" style="width: 100%" />
      </a-form-item>
      <a-form-item label="步长">
        <a-input-number v-model:value="form.specStep" placeholder="请输入步长" style="width: 100%" />
      </a-form-item>
      <a-form-item label="单位">
        <a-input v-model:value="form.specUnitName" placeholder="请输入单位" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { ref, reactive, computed } from 'vue';
  import { getDataTypeDesc } from '/@/constants/business/iot/product/iot-product-const.js';

  // 是否显示模态框
  const visible = ref(false);
  
  // 表单数据
  const form = reactive({});

  // 计算属性：获取数据类型描述
  const dataTypeDesc = computed(() => {
    return getDataTypeDesc(form.fieldType);
  });

  // 关闭模态框
  const onClose = () => {
    visible.value = false;
  };

  // 显示模态框并接收参数
  const show = (data) => {
    Object.assign(form,data)
    visible.value = true;
  };

  // 对外暴露方法
  defineExpose({
    show
  });
</script>
