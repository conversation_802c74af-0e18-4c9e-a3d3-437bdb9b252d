<!--
  * 设备管理-区域搜索
  *
  * @Date:      2024-07-13
-->
<template>
    <div class="search-drawer">
      <div class="search-header">
        <div class="all-region">
            <a-button 
            type="primary" 
            ghost 
            block 
            style="margin-bottom: 12px"
            @click="handleSelectAll"
            >
            <global-outlined /> 全部区域
            </a-button>
        </div>
        <a-input-search v-model:value="search" placeholder="输入区域名称搜索">
          <template #enterButton>
            <a-button type="primary" @click="handleSearch"> 搜索
            </a-button>
          </template>
        </a-input-search>
      </div>
      <a-list 
        size="small" 
        bordered 
        :data-source="searchList" 
        :loading="loading"
        style="margin-top: 10px; height: calc(100% - 50px); overflow-y: auto;"
      >
        <template #renderItem="{ item }">
          <a-list-item @click="handleItemClick(item)">
            <a-card :bordered="false" hoverable style="width: 100%;">
              <div class="card-content" :title="item.name">
                <div class="card-header">
                  <span style="margin-right: 5px;">{{ item.name }}</span>
                  <span style="font-size: 14px;">{{ item.address }}</span>
                </div>
                <div class="card-footer">
                  <span style="font-size: 13px;  margin-right: 10px;">{{ item.contact }}</span>
                  <span style="font-size: 13px; ">{{ item.phone }}</span>
                </div>
              </div>
            </a-card>
          </a-list-item>
        </template>
      </a-list>
    </div>
  </template>
  
  <script setup>
  import { ref ,onMounted} from 'vue';
  import { message } from 'ant-design-vue';
  import { iotRegionApi } from '/@/api/business/iot/region/iot-region-api';
  
  // 定义组件对外暴露的事件
  const emit = defineEmits(['selectRegion']);
  
  // 搜索相关变量
  let search = ref('');
  let searchList = ref([]);
  let loading = ref(false);
  
  // 处理搜索
  async function handleSearch() {
    
    loading.value = true;
    try {
      // 调用API搜索区域
      const params = {
        name: search.value,
        "pageNum": 1,
        "pageSize": 50
      };
      
      const result = await iotRegionApi.queryPage(params);
      
      if (result && result.data && result.data.list) {
        searchList.value = result.data.list;
      } else {
        searchList.value = [];
        message.info('未找到相关区域');
      }
    } catch (error) {
      console.error('区域搜索失败:', error);
      message.error('搜索失败，请稍后重试');
      searchList.value = [];
    } finally {
      loading.value = false;
    }
  }
  
  // 处理区域项点击
  function handleItemClick(item) {
    // 将选中的区域信息传递给父组件
    emit('selectRegion', item);
  }
  //处理全部区域
  function handleSelectAll() {
    emit('selectRegion', null);
  }
  onMounted(()=>{
    handleSearch();
  })
  </script>
  
  <style scoped>
  .search-drawer {
    padding: 10px;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  .search-header {
    margin-bottom: 10px;
  }
  .all-region {
  margin-bottom: 12px;
  }
  .card-content {
    width: 100%;
    overflow: hidden;
  }
  
  .card-header {
    display: flex;
    align-items: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 8px;
  }
  
  .card-footer {
    display: flex;
    align-items: center;
    color: #999;
  }
  </style>