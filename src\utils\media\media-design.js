/**
 * 创建可写文件流用于保存文件
 * @param {string} extName - 文件扩展名，默认为mp4
 * @returns {Promise<FileSystemWritableFileStream>} 可写文件流
 */
export async function createFileWriter(extName = 'mp4') {
    const fileHandle = await window.showSaveFilePicker({
        suggestedName: `WebAV-export-${Date.now()}.${extName}`,
    });
    return fileHandle.createWritable();
}

/**
 * 加载本地文件
 * @param {Object} accept - 接受的文件类型，如 {'video/*': ['.mp4', '.mov']}
 * @returns {Promise<File>} 文件对象
 */
export async function loadFile(accept) {
    try {
        const [fileHandle] = await window.showOpenFilePicker({
            types: [{ accept }],
        });
        return fileHandle.getFile();
    } catch (error) {
        console.error('加载文件失败:', error);
        throw error;
    }
}