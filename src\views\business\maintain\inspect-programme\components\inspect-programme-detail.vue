<!--
  * 巡检方案表单
  *
  * @Author:    潘显镇
  * @Date:      2025-04-11
-->
<template>
  <a-modal :title="'巡检方案详情'" :width="1200" :open="visibleFlag" @cancel="onClose" :maskClosable="false"
    :destroyOnClose="true" :footer="null">
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 6 }" disabled>
      <a-row>
        <a-col :span="12">
          <a-form-item label="方案名称" name="programmeName">
            <a-input style="width: 100%" v-model:value="form.programmeName" placeholder="请输入巡检方案名称" />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="巡检频率" name="inspectionFrequency">
            <a-select style="width: 100%" v-model:value="form.inspectionFrequency" placeholder="请选择巡检频率"
              :options="TIME_UNIT_ENUM.getOptions()" />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="频次(天)" name="dailyInspectionCount" v-if="form.inspectionFrequency === 'day'">
            <a-input-number style="width: 100%" v-model:value="form.dailyInspectionCount" placeholder="请输入次数" />
          </a-form-item>
          <a-form-item label="频次(周)" name="dailyInspectionCount" v-if="form.inspectionFrequency === 'week'">
            <a-select style="width: 100%" v-model:value="form.ruleValues" placeholder="请选择星期" mode="multiple"
              :max-tag-count="2" :options="WEEK_DAY_ENUM.getOptions()" />

          </a-form-item>
          <a-form-item label="频次(月)" name="dailyInspectionCount" v-if="form.inspectionFrequency === 'month'">
            <a-select style="width: 100%" v-model:value="form.ruleValues" placeholder="请选择日期" mode="multiple"
              :max-tag-count="2" :options="dayOptions" />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="状态" name="enabledFlag">
            <a-select style="width: 100%" v-model:value="form.enabledFlag" placeholder="请选择状态">
              <a-select-option :value="true">启用</a-select-option>
              <a-select-option :value="false">停用</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="方案描述" name="description">
            <a-textarea v-model:value="form.description" placeholder="请输入方案描述" :rows="2" />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 巡检设备列表 -->
      <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane tab="巡检设备列表" key="device">
          <a-table :columns="deviceColumns" :data-source="form.deviceList" :pagination="false" size="small" bordered
            rowKey="deviceId">
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.dataIndex === 'index'">
                {{ index + 1 }}
              </template>
              <template v-if="column.dataIndex === 'device'">
                <iotDeviceSelect v-model:value="record.deviceId" style="width: 100%" />
              </template>
              <template v-if="column.dataIndex === 'ruleDate'">
                <a-date-picker v-model:value="record.ruleDate" style="width: 100%" placeholder="选择规定日期"
                  valueFormat="YYYY-MM-DD" />
              </template>
            </template>
          </a-table>
        </a-tab-pane>

        <!-- 巡检项目列表-->
        <a-tab-pane tab="巡检项目列表" key="item">
          <a-table :columns="itemColumns" :data-source="form.itemList" :pagination="false" size="small" bordered>
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.dataIndex === 'index'">
                {{ index + 1 }}
              </template>
              <template v-if="column.dataIndex === 'itemName'">
                <a-input v-model:value="record.itemName" placeholder="请输入检查项" />
              </template>
              <template v-if="column.dataIndex === 'inspectionMethod'">
                <a-input v-model:value="record.inspectionMethod" placeholder="请输入检查方法" />
              </template>
              <template v-if="column.dataIndex === 'minValue'">
                <a-input-number v-model:value="record.minValue" placeholder="最小值" style="width: 100%" />
              </template>
              <template v-if="column.dataIndex === 'maxValue'">
                <a-input-number v-model:value="record.maxValue" placeholder="最大值" style="width: 100%" />
              </template>
              <template v-if="column.dataIndex === 'description'">
                <a-input v-model:value="record.description" placeholder="请输入描述" />
              </template>
            </template>
          </a-table>
        </a-tab-pane>
      </a-tabs>
    </a-form>
  </a-modal>
</template>

<script setup>
import { reactive, ref, nextTick } from 'vue';
import _ from 'lodash';
import { message } from 'ant-design-vue';
import IotDeviceSelect from '/@/components/business/iot/iot-device-select/index.vue';
import { inspectProgrammeApi } from '/@/api/business/maintain/inspect-programme/inspect-programme-api.js';
import { smartSentry } from '/@/lib/smart-sentry';
import { TIME_UNIT_ENUM } from '/@/constants/business/ops/inspect-programme-const';
import {WEEK_DAY_ENUM} from '/@/constants/week-const.js';
// ------------------------ 事件 ------------------------
const emits = defineEmits(['reloadList']);
const activeKey = ref('device');
// ------------------------ 显示与隐藏 ------------------------
const visibleFlag = ref(false);

// ------------------------ 表单 ------------------------
const formDefault = {
  id: undefined,           // 主键ID
  programmeName: undefined,  // 方案名称
  inspectionFrequency: 'day', // 巡检频率
  dailyInspectionCount: undefined, // 巡检次数
  enabledFlag: true,           // 启用状态，默认启用
  description: undefined, // 方案描述
  ruleValues: [], // 规定值
  itemList: [],       // 巡检项列表
  deviceList: [], // 设备列表
};

const formRef = ref();

let form = reactive({ ...formDefault });

async function show(id) {
    visibleFlag.value = true;
    try{
    const res = await inspectProgrammeApi.getById(id);
    console.log('res', res);
    Object.assign(form, res.data);
    }catch (error) {
      smartSentry.error(error);
      message.error('获取巡检方案详情失败！');
    }
}

function onClose() {
  Object.assign(form, formDefault);
  visibleFlag.value = false;
}

// ------------------------ 设备相关方法 ------------------------

// 设备表格列定义
const deviceColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 60,
    align: 'center'
  },
  {
    title: '设备',
    dataIndex: 'device',
    width: 300
  },
  {
    title: '规则日期',
    dataIndex: 'ruleDate',
    width: 200
  },
];

// 巡检项目表格列定义
const itemColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 60,
    align: 'center'
  },
  {
    title: '检查项',
    dataIndex: 'itemName'
  },
  {
    title: '检查方法',
    dataIndex: 'inspectionMethod'
  },
  {
    title: '最小值',
    dataIndex: 'minValue',
    width: 120
  },
  {
    title: '最大值',
    dataIndex: 'maxValue',
    width: 120
  },
  {
    title: '描述',
    dataIndex: 'description'
  },
];

defineExpose({
  show,
});
</script>