<template>
  <div ref="borderBoxContainer" class="border-box-container">
    <Decoration7 class="top-left-decoration" :color="['#18abd9', '#1ba6d0']">
      <text style="margin:1vw;">工单处理进度</text>
    </Decoration7>
    <div class="border-box-content">
      <BorderBox10 :color="['#3896b3', '#329bbb']">
        <div class="content">
          <ScrollBoard :config="scrollConfig" class="scroll-board" />
        </div>
      </BorderBox10>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { BorderBox10, Decoration7, ScrollBoard } from '@kjgl77/datav-vue3';

const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
});

// -----------------------------------------------------数据处理
// 转换数据格式
const tableData = computed(() => {
  return props.list.map((item) => {
    return [
      item.orderNumber || '-',
      item.regionName || '-',
      item.maintainer || '-',
      getStatusText(item.status),
    ];
  });
});

// 处理状态映射
function getStatusText(status){
  switch (status) {
    case 0:
      return '<span style="color: red;">未查阅</span>';
    case 1:
      return '<span style="color: #b0f54a;">已查阅</span>';
    case 2:
      return '<span style="color: #f3cc0b;">已出工</span>';
    case 3:
      return '<span style="color: #8045f2;">维修中</span>';
    case 4:
      return '<span style="color: #f69d67;">已维修</span>';
    default:
      return '<span style="color: gray;">未知</span>';
  }
}
// -----------------------------------------------------轮播表格配置
const scrollConfig = computed(() => {
  return {
    header: [
      '<span style="color:#0eeaea;">工单编号</span>',
      '<span style="color:#0eeaea;">区域名</span>',
      '<span style="color:#0eeaea;">维修人</span>',
      '<span style="color:#0eeaea;">状态</span>',
    ],
    data: tableData.value,
    align: ['center', 'center', 'center', 'center'],
    evenRowBGC: '#0d3f4d',
    oddRowBGC: '#0a313c',
    headerBGC: '#075364',
    headerHeight: 40,
    columnWidth: [180, 190, 90, 90],
    waitTime: 2000,
    rowNum: 3,
    carousel: 'single',
  };
});

// -----------------------------------------------------边框容器
const borderBoxContainer = ref(null);

onMounted(() => {
  if (!borderBoxContainer.value) {
    console.error('BorderBox container ref not exist');
    return;
  }
  console.log('BorderBox container mounted:', borderBoxContainer.value);
});
</script>

<style scoped>
.border-box-container {
  position: absolute;
  top: 65vh;
  left: 1vw;
  width: 25vw;
  height: 27vh;
  z-index: 1000;
}

.top-left-decoration {
  position: absolute;
  color: white;
  font-size: 1.1vw;
  height: 3vh;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.border-box-content {
  position: absolute;
  top: 4.5vh;
  left: 0;
  width: 100%;
  height: calc(100% - 3vh);
}

.content {
  width: 100%;
  height: 100%;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
}

.scroll-board {
  width: 100%;
  height: 100%;
}
</style>