/**
 * 设备区域 api 封装
 *
 * @Author:    李帅兵
 * @Date:      2025-03-22 17:51:27
 * @Copyright  中山睿数信息技术有限公司 2025
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const iotRegionApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/iotRegion/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/iotRegion/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/iotRegion/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/iotRegion/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
      return postRequest('/iotRegion/batchDelete', idList);
  },
  

  /**
   * 查询区域下的设备列表
   */
  queryDeviceByArea: (param) => {
    return postRequest('/iotDevice/queryDeviceByRegion', param);
  },
  /**
   * 分页查询未分配区域的设备 
   */
  queryUnSetRegionDevicePage: () => {
    return postRequest('/iotDevice/queryUnSetRegionDevicePage');
  },

  /**
   * 设置区域内设备/iotRegion/unsetDevice
   */
  setRegionDevice: (param) => {
    return postRequest('/iotRegion/setDevice', param);
  },
  /**
   * 解绑区域内设备
   */
  unsetRegionDevice: (param) => {
    return postRequest('/iotRegion/unsetDevice', param);
  }
};
