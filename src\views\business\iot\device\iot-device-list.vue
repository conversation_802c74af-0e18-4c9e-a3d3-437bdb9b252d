<!--
  * 设备管理
  *
  * @Author:    冯斌杰
  * @Date:      2025-02-11 15:38:08
  * @Copyright  2025 电子科技大学中山学院大数据与智能计算实验室
-->
  
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item class="custom-label-form-item smart-query-form-item">
        <template #label>
          <div class="label-container">
            <span>设备名称</span>
            <a-tooltip 
              title="英文字母，并可包含数字、下划线（_）、中划线（-）、点号（.）、半角冒号（:）以及特殊字符@，整体长度需控制在4至32个字符之间" 
              placement="topLeft"
              class="label-tooltip"
            >
              <question-circle-outlined />
            </a-tooltip>
          </div>
        </template>
        <a-input style="width: 155px;" v-model:value="queryForm.deviceName" placeholder="请输入设备名称" />
      </a-form-item>
      <a-form-item class="custom-label-form-item smart-query-form-item">
        <template #label>
          <div class="label-container">
            <span>唯一编号</span>
            <a-tooltip 
              title="设备唯一信息编号，比如mac地址，sn码等，标识设备唯一性" 
              placement="topLeft"
              class="label-tooltip"
            >
              <question-circle-outlined />
            </a-tooltip>
          </div>
        </template>
        <a-input style="width: 155px;" v-model:value="queryForm.uniqueNo" placeholder="请输入唯一编号" />
      </a-form-item>
      <a-form-item class="custom-label-form-item smart-query-form-item">
        <template #label>
          <div class="label-container">
            <span>所属产品</span>
            <a-tooltip 
              title="可选择已发布的产品数据，将设备绑定后继承其物模型" 
              placement="topLeft"
              class="label-tooltip"
            >
              <question-circle-outlined />
            </a-tooltip>
          </div>
        </template>
        <a-input style="width: 155px;"  placeholder="请选择所属产品" @click="showProductForm" v-if="queryForm.productName == undefined" />
        <a-input style="width: 155px;"  placeholder="请选择所属产品" @click="showProductForm" :value="`${queryForm.productName} : ${queryForm.productKey}`" v-else />
      </a-form-item>
      <a-form-item label="设备类型" class="smart-query-form-item" v-show="!isCollapsed">
<!--        <DictSelect keyCode="10" placeholder="请选择设备类型" v-model:value="queryForm.deviceType" width="155px" />-->
        <SmartEnumSelect width="120px" v-model:value="queryForm.deviceType" placeholder="请选择设备类型" enum-name="DEVICE_TYPE_ENUM" />
      </a-form-item>
      <a-form-item label="是否启用" class="smart-query-form-item" v-show="!isCollapsed">
<!--        <DictSelect keyCode="9" placeholder="请选择是否启用" v-model:value="queryForm.status" width="155px" />-->
        <SmartEnumSelect width="120px" v-model:value="queryForm.status" placeholder="请选择是否启用" enum-name="STATUS_ENUM" />
      </a-form-item>
      <a-form-item label="设备状态" class="smart-query-form-item" v-show="!isCollapsed">
<!--        <DictSelect keyCode="8" placeholder="请选择设备状态" v-model:value="queryForm.deviceStatus" width="155px" />-->
        <SmartEnumSelect width="120px" v-model:value="queryForm.deviceStatus" placeholder="请选择是否启用" enum-name="DEVICE_STATUS_ENUM" />
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="onSearch">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
        <a-button @click="toggleCollapse" class="smart-margin-left16" style="margin-left: 10px">
          <template #icon>
            <MenuFoldOutlined v-if="!isCollapsed" />
            <MenuUnfoldOutlined v-else />
          </template>
          {{ isCollapsed ? "展开" : "收起" }}
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>

  <a-card size="small" :bordered="false" :hoverable="true">
      <!-- 表格操作行 begin -->
      <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
          <a-button @click="showForm()" type="primary" size="small">
          <template #icon>
              <PlusOutlined />
          </template>
          新建
          </a-button>
          <a-button @click="confirmBatchDelete" type="primary" danger size="small" :disabled="selectedRowKeyList.length == 0">
          <template #icon>
              <DeleteOutlined />
          </template>
          批量删除
          </a-button>
      </div>
      <div class="smart-table-setting-block">
          <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
      </div>
      </a-row>
      
      <!---------- 表格操作行 end ----------->

      <a-list :grid="{ gutter: 10, xs: 1, sm: 1, md: 2, lg: 2, xl: 3, xxl: 4 }" :data-source="data" :loading="initLoading">
          <template #renderItem="{ item, index }">
              <a-list-item>
              <a-card
                  hoverable
                  :loading="loading"
                  size="default"
                  :style="
                  item.status == 1
                      ? 'background: linear-gradient(rgba(88, 158, 255, 0.1), white)'
                      : item.status == 0
                      ? 'background: linear-gradient(rgba(128, 128, 128, 0.1), white)'
                      : 'background: linear-gradient(rgba(128, 128, 128, 0.1), white)'
                  "
              >
                  <template #title>
                  <a  class="detail">{{ item.deviceName }}</a>
                  </template>
                  <template #extra>
                  <div style="width: inherit; position: absolute; right: 30px; top: 15px">No{{ index + 1 }}</div>
                  <a-checkbox v-model:checked="item.checked" style="position: absolute; right: 5px; top: 3px" @change="onSelectChange(item)"
                  /></template>
                  <div style="height: 100%; width: 100%; display: flex;" >
                    <div style="flex: 1; display: flex; flex-direction: column;z-index: 2;">
                        <span class="span-multiline-ellipsis">备注名称：{{ item.deviceNoteName }}</span>
                        <span class="span-multiline-ellipsis">所属产品：{{ item.productKey }}</span>
                        <span class="span-multiline-ellipsis"
                        >设备类型：<span class="multiline-ellipsis">{{ findDescByValue(item.deviceType, DEVICE_TYPE) }}</span></span
                        >
                        <span class="span-multiline-ellipsis"
                        >是否启用：
                        <a-switch 
                            :checked="item.status === 1" checked-children="否" un-checked-children="是" @change="switchChange(item)"
                        />
                         </span>
                        <span class="span-multiline-ellipsis"
                        >设备状态：{{getDeviceStatusEnumDesc(item.deviceStatus).desc}}
                          </span>
                        <span class="span-multiline-ellipsis">所属区域名称：{{ item.regionName }}</span>
                        <span class="span-multiline-ellipsis">最后上线时间：{{ item.lastOnlineTime }}</span>
                    </div>
                    <div style="flex: 1; display: flex; justify-content: center; align-items: center; z-index: 1;">
                        <img src="/@/assets/images/product/icon2.svg" alt="" style="max-width: 130px; max-height: 130px; object-fit: contain" />
                    </div>
                  </div>
                  <template #actions>
                  <span style="color: #108ee9" @click="showDetail(item)"><setting-outlined key="setting" /> 配置</span>
                  <span style="color: #108ee9" @click="showForm(item)"><FormOutlined /> 编辑</span>
                  <span style="color: #108ee9" @click="showDevice(item)"  v-if="findDescByValue(item.deviceType, DEVICE_TYPE) === '网关设备'"><MobileOutlined /> 管理设备</span>
                  <span style="color: #f56c6c" @click="onDelete(item)"><DeleteOutlined /> 删除</span>
                  </template>
              </a-card>
              </a-list-item>
          </template>
      </a-list>
      <div class="smart-query-table-page">
          <a-pagination
              showSizeChanger
              showQuickJumper
              show-less-items
              :pageSizeOptions="PAGE_SIZE_OPTIONS"
              :defaultPageSize="queryForm.pageSize"
              v-model:current="queryForm.pageNum"
              v-model:pageSize="queryForm.pageSize"
              :total="total"
              @change="queryData"
              @showSizeChange="queryData"
              :show-total="(total) => `共${total}条`"
          />
      </div>

      <iotDeviceForm ref="formRef" @reloadList="queryData" />
      <chooseProductForm ref="chooseProductFormRef" @emitSelectedProduct="handleProductSelected" />
      <!-- <EquipmentInstanceDetail ref="detailRef" /> -->
  </a-card>
</template>
<script setup>
  import { reactive, ref, onMounted, watch } from 'vue';
  import { message, Modal } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import TableOperator from '/@/components/support/table-operator/index.vue';
  import { iotDeviceApi } from '/@/api/business/iot/device/iot-device-api.js';
  import iotDeviceForm from './components/iot-device-form.vue';
  import chooseProductForm from './components/choose-product-form.vue';
  import { DEVICE_TYPE } from '/@/constants/business/iot/device/iot-device-const.js';
  import { useRouter,useRoute } from 'vue-router';
  import SmartEnumSelect from '/@/components/framework/smart-enum-select/index.vue';
  import { getDeviceStatusEnumDesc } from '/@/constants/business/iot/device/iot-device-const.js'//getDeviceStatusEnumDesc
  // ---------------------------- 表格列 ----------------------------
  const columns = []//? 待定
  const initLoading = ref(false);
  const loading = ref(false);
  const router = useRouter();
  const state = reactive({
    checked1: true,
  });

  // ---------------------------- 查询数据表单和方法 ----------------------------

  const queryFormState = {
    deviceName: undefined,
    uniqueNo: undefined,
    productKey: undefined,
    deviceStatus: undefined,
    deviceType: undefined,
    status: undefined,
    productName: undefined,
    pageNum: 1,
    pageSize: 9,
    sortItemList: [{ isAsc: false, column: 'create_time' }],
  };
  // 查询表单form
  const queryForm = reactive({ ...queryFormState });
  // 表格数据
  const data = ref([]);
  // 总数
  const total = ref(0);
   // 重置查询条件
function resetQuery() {
  let pageSize = queryForm.pageSize;
  Object.assign(queryForm, queryFormState);
  queryForm.pageSize = pageSize;
  queryData();
}

// 搜索
function onSearch() {
  console.log(queryForm,"查询")
  queryForm.pageNum = 1;
  queryData();
}

const route = useRoute();
// 查询数据
async function queryData() {
  initLoading.value = true;
  loading.value = true;
  try {
    queryForm.productKey = route.query?.productKey;
    let queryResult = await iotDeviceApi.queryPage(queryForm);
    total.value = queryResult.data.total;
    data.value = queryResult.data.list;
    selected = [];
    data.value.forEach((item) => {
      selected.push({
        id: item.id,
        checked: false,
      });
    });
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    initLoading.value = false;
    loading.value = false;
  }
}

watch(() => route.query, (newQuery) => {
  queryData();
});

onMounted(queryData);

// ---------------------------- 添加/修改 ----------------------------
const formRef = ref();
const detailRef = ref();
const chooseProductFormRef = ref();

function showProductForm() {
  console.log("打开from表单")
  chooseProductFormRef.value.show();
}


function showForm(data) {
  console.log("打开from表单")
  console.log(data);
  
  formRef.value.show(data);
}

function showDetail(data) {
    router.push({
      path: '/iot/device/detail', // 跳转到品类详情页
      query: {
        productKey: data.productKey,
        deviceName: data.deviceName,
        id: data.id,
      },
    });
  }

  function showDevice(data) {
    router.push({
      path: '/iot/device/detail', 
      query: {
        key: data.productKey,
        name: data.deviceName,
        active: '6',
      },
    });
  }

async function switchChange(data) {
  if (!data.id) {
    return;
  }
  try {
    console.log("调用修改接口啦前",data.status)
    data.status = data.status === 1 ? 2 : 1;
    console.log("调用修改接口啦后",data.status)
    await iotDeviceApi.update(data);
    message.success('操作成功');
    queryData();
  } catch (err) {
    smartSentry.captureError(err);
  }
}

// ---------------------------- 单个删除 ----------------------------
//确认删除
function onDelete(data) {
  Modal.confirm({
    title: '提示',
    content: '确定要删除选吗?',
    okText: '删除',
    okType: 'danger',
    onOk() {
      requestDelete(data);
    },
    cancelText: '取消',
    onCancel() {},
  });
}

//请求删除
async function requestDelete(data) {
  console.log("调用删除接口啦")
  SmartLoading.show();
  try {
    let deleteForm = {
      goodsIdList: selectedRowKeyList.value,
    };
    await iotDeviceApi.delete(data.id);
    message.success('删除成功');
    queryData();
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    SmartLoading.hide();
  }
}

// ---------------------------- 批量删除 ----------------------------

// 选择表格行
const selectedRowKeyList = ref([]);
let selected = [];

function onSelectChange(item) {
  const index = selected.findIndex((select) => select.id === item.id);
  if (selected[index].checked) {
    selected[index].checked = false;
    selectedRowKeyList.value = selectedRowKeyList.value.filter((id) => id !== item.id);
  } else {
    selected[index].checked = true;
    selectedRowKeyList.value.push(item.id);
  }
}

// 批量删除
function confirmBatchDelete() {
  Modal.confirm({
    title: '提示',
    content: '确定要批量删除这些数据吗?',
    okText: '删除',
    okType: 'danger',
    onOk() {
      requestBatchDelete();
    },
    cancelText: '取消',
    onCancel() {},
  });
}

//请求批量删除
async function requestBatchDelete() {
  console.log("调用批量删除接口啦")
  try {
    SmartLoading.show();
    await iotDeviceApi.batchDelete(selectedRowKeyList.value);
    message.success('删除成功');
    queryData();
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    SmartLoading.hide();
  }
}

//返回键值
function findDescByValue(value, enumObject) {
  // 遍历枚举对象的键值对
  for (const key in enumObject) {
      if (enumObject[key].value === value) {
      return enumObject[key].desc; // 找到对应的描述后返回
      }
  }
  return null; // 如果找不到对应的值，则返回 null
}
// 收缩，展开
const isCollapsed = ref(false);
const toggleCollapse = () => {
isCollapsed.value = !isCollapsed.value;
};


// 接受子组件数据
function handleProductSelected(selectedProduct) {
console.log('父组件接收到的产品信息:', selectedProduct);
// 在这里可以处理 selectedProduct，例如保存到状态中
queryForm.productKey = selectedProduct.productKey;
queryForm.productName = selectedProduct.productName;
}


//打开传输框
const modalRef = ref(null);

const openModal = () => {
  if (modalRef.value) {
    modalRef.value.Form();
  } else {
    console.error('组件引用未找到!');
  }
};
</script>

<style scoped lang="less">
  :deep(.ant-card-body) {
    padding: 10px 20px;
  }
  .scroll-container {
    height: 580px; /* 设置容器的高度 */
    overflow-y: auto; /* 启用 y 轴滚动 */
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  ::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 8px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
  .span-multiline-ellipsis {
    // display: flex;
    // width: 230px;
    // margin: 5px;
    display: -webkit-box;          /* Flexbox 模式 */
    -webkit-box-orient: vertical; /* 设置盒子为垂直方向 */
    overflow: hidden;          /* 隐藏多余内容 */
    text-overflow: ellipsis;      /* 增加省略号 */
    -webkit-line-clamp: 1;        /* 限制显示2行，多出的内容隐藏 */
    max-width: 100%;              /* 设置最大宽度 */
    line-height: 1.5;             /* 设置行高（根据需要调整） */
    max-height: calc(1.5em * 2);  /* 与行高和行数匹配 */
    word-break: break-word;       /* 防止单词溢出容器 */
    font-size: 0.8em;              /* 设置字体大小 */
    margin-bottom: 10px;           /* 增加下边距 */
  }

  .detail{
    display: inline-block;
    padding: 5px 10px;
    background-color: rgba(88, 158, 255, 0.1); /* 设置背景颜色为淡蓝色 */
    border: 1px solid rgba(88, 158, 255, 0.1); /* 边框颜色 */
    border-radius: 8px; /* 圆角 */
    color: #2c77f1; /* 字体颜色 */
    font-size: 16px; /* 字体大小 */
    text-align: center; /* 文字居中 */
    font-weight: bold; /* 加粗字体 */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
  }

  .offline-btn {
    display: inline-flex;
    align-items: center;
    padding: 5px 10px;
    background-color: #FDEDED; /* 浅红背景 */
    border: 1px solid transparent; /* 无边框 */
    border-radius: 5px; /* 圆角效果 */
    color: #F56C6C; /* 红色文字 */
    font-size: 14px; /* 字体大小 */
    cursor: pointer;
  }

  .offline-btn .icon {
    display: inline-block;
    width: 18px;
    height: 18px;
    margin-right: 5px; /* 图标和文字间距 */
    background-color: #F56C6C; /* 红色圆圈背景 */
    color: #ffffff; /* 图标颜色 */
    font-size: 12px; /* 图标大小 */
    line-height: 18px; /* 圆形内居中对齐 */
    text-align: center;
    border-radius: 50%; /* 圆形样式 */
  }


  /* 自定义标签容器 */
.label-container {
position: relative;
display: inline-block;
padding-left: 18px; /* 为图标留出空间 */
}

/* 提示图标定位 */
.label-tooltip {
position: absolute;
left: 0;
top: 0;
}

/* 保持与其他表单项对齐 */
.custom-label-form-item :deep(.ant-form-item-label) {
// padding-bottom: 4px;
line-height: 2.5;
}

/* 图标样式调整 */
.label-tooltip .anticon {
font-size: 14px;
color: #1890ff;
cursor: help;

}

/* 输入框组宽度适配 */
.custom-label-form-item :deep(.ant-input-group) {
width: 100%;

}
</style>
