<template>
    <div class="image-uploader">
      <div class="upload-area" @click="triggerFileInput" :class="{ 'has-image': previewImage }">
        <input
          type="file"
          ref="fileInput"
          @change="handleFileChange"
          accept="image/jpeg, image/png"
          style="display: none"
        />
        <template v-if="!previewImage">
          <div class="upload-icon">
            <svg viewBox="0 0 24 24" width="48" height="48">
              <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" fill="currentColor" />
            </svg>
          </div>
          <div class="upload-text">点击上传图片</div>
          <div class="upload-hint">只能上传jpg/png文件，且不超过500kb</div>
        </template>
        <template v-else>
          <img :src="previewImage" alt="预览图片" class="preview-image" />
          <button class="remove-btn" @click.stop="removeImage">×</button>
        </template>
      </div>
  
      <div v-if="errorMessage" class="error-message">{{ errorMessage }}</div>
    </div>
  </template>
  
  <script setup>
  import { ref } from 'vue';
  
  const fileInput = ref(null);
  const previewImage = ref('');
  const isPublished = ref(false);
  const errorMessage = ref('');
  
  const triggerFileInput = () => {
    fileInput.value.click();
  };
  
  const emit = defineEmits(['update:modelValue', 'update:publishStatus']);

    const props = defineProps({
    modelValue: {
        type: File,
        default: null
    },
    publishStatus: {
        type: Boolean,
        default: false
    }
    });


  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (!file) return;
  
    // 检查文件类型
    const validTypes = ['image/jpeg', 'image/png'];
    if (!validTypes.includes(file.type)) {
      errorMessage.value = '只能上传jpg/png格式的图片';
      return;
    }
  
    // 检查文件大小 (500KB)
    if (file.size > 500 * 1024) {
      errorMessage.value = '图片大小不能超过500KB';
      return;
    }
  
    errorMessage.value = '';
    
    // 预览图片
    const reader = new FileReader();
    reader.onload = (e) => {
      previewImage.value = e.target.result;
    };
    reader.readAsDataURL(file);
  };
  
  const removeImage = () => {
    previewImage.value = '';
    fileInput.value.value = '';
  };
  </script>
  
  <style scoped>
  .image-uploader {
    max-width: 400px;
    margin: 0 auto;
    font-family: Arial, sans-serif;
  }
  
  .upload-area {
    border: 2px dashed #ccc;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  
  .upload-area:hover {
    border-color: #409eff;
  }
  
  .upload-icon {
    color: #409eff;
    margin-bottom: 10px;
  }
  
  .upload-text {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
  }
  
  .upload-hint {
    font-size: 12px;
    color: #999;
  }
  
  .preview-image {
    max-width: 100%;
    max-height: 300px;
    border-radius: 4px;
  }
  
  .has-image {
    padding: 10px;
  }
  
  .remove-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 24px;
    height: 24px;
    background: #f56c6c;
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 16px;
    line-height: 24px;
    cursor: pointer;
  }
  
  .remove-btn:hover {
    background: #f78989;
  }
  
  .publish-status {
    margin-top: 20px;
    display: flex;
    align-items: center;
  }
  
  .publish-status label {
    margin-right: 10px;
    font-weight: bold;
  }
  
  .status-options {
    display: flex;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;
  }
  
  .status-options span {
    padding: 8px 15px;
    cursor: pointer;
    transition: all 0.3s;
  }
  
  .status-options span:first-child {
    border-right: 1px solid #dcdfe6;
  }
  
  .status-options span.active {
    background-color: #409eff;
    color: white;
  }
  
  .error-message {
    margin-top: 10px;
    color: #f56c6c;
    font-size: 12px;
  }
  </style>