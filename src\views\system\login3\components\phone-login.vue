<template>
    <a-form ref="formRef" class="login-form" :model="loginForm" :rules="rules">
        <a-form-item name="phone">
            <a-input v-model:value.trim="loginForm.phone" placeholder="请输入手机号" />
        </a-form-item>
        <a-form-item name="code">
            <a-input v-model:value="loginForm.code" placeholder="请输入手机验证码" style="position: relative;" />
            <a-button @click="sendSmsCode" class="code-btn" type="link" :disabled="phoneCodeButtonDisabled"
                style="position: absolute; right: 0;">
                {{ phoneCodeTips }}
            </a-button>
        </a-form-item>
        <a-form-item>
            <div class="btn" @click="onLogin">登录</div>
        </a-form-item>
    </a-form>
</template>

<script setup>
import { message } from 'ant-design-vue';
import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { LOGIN_DEVICE_ENUM } from '/@/constants/system/login-device-const';
import { useUserStore } from '/@/store/modules/system/user';
import { smartSentry } from '/@/lib/smart-sentry';
import { localSave } from '/@/utils/local-util.js';
import LocalStorageKeyConst from '/@/constants/local-storage-key-const.js';
import { loginPhoneApi } from '/@/api/system/login-phone-api';
import { buildRoutes } from "/@/router/index.js";

const loginForm = reactive({
    phone: '',
    code: '',
    loginDevice: LOGIN_DEVICE_ENUM.PC.value,
});

const rules = {
    code: [{ required: true, message: '手机验证码不能为空' }],
    phone: [{ required: true, message: '手机号不能为空' }],
};

const router = useRouter();
const formRef = ref();
let phoneCodeTips = ref('获取验证码');
let phoneCodeButtonDisabled = ref(false);
let countDownTimer = null;

async function onLogin() {
    formRef.value.validate().then(async () => {
        try {
            SmartLoading.show();
            const res = await loginPhoneApi.loginByPhone(loginForm);

            localSave(LocalStorageKeyConst.USER_TOKEN, res.data.token ? res.data.token : '');
            message.success('登录成功');
            useUserStore().setUserLoginInfo(res.data);
            //构建路由
            buildRoutes();
            router.push('/home');
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            SmartLoading.hide();
        }
    });
}

function runCountDown() {
    let countDown = 60;
    phoneCodeTips.value = `${countDown}秒后重新获取`;
    countDownTimer = setInterval(() => {
        if (countDown > 0) {
            countDown--;
            phoneCodeTips.value = `${countDown}秒后重新获取`;
        } else {
            clearInterval(countDownTimer);
            phoneCodeButtonDisabled.value = false;
            phoneCodeTips.value = '获取验证码';
        }
    }, 1000);
}

async function sendSmsCode() {
    try {
        if (!loginForm.phone) {
            message.error('请输入手机号');
            return;
        }
        //正则验证手机号
        if (/^(13[0-9]|14[01456879]|15[0-3,5-9]|16[2567]|17[0-8]|18[0-9]|19[0-3,5-9])d{8}$/
            .test(loginForm.phone)) {
            message.error('请输入正确的手机号');
            return;
        }
        const res = await loginPhoneApi.sendSmsCodeByPhone(loginForm.phone);
        runCountDown();
        phoneCodeButtonDisabled.value = true;
        message.success('验证码发送成功! 请查看手机验证码~');
    } catch (e) {
        smartSentry.captureError(e);
    }
}
</script>

<style lang="less" scoped>
.login-form {

    .ant-input,
    .ant-input-affix-wrapper {
        height: 44px;
        border: 1px solid #ededed;
        border-radius: 4px;
    }

    .code-btn {
        height: 44px;
        padding: 4px 5px;
        width: 108px;
    }

    .btn {
        width: 350px;
        height: 50px;
        background: rgb(255, 98, 0);
        border-radius: 4px;
        font-size: 16px;
        font-weight: 700;
        text-align: center;
        color: #ffffff;
        line-height: 50px;
        cursor: pointer;
    }
}
</style>
