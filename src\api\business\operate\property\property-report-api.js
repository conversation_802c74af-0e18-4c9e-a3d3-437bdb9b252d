/**
 * 设备资产报告表，用于统计各区域的设备、产品和灯杆数量等信息 api 封装
 *
 * @Author:    yourName
 * @Date:      2025-05-05 19:40:12
 * @Copyright  中山睿数信息技术有限公司 2025
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const propertyReportApi = {

  /**
   * 资产报告基础信息查询 
   */
  propertyReportBaseInfo: (param) => {
    return postRequest('/propertyReport/property/report', param);
  },
  /**
   * 资产报告设备列表查询 
   */
  reportRefQueryPage: (param) => {
    return postRequest('/reportRef/queryPage', param);
  },
  /**
   * 设备位置列表查询
   */
  queryLocation: () => {
    return getRequest('/reportRef/query/location');
  },

  /**
   * 设备类型查询
   */
  queryDeviceType: (deviceNum) => {
    return getRequest(`/reportRef/device/type?deviceNum=${deviceNum}`);
  },

};
