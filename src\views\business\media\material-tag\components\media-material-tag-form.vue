<!--
  * 素材标签表
  *
  * @Author:    谢志豪
  * @Date:      2025-04-05 15:10:13
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
  <a-modal :title="form.tagId ? '编辑' : '添加'" :width="600" :open="visibleFlag" @cancel="onClose" :maskClosable="false" :destroyOnClose="true">
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }">
      <a-form-item label="素材标签名称" name="tagName">
        <a-input style="width: 100%" v-model:value="form.tagName" placeholder="素材标签名称" />
      </a-form-item>
      <a-form-item label="创建人" name="createUserId">
        <a-input style="width: 100%" v-model:value="form.createUserId" placeholder="创建人" />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { reactive, ref, nextTick } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { mediaMaterialTagApi } from '/@/api/business/media/material-tag/media-material-tag-api';
  import { smartSentry } from '/@/lib/smart-sentry';

  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show(rowData) {
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
    }
    // 使用字典时把下面这注释修改成自己的字典字段 有多个字典字段就复制多份同理修改 不然打开表单时不显示字典初始值
    // if (form.status && form.status.length > 0) {
    //   form.status = form.status.map((e) => e.valueCode);
    // }
    visibleFlag.value = true;
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
    tagName: undefined, //素材标签名称
    createUserId: undefined, //创建人
  };

  let form = reactive({ ...formDefault });

  const rules = {
    tagName: [{ required: true, message: '素材标签名称 必填' }],
    createUserId: [{ required: true, message: '创建人 必填' }],
  };

  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      if (form.tagId) {
        await mediaMaterialTagApi.update(form);
      } else {
        await mediaMaterialTagApi.add(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  defineExpose({
    show,
  });
</script>
