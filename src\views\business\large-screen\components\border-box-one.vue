<template>
  <div ref="borderBoxContainer" class="border-box-container">
    <div class="center-container">
      <decoration1 class="center-left-decoration" />
      <Decoration7 class="center-decoration" :color="['#18abd9', '#1ba6d0']">
        <text style="margin:1vw;">建设概况</text>
      </Decoration7>
      <decoration1 class="center-right-decoration"  />
    </div>
    <div class="border-box-content">
      <BorderBox10 :color="['#3896b3', '#329bbb']">
        <div class="content">
          <div class="grid-container">
            <div class="grid-item">
              <div class="item-inner">
                <img class="item-icon" src="/@/assets/images/large-screen/icon3.png"/>
                <div class="item-data">
                  <div class="item-title">工单数</div>
                  <div class="item-value" >{{ data.workOrderNum}}</div>
                  <div class="item-unit">个</div>
                </div>
              </div>
            </div>
            <div class="grid-item">
              <div class="item-inner">
                <img class="item-icon" src="/@/assets/images/large-screen/icon2.png"/>
                <div class="item-data">
                  <div class="item-title">边缘节点</div>
                  <div class="item-value">{{ data.edgeNodeNum}}</div>
                  <div class="item-unit">个</div>
                </div>
              </div>
            </div>
            <div class="grid-item">
              <div class="item-inner">
                <img class="item-icon" src="/@/assets/images/large-screen/icon1.png"/>
                <div class="item-data">
                  <div class="item-title">管理面积</div>
                  <div class="item-value">{{ totalAreaValue}}</div>
                  <div class="item-unit">km²</div>
                </div>
              </div>
            </div>
            <div class="grid-item">
              <div class="item-inner">
                <img class="item-icon" src="/@/assets/images/large-screen/icon5.png"/>
                <div class="item-data">
                  <div class="item-title">设备数</div>
                  <div class="item-value">{{ data.deviceNum}}</div>
                  <div class="item-unit">个</div>
                </div>
              </div>
            </div>
            <div class="grid-item">
              <div class="item-inner">
                <img class="item-icon" src="/@/assets/images/large-screen/icon6.png"/>
                <div class="item-data">
                  <div class="item-title">路灯数</div>
                  <div class="item-value">{{ data.lightNum }}</div>
                  <div class="item-unit">个</div>
                </div>
              </div>
            </div>
            <div class="grid-item">
              <div class="item-inner">
                <img class="item-icon" src="/@/assets/images/large-screen/icon4.png"/>
                <div class="item-data">
                  <div class="item-title">区域数</div>
                  <div class="item-value">{{ totalAreaCount }}</div>
                  <div class="item-unit">个</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </BorderBox10>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { BorderBox10, Decoration7, Decoration1 } from '@kjgl77/datav-vue3';

const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
});

// -------------------------------处理数据显示
const totalAreaValue = computed(() => {
  if (props.data.totalArea) {
    const value = Object.values(props.data.totalArea)[0];
    // 将平方米转换为平方千米
    return value ? (Number(value) / 1000000).toFixed(2) : '0.00';
  }
  return '0.00';
});

const totalAreaCount = computed(() => {
  if (props.data.totalArea) {
    return Object.keys(props.data.totalArea)[0];
  }
  return 0;
});

// -----------------------------------------边框
const borderBoxContainer = ref(null);

onMounted(() => {
  if (!borderBoxContainer.value) {
    console.error('BorderBox container ref not exist');
    return;
  }
  console.log('BorderBox container mounted:', borderBoxContainer.value);
});
</script>

<style scoped>
.border-box-container {
  position: absolute;
  top: 7vh;
  left: 1vw;
  width: 25vw;
  height: 26vh;
  z-index: 1000;
}

.center-container {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 3vh;
}

.center-right-decoration {
  width: 8vw;
  height: 2vh;
}

.center-left-decoration {
  transform: rotate(-180deg);
  width: 8vw;
  height: 2vh;
}

.center-decoration {
  position: absolute;
  color: white;
  font-size: 1.1vw;
  height: 3vh;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.border-box-content {
  position: absolute;
  top: 4.5vh;
  left: 0;
  width: 100%;
  height: calc(100% - 4.5vh);
}

.content {
  width: 100%;
  height: 100%;
  color: white;
  padding: 10px;
  box-sizing: border-box;
}

.grid-container {
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-rows: repeat(2, 1fr);
  grid-template-columns: repeat(3, 1fr);
  padding: 1px;
  box-sizing: border-box;
}

.grid-item {
  /*background: rgba(0, 21, 44, 0.8);*/
  /*border: 1px solid rgba(1, 122, 255, 0.3);*/
  border-radius: 5px;
  /*padding: 1px;*/
  display: flex;
  align-items: center;
  justify-content: center;
}

.item-inner {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.item-icon {
  font-size: 1.5vw;
  width:  3.5vw;
  height:  3.5vw;
  color: #017aff;
}

.item-data {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.item-title {
  font-size: 0.8vw;
  color: rgba(255, 255, 255, 0.7);
}

.item-value {
  font-size: 1.2vw;
  font-weight: bold;
  color: #01fcfc;
}

.item-unit {
  font-size: 0.8vw;
  color: rgba(255, 255, 255, 0.7);
}
</style>