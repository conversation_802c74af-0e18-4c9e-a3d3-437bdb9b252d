<!--
  * 设备表
  *
  * @Author:    李帅兵
  * @Date:      2025-03-21 22:21:07
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
  <a-modal :title="form.id ? '编辑' : '添加'" :width="700" :open="visibleFlag" @cancel="onClose" :maskClosable="false"
    :destroyOnClose="true">
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }">
      <a-form-item class="custom-label-form-item smart-query-form-item" name="deviceName">
        <template #label>
          <div class="label-container">
            <span>设备名称</span>
            <a-tooltip title="英文字母，并可包含数字、下划线（_）、中划线（-）、点号（.）、半角冒号（:）以及特殊字符@，整体长度需控制在4至32个字符之间" placement="topLeft"
              class="label-tooltip">
              <question-circle-outlined />
            </a-tooltip>
          </div>
        </template>
        <a-input-group compact>
          <a-input placeholder="请输入设备名称" style="width: calc(100% - 100px);" v-model:value="form.deviceName"
            :disabled="!!form.id" />
          <a-button type="default" style="width: 100px;" @click="generateDeviceName" :loading="generatingDeviceName"
            :disabled="!!form.id">
            自动生成
          </a-button>
        </a-input-group>
      </a-form-item>

      <a-form-item class="custom-label-form-item smart-query-form-item" name="deviceNoteName">
        <template #label>
          <div class="label-container">
            <span>备注名称</span>
            <a-tooltip title="中文或英文字母，并可包含数字和下划线（_），长度限制为 4 ~ 64 个字符" placement="topLeft" class="label-tooltip">
              <question-circle-outlined />
            </a-tooltip>
          </div>
        </template>
        <a-input-group compact>
          <a-input placeholder="设备备注名称" style="width: calc(100% - 100px);" v-model:value="form.deviceNoteName" />
          <a-button type="default" style="width: 100px;" @click="generateDeviceNoteName"
            :loading="generatingDeviceNoteName">
            自动生成
          </a-button>
        </a-input-group>
      </a-form-item>

      <a-form-item class="custom-label-form-item smart-query-form-item" name="deviceDesc">
        <template #label>
          <div class="label-container">
            <span>唯一编号</span>
            <a-tooltip title="设备唯一信息编号，比如mac地址，sn码等，标识设备唯一性" placement="topLeft" class="label-tooltip">
              <question-circle-outlined />
            </a-tooltip>
          </div>
        </template>
        <a-input-group compact>
          <a-input placeholder="设备备注名称" style="width: calc(100% - 100px);" v-model:value="form.uniqueNo" />
          <a-button type="default" style="width: 100px;" @click="generateUniqueNo" :loading="generatingUniqueNo">
            自动生成
          </a-button>
        </a-input-group>
      </a-form-item>


      <a-form-item class="custom-label-form-item smart-query-form-item" name="productKey">
        <template #label>
          <div class="label-container">
            <span>所属产品</span>
            <a-tooltip title="可选择已发布的产品数据，将设备绑定后继承其物模型" placement="topLeft" class="label-tooltip">
              <question-circle-outlined />
            </a-tooltip>
          </div>
        </template>
        <a-input style="width: 100%;" placeholder="请选择所属产品" @click="showProductForm"
          v-if="form.productKey === undefined" :disabled="!!form.id" />
        <a-input style="width: 100%;" placeholder="请选择所属产品" @click="showProductForm"
          :value="`${form.productName} : ${form.productKey}`" v-else :disabled="!!form.id || !!form.productName" />
        <!-- <a-input style="width: 100%;"  v-model:value="form.productKey" placeholder="产品id" :disabled="!!form.id" /> -->
      </a-form-item>

      <a-form-item label="设备类型" name="deviceType">
        <a-radio-group v-model:value="form.deviceType">
          <a-radio :value="'device'">设备</a-radio>
          <a-radio :value="'gateway'">网关设备</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="设备图标" name="logo">
        <!-- <upload /> -->
        <FileUpload />
        <!-- <a-input style="width: 100%" v-model:value="form.logo" placeholder="设备图标" /> -->
      </a-form-item>
      <a-form-item label="设备描述" name="categoryGuide">
        <!-- <a-textarea style="width: 100%" v-model:value="form.categoryGuide" placeholder="请输入引导说明" /> -->
        <SmartWangeditor ref="contentRef" :modelValue="form.contentHtml" :height="300" />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
  <chooseProductForm ref="chooseProductFormRef" @emitSelectedProduct="handleProductSelected" />
</template>
<script setup>
  import { reactive, ref, nextTick } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/src/components/framework/smart-loading';
  import { iotDeviceApi } from '/@/api/business/iot/device/iot-device-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  import SmartWangeditor from '/@/components/framework/wangeditor/index.vue';
  import FileUpload from '/@/components/support/file-upload/index.vue';
  import chooseProductForm from '/@/views/business/iot/device/components/choose-product-form.vue';
import { useRoute } from 'vue-router';

  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);
  const chooseProductFormRef = ref();
  const route = useRoute();
  function show(rowData) {
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
    }
    if (route.query && _.isEmpty(rowData)){
      Object.assign(form, route.query);
}
    // 使用字典时把下面这注释修改成自己的字典字段 有多个字典字段就复制多份同理修改 不然打开表单时不显示字典初始值
    // if (form.status && form.status.length > 0) {
    //   form.status = form.status.map((e) => e.valueCode);
    // }
    visibleFlag.value = true;
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  function showProductForm() {
    console.log("打开from表单")
    chooseProductFormRef.value.show();
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

const formDefault = {
  id: undefined,              // 主键编号
  deviceStatus: 0,            // 0 未激活 1 在线 2 离线
  device: "'device'",
  isTrusted: undefined,
  _vts: undefined,
  deviceName: undefined,
  deviceNoteName: undefined,
  uniqueNo: undefined,
  productKey: undefined,
  productName: undefined,
  deviceType: undefined,
  activeTime: undefined,
  createTime: undefined,
  createUserId: undefined,
  deviceDesc: undefined,
  deviceSecret: undefined,
  imei: undefined,
  lastOnlineTime: undefined,
  latitude: undefined,
  logo: undefined,
  longitude: undefined,
  parentId: undefined,
  recordStatus: undefined,
  regionId: undefined,
  regionName: undefined,
  sn: undefined,
  status: undefined,
  tenantId: undefined,
  updateTime: undefined,
  updateUserId: undefined
};

  const form = reactive({ ...formDefault });

  const rules = {
      id: [{ required: true, message: '主键编号 必填' }],
  };

  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      if (form.id) {
        await iotDeviceApi.update(form);

      } else {
        await iotDeviceApi.submit(form);
       
      }

      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  // // 自动生成
  // const generatingDeviceName = ref(false);
  // const generatingDeviceNoteName = ref(false);
  // const generatingUniqueNo = ref(false);
  //  // 生成设备名称
  //  const generateDeviceName = async () => {
  //     try {
  //       generatingDeviceName.value = true;
  //       const res = await iotDeviceApi.getRandomName();
  //       console.log("res",res)
  //       if (res) {
  //         form.deviceName = res.data;
  //         message.success('设备名称生成成功');
  //       } else {
  //         message.error(res.message || '生成设备名称失败');
  //       }
  //     } catch (error) {
  //       message.error('生成设备名称失败');
  //     } finally {
  //       generatingDeviceName.value = false;
  //     }
  //   };

  //   // 生成设备名称备注
  //  const generateDeviceNoteName = async () => {
  //     try {
  //       generatingDeviceNoteName.value = true;
  //       const res = await iotDeviceApi.getRandomRemark();
  //       console.log("res",res)
  //       if (res) {
  //         form.deviceNoteName = res.data;
  //         message.success('设备名称生成成功');
  //       } else {
  //         message.error(res.message || '生成设备名称失败');
  //       }
  //     } catch (error) {
  //       message.error('生成设备名称失败');
  //     } finally {
  //       generatingDeviceNoteName.value = false;
  //     }
  //   };

  //   // 生成唯一编号
  //  const generateUniqueNo = async () => {
  //     try {
  //       generatingUniqueNo.value = true;
  //       const res = await iotDeviceApi.getRandomCode();
  //       console.log("res",res)
  //       if (res) {
  //         form.uniqueNo = res.data;
  //         message.success('设备名称生成成功');
  //       } else {
  //         message.error(res.message || '生成设备名称失败');
  //       }
  //     } catch (error) {
  //       message.error('生成设备名称失败');
  //     } finally {
  //       generatingUniqueNo.value = false;
  //     }
  //   };
  


  // 状态管理
const generatingDeviceName = ref(false);
const generatingDeviceNoteName = ref(false);
const generatingUniqueNo = ref(false);

// 随机字符串生成器（辅助函数）
const generateRandomString = (length = 4, options = {}) => {
  const { 
    numericOnly = false, 
    uppercaseOnly = false, 
    lowercaseOnly = false 
  } = options;
  
  let chars = '0123456789';
  if (!numericOnly) {
    chars += 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    if (uppercaseOnly) chars = chars.slice(10, 36); // A-Z only
    if (lowercaseOnly) chars = chars.slice(36);     // a-z only
  }
  
  return Array.from({ length }, () => 
    chars[Math.floor(Math.random() * chars.length)]
  ).join('');
};

// 设备名称生成（格式：Device-XXXX）
const generateDeviceName = () => {
  generatingDeviceName.value = true;
  
  try {
    const randomPart = generateRandomString(7, { uppercaseOnly: true });
    form.deviceName = `Device-${randomPart}`;
    message.success('设备名称生成成功');
  } catch {
    message.error('生成设备名称失败');
  } finally {
    generatingDeviceName.value = false;
  }
};

// 生成设备备注（纯乱码）
const generateDeviceNoteName = () => {
  generatingDeviceNoteName.value = true;
  try {
    form.deviceNoteName = generateRandomString(12); // 12位乱码
    message.success('设备备注生成成功');
  } catch {
    message.error('生成设备备注失败');
  } finally {
    generatingDeviceNoteName.value = false;
  }
};

// 唯一编号生成（格式：UNIQUE-XXXX-XXXX）
const generateUniqueNo = () => {
  generatingUniqueNo.value = true;
  
  try {
    const part1 = generateRandomString(4, { uppercaseOnly: true });
    const part2 = generateRandomString(4, { uppercaseOnly: true });
    form.uniqueNo = `UNIQUE-${part1}-${part2}`;
    message.success('唯一编号生成成功');
  } catch {
    message.error('生成唯一编号失败');
  } finally {
    generatingUniqueNo.value = false;
  }
};

    // 接受子组件数据
function handleProductSelected(selectedProduct) {
  console.log('父组件接收到的产品信息:', selectedProduct);
  // 在这里可以处理 selectedProduct，例如保存到状态中
  form.productKey = selectedProduct.productKey;
  form.productName = selectedProduct.productName;
}

  defineExpose({
    show,
  });
</script>
<style scoped>
   .label-container {
  position: relative;
  display: inline-block;
  padding-left: 18px; /* 为图标留出空间 */
}

/* 提示图标定位 */
.label-tooltip {
  position: absolute;
  left: 0;
  top: 0;
}

/* 保持与其他表单项对齐 */
.custom-label-form-item :deep(.ant-form-item-label) {
  /* padding-bottom: 4px; */
  line-height: 2.5;
}

/* 图标样式调整 */
.label-tooltip .anticon {
  font-size: 14px;
  color: #1890ff;
  cursor: help;
  
}

/* 输入框组宽度适配 */
.custom-label-form-item :deep(.ant-input-group) {
  width: 100%;

}
</style>