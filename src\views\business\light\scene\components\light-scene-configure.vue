<!--
  * 照明场景详情-照明配置
  *
  * @Author:    骆伟林
  * @Date:      2025-04-07 21:36:15
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
  <a-row>
    <a-col :span="18">
      <a-card>
        <a-list :data-source="dataSource" :loading="tableLoading">
        <template #header>
          <a-button type="primary" @click="addConfigure" style="margin-bottom: 10px">添加策略</a-button>
        </template>
        <a-list-item v-for="(item, index) in dataSource" :key="index">
          <a-card :bordered="false" hoverable style="width: 100%">
            <div class="card-content">
              <div class="card-header">
                <span style="font-size: 14px; color: #999">
                  时间段：
                  <a-time-range-picker v-model:value="item.timeRange" @change="getTimeRange(index, $event)" style="width: 300px" />
                </span>
                <span style="font-size: 14px; color: #999">配置策略：</span>
                <div class="drop-zone">
                  <VueDraggable
                    v-model="item.strategies"
                    :animation="150"
                    group="people"
                    item-key="id"
                    class="strategy-container"
                    @add="onStrategyAdded($event, index)"
                  >
                    <template v-if="item.strategies.length === 0">
                      <div class="empty-placeholder">右边列表拖拉选择</div>
                    </template>
                    <div v-for="option in item.strategies" :key="option.id" class="cursor-move h-50px bg-gray-500/5 rounded p-3 strategy-item">
                      {{ option.label }}
                      <CloseOutlined class="cursor-pointer" @click="remove(index, option.id)" />
                    </div>
                  </VueDraggable>
                </div>
                <span style="float: right"> <CloseOutlined class="cursor-pointer" @click="removeCard(index)" /></span>
              </div>
            </div>
          </a-card>
        </a-list-item>
      </a-list>
      </a-card>

    </a-col>

    <a-col :span="6">
      <div class="strategy-palette">
        <h3 class="palette-title">策略列表</h3>
        <div ref="listContainer" class="scroll-container">
          <VueDraggable
            v-model="cardData"
            :animation="150"
            :group="{ name: 'people', pull: 'clone', put: false }"
            :sort="false"
            item-key="id"
            class="palette-container"
          >
            <li
              v-for="(item, index) in cardData"
              :key="item.id"
              class="strategy-item h-12 bg-gray-100 rounded-lg flex items-center justify-between px-4 mb-2"
            >
              <!-- <span class="text-gray-700 font-medium" style="font-size: 18px;">{{ item.label }}</span> -->
              <a-card>
                <div>
               <span style="margin-right: 5px">{{ item.label }}</span>
                <span style="font-size: 14px; color: #999; float: right">{{ item.type }}</span>
                </div>
               <div>
                <span style="font-size: 13px; color: #999; width: 50%">{{item.createTime }}</span>
               </div>
              </a-card>
            </li>
          </VueDraggable>
          <div v-if="tableLoading" class="loading-more">加载中...</div>
          <div v-else-if="hasMoreData" class="load-more" @click="loadMoreData">点击加载更多</div>
          <div v-else class="no-more">没有更多数据了</div>
        </div>
      </div>
    </a-col>
  </a-row>
</template>
<script setup>
import { ref, reactive, onMounted, defineEmits, watch, onUnmounted, nextTick } from 'vue';
import { VueDraggable } from 'vue-draggable-plus';
import { lightSceneApi } from '/@/api/business/light/scene/light-scene-api.js';
import dayjs from 'dayjs';
import PerfectScrollbar from 'perfect-scrollbar';

const props = defineProps({
  strategyList: {
    type: Array,
    default: () => [],
  },
});
const emit = defineEmits(['dataSource']);

function getTimeRange(index, timeRange) {
  console.log('时间范围变化:', index, timeRange);
  const item = dataSource.value[index];
  if (timeRange && timeRange.length === 2) {
    const startTime = dayjs.isDayjs(timeRange[0]) ? timeRange[0] : dayjs(timeRange[0]);
    const endTime = dayjs.isDayjs(timeRange[1]) ? timeRange[1] : dayjs(timeRange[1]);

    item.createTime = startTime.format('HH:mm:ss');
    item.endTime = endTime.format('HH:mm:ss');
    console.log(item);
    console.log(dataSource.value);
    emit('dataSource', dataSource.value);
  } else {
    item.createTime = '';
    item.endTime = '';
  }
}

function remove(configIndex, strategyId) {
  const config = dataSource.value[configIndex];
  if (config.strategies) {
    config.strategies = config.strategies.filter((s) => s.id !== strategyId);
    emit('dataSource', dataSource.value);
  }
}

const dataSource = ref([]);

function removeCard(index) {
  dataSource.value.splice(index, 1);
  emit('dataSource', dataSource.value);
}

function addConfigure() {
  dataSource.value.push({
    timeRange: null,
    strategies: [],
  });
}

const onStrategyAdded = (event, configIndex) => {
  const { newIndex } = event;
  const strategy = dataSource.value[configIndex].strategies?.[newIndex];
  if (dataSource.value[configIndex].strategies.length >= 1) {
    dataSource.value[configIndex].strategies = [];
  }
  if (strategy) {
    dataSource.value[configIndex].strategies.push(strategy);
  }
  emit('dataSource', dataSource.value);
};

const currentPgNum = ref(1);
const hasMoreData = ref(true);
const cardData = ref([]);
const tableLoading = ref(false);
const listContainer = ref(null);

const queryForm = reactive({
  pageNum: 1,
  pageSize: 6,
});

async function queryData() {
  tableLoading.value = true;
  try {
    const result = await lightSceneApi.strategyQueryPage({
      pageNum: currentPgNum.value,
      pageSize: queryForm.pageSize,
    });
    cardData.value = [...cardData.value, ...result.data.list];
    if (result.data.list.length < queryForm.pageSize) {
      hasMoreData.value = false;
    }
  } finally {
    tableLoading.value = false;
  }
}

watch(
  () => props.strategyList,
  (newList) => {
    if (Array.isArray(newList) && newList.length > 0) {
      dataSource.value = newList.map((strategy) => ({
        timeRange: [dayjs(strategy.startTime, 'HH:mm:ss'), dayjs(strategy.endTime, 'HH:mm:ss')],
        strategies: [
          {
            id: strategy.strategyId,
            createUserId: strategy.createUserId,
            createTime: strategy.createTime,
            updateUserId: strategy.updateUserId,
            updateTime: strategy.updateTime,
            tenantId: strategy.tenantId,
            type: strategy.strategyType,
            label: strategy.strategyLabel,
          },
        ],
        createTime: strategy.createTime.match(/\d{2}:\d{2}:\d{2}/)?.[0],
        endTime: strategy.createTime.match(/\d{2}:\d{2}:\d{2}/)?.[0],
      }));
    } else {
      dataSource.value = [];
    }
  },
  { immediate: true }
);

async function loadMoreData() {
  if (!hasMoreData.value) return;

  currentPgNum.value += 1;
  await queryData();
}

function debounce(fn, delay) {
  let timer = null;
  return function(...args) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => fn.apply(this, args), delay);
  };
}

const handleScroll = debounce((event) => {
  const container = event.target;
  const { scrollTop, scrollHeight, clientHeight } = container;
  const threshold = 50;
  
  if (scrollTop + clientHeight >= scrollHeight - threshold) {
    loadMoreData();
  }
}, 200);

onMounted(async () => {
  await queryData();
  await nextTick();
  
  const container = listContainer.value;
  if (container) {
    container.addEventListener('scroll', handleScroll);
    new PerfectScrollbar(container, {
      suppressScrollX: true,
      wheelPropagation: true
    });
  }
});

onUnmounted(() => {
  const container = listContainer.value;
  if (container) {
    container.removeEventListener('scroll', handleScroll);
  }
});
</script>

<style scoped>
.strategy-item {
  transition: all 0.3s ease;
}

.strategy-item:hover {
  background-color: #f0f0f0;
}

.card-header {
  display: flex;
  justify-content: space-around;
}

.strategy-palette {
  background: #fafafa;
  padding: 16px;
  border-radius: 4px;
  height: 350px; /* 调整为一个合适的高度 */
  position: relative;
}

.scroll-container {
  max-height: 100%;
  overflow: auto;
}

.palette-title {
  margin-bottom: 12px;
  font-size: 18px;
  color: #333;
}

.palette-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.empty-placeholder {
  color: #888;
  text-align: center;
  font-size: 14px;
  width: 100%;
}

.strategy-container {
  height: 30px;
  width: 250px;
  border: 2px dashed #1890ff;
  border-radius: 4px;
  background-color: #f0f9ff;
  gap: 8px;
  transition: all 0.3s ease;
  text-align: center;
}
  
.strategy-container:hover {
  border-color: #096dd9;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
.loading-more, .load-more, .no-more {
  text-align: center;
  margin-top: 10px;
  color: #999;
  cursor: pointer;
}

.loading-more {
  cursor: default;
}
</style>