/**
 * 智慧运营 api 封装
 *
 * @Author:    文希希
 * @Date:      2025-03-21 22:21:07
 * @Copyright  中山睿数信息技术有限公司 2025
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const operateEnvApi = {
  //根据区域ID、产品key查询设备
  getDeviceByProductKey: (param) => {
    return postRequest('/iotDevice/getDeviceByProductKey', param);
  },

  //根据时间查询环境数据
  getEnvDataByTimeRange: (param) => {
    return postRequest('/env/queryEnvDataByTimeRange', param);
  },

  //输出每小时平均环境数据
  getEnvDataPerHour: (param) => {
    return postRequest('/env/envDataPerHour', param);
  },

  //输出温度和湿度Top榜单
  getEnvDataTop: (param) => {
    return postRequest('/env/envDataTop', param);
  },

  //输出每日平均环境数据
  getEnvDataPerDay: (param) => {
    return postRequest('/env/envDataPerDay', param);
  },
};
