<template>
  <a-row>
    <a-col span="4">
      <TreeTimeSelect @update:timeValue="handleTimeValue" />
    </a-col>
    <a-col span="20">
  <a-card size="small" :bordered="false" :hoverable="true">
    <a-calendar v-model:value="currentDate" @panelChange="fetchInspectionData">
      <template #dateCellRender="{ current }">
        <div v-for="device in getDevices(current)" :key="device?.id">
          <a-badge status="processing" :text="`巡检设备: ${device?.deviceName}`" />
        </div>
      </template>
    </a-calendar>
  </a-card>
  </a-col>
  </a-row>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import dayjs from 'dayjs';
import { inspectApi } from '/@/api/business/maintain/inspect/inspect-api.js';
import { smartSentry } from '/@/lib/smart-sentry';
import TreeTimeSelect from '/@/views/business/operate/components/treetime/index.vue';
//目前的时间
const currentDate = ref(dayjs());
const inspectionData = ref([]);

const formState = {
  beginDate: undefined,
  endDate: undefined
};


const form = reactive({...formState});

onMounted(() => {
  fetchInspectionData(currentDate.value);
});

const fetchInspectionData = async (date) => {
  form.beginDate = date.startOf('month').format('YYYY-MM-DD');
  form.endDate = date.endOf('month').format('YYYY-MM-DD');
  try {
    const res = await inspectApi.queryInspectCondition(form);
    inspectionData.value = res.data; 
  } catch (error) {
    smartSentry.captureError(error);
    console.error('巡检数据查询失败:', error);
  }
};
const getDevices = (date) => {
  const dateStr = date.format('YYYY-MM-DD');
  const item = inspectionData.value.find(d => d.date === dateStr);
  return item?.deviceList || [];
};

const handleTimeValue = (timeValue) => {
  if (timeValue !== dayjs(new Date()).format('YYYY-MM')) {
    currentDate.value = dayjs(timeValue);
  }else{
    currentDate.value = dayjs(new Date());
  }
  fetchInspectionData(currentDate.value);
};
</script>