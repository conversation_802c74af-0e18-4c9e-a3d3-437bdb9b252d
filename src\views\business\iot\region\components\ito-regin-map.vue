<!--
  * 设备区域-地图
  *
  * @Author:    骆伟林
  * @Date:      2025-03-22 17:51:27
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
  <a-row :gutter="8" style="margin-bottom: 10px;">
    <div style="display: flex; align-items: center;">
      地址：
      <table>
        <tr>
          <td>
            <a-input id="tipinput" v-model:value="address" placeholder="请输入地址" @change="select" />
          </td>
        </tr>
      </table>
    </div>
    <a-button type="primary" style="margin:0 10px;" @click="drawPolygon">开始编辑</a-button>
    <a-button type="primary" style="margin-right: 10px;" @click="clearDrawings">清空绘制图像</a-button>
    <a-button type="primary" @click="confirmRegion">确定区域</a-button>
  </a-row>
  <div>
    <div id="container" ref="mapContainer"></div>
  </div>
</template>
<script setup>
import { ref, onMounted, onUnmounted,computed } from 'vue';
import AMapLoader from '@amap/amap-jsapi-loader';
import { message } from 'ant-design-vue';

const emit = defineEmits(['getArea']);

// 定义 props
const props = defineProps({
  address: {
    type: String,
    default: '',
  },
  longitude: {
    type: Number,
    default: undefined,
  },
  latitude: {
    type: Number,
    default: undefined,
  },
  latAndLon: {
    type: Object,
    default: () => ({ longitude: undefined, latitude: undefined }),
  },
});

const address = ref(''); 
let map = null;
let mouseTool = null;
let polygon = null; // 用于存储绘制的多边形
let currentOverlay = null;
let AMapInstance = null;
let placeSearch = null; // 地点搜索实例
let autoComplete = null; // 自动完成实例
let longitude=null
let latitude=null

// 初始化地图
let initMap = () => {
  const container = document.getElementById('container');
  if (!container) {
    console.error('Map container div not exist');
    return;
  }
  window._AMapSecurityConfig = {
        securityJsCode: 'fd83369091f64a0f25572251e0c9eae5',
      };

  AMapLoader.load({
    key: '1778f4eaad1a5a43d9b04ef0c9690b3f', // 高德地图 Key
    version: '2.0', 
    plugins: [
      'AMap.MouseTool',
      'AMap.ToolBar',
      'AMap.Scale',
      'AMap.HawkEye',
      'AMap.ControlBar',
      'AMap.Geometry',
      'AMap.AutoComplete',
      'AMap.PlaceSearch',
    ], 
  })
    .then((AMap) => {
      AMapInstance = AMap;
      map = new AMap.Map('container', {
        center: [116.397428,  39.90923], // 地图中心点
        zoom: 14,
        viewMode: '2D',
        pitch: 30, 
      });
      // 初始化鼠标工具
      mouseTool = new AMap.MouseTool(map);
      // 监听绘制完成事件
      mouseTool.on('draw', (event) => {
        if (event.obj instanceof AMap.Polygon) {
          polygon = event.obj;
          currentOverlay = event.obj; 
        }
      });

      // 添加比例尺控件
      const scale = new AMap.Scale();
      map.addControl(scale);

      // 添加工具条控件
      const toolBar = new AMap.ToolBar({
        position: {
          top: '110px',
          right: '40px',
        },
      });
      map.addControl(toolBar);

      // 初始化自动完成服务
      autoComplete = new AMapInstance.AutoComplete({
        input: 'tipinput', 
      });
      // 监听选择事件
      autoComplete.on('select', select);
      // 初始化地点搜索
      placeSearch = new AMapInstance.PlaceSearch({
        map: map, 
      });
       // 设置地图中心点
       map.setCenter([props.longitude, props.latitude]);
      map.setZoom(15); 

      // 回显多边形
      if (parsedLatAndLon.value.length > 0) {
        drawExistingPolygon(parsedLatAndLon.value);
      }
    })
    .catch((e) => {
      console.error('地图加载失败:', e);
    });
};

// 处理选择
const select = (e) => {
  if (e.poi && e.poi.location) {
    const { lng, lat } = e.poi.location;
    const { adcode, name } = e.poi;
    console.log(name);
    address.value=name;
    // 跳转到选择的位置
    map.setCenter([lng, lat]);
    map.setZoom(15); 
    // 在地图上显示标记
    placeSearch.setCity(adcode);
    placeSearch.search(name);
    longitude=lng
    latitude=lat
  }
};

// 绘制多边形
function drawPolygon() {
  mouseTool.polygon({
    strokeColor: '#FF33FF',
    strokeOpacity: 1,
    strokeWeight: 6,
    strokeOpacity: 0.2,
    fillColor: '#1791fc',
    fillOpacity: 0.4,
    strokeStyle: 'solid',
  });
}
function drawExistingPolygon(path) {
  console.log(path);
  
  if (map && path.length > 0) {
    polygon = new AMapInstance.Polygon({
      path: path,
      strokeColor: '#FF33FF',
      strokeOpacity: 1,
      strokeWeight: 6,
      strokeOpacity: 0.2,
      fillColor: '#1791fc',
      fillOpacity: 0.4,
      strokeStyle: 'solid',
    });
    map.add(polygon);
    currentOverlay = polygon;
    map.setFitView([polygon]); // 调整视图以适应多边形
  }
}
// 清空绘制图像
function clearDrawings() {
  if (polygon) {
    map.remove(polygon);
    polygon = null;
    currentOverlay = null; 
  }
}

// 计算多边形面积
function calculatePolygonArea(path) {
  const area = Math.round(AMapInstance.GeometryUtil.ringArea(path));
  return area;
}

// 确定区域
function confirmRegion() {
  if (currentOverlay) {
    let area = 0;
    let coordinates = [];
    if (currentOverlay instanceof AMap.Polygon) {
      const path = currentOverlay.getPath();
      const formattedPath = path.map((point) => [point.getLat(), point.getLng()]);
      const areaPath = formattedPath.map(([lat, lng]) => [lng, lat]);
      area = calculatePolygonArea(areaPath);
      const latAndLon = `[${formattedPath.map(([lat, lng]) => `(${lat}, ${lng})`).join(', ')}]`;
      coordinates = latAndLon;
    } else {
      message.warning('当前覆盖物不支持面积计算');
      return;
    }
    message.success(`区域面积: ${area} 平方米`);
    mouseTool.close();
    emit('getArea', { area, coordinates, longitude, latitude,address });
  } else {
    message.warning('请先绘制覆盖物');
  }
}

const parsedLatAndLon = computed(() => {
  try {
    const parsed = JSON.parse(props.latAndLon.replace(/\(/g, '[').replace(/\)/g, ']'));
    return parsed.map(([lat, lng]) => [lng, lat]);
  } catch (e) {
    console.error('解析 latAndLon 失败:', e);
    return [];
  }
});


// 组件挂载时初始化地图
onMounted(() => {
  address.value=props.address;
  initMap();
  longitude=props.longitude
  latitude=props.latitude
  
});

// 组件卸载时销毁地图
onUnmounted(() => {
  if (map) {
    map.destroy();
    map = null;
  }
});
</script>

<style scoped>
#container {
  width: 100%;
  height: 70vh;
}
.input-card {
  margin-top: 10px; 
}
</style>