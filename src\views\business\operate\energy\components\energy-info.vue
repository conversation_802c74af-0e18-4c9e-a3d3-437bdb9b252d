<template>
  <div class="energy-dashboard">
    <div class="stats-cards">
      <a-row :gutter="16">
        <a-col :span="8">
          <a-card class="energy-card" :bordered="false">
            <div class="card-content">
              <span class="card-title">本月用电量(kWh)</span>
              <span class="card-value">{{energyInfo.thisMonthEnergy}}</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card class="energy-card" :bordered="false">
            <div class="card-content">
              <span class="card-title">上月用电量(kWh)</span>
              <span class="card-value">{{energyInfo.lastMonthEnergy}}</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card class="energy-card" :bordered="false">
            <div class="card-content">
              <span class="card-title">环比节能(kWh)</span>
              <span class="card-value">{{energyInfo.energyPercent}}</span>
              <div class="card-footer">
                <span class="card-label">节能比率</span>
                <span class="card-percent" 
                :class="{'up': Number(energyInfo.energyPercentRate) < 0, 'down': Number(energyInfo.energyPercentRate) > 0}">
                {{energyInfo.energyPercentRate}}%
                </span>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
      <a-row :gutter="16" style="margin-top: 16px">
        <a-col :span="8">
          <a-card class="energy-card" :bordered="false">
            <div class="card-content">
              <span class="card-title">本月平均功率(W)</span>
              <span class="card-value">{{energyInfo.thisMonthPowerAvg}}</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card class="energy-card" :bordered="false">
            <div class="card-content">
              <span class="card-title">上月平均功率(W)</span>
              <span class="card-value">{{energyInfo.lastMonthPowerAvg}}</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card class="energy-card" :bordered="false">
            <div class="card-content">
              <span class="card-title">平均功率节省(W)</span>
              <span class="card-value">{{energyInfo.powerPercent}}</span>
              <div class="card-footer">
                <span class="card-label">节能比率</span>
                <span class="card-percent" 
                :class="{'up': Number(energyInfo.powerPercentRate) < 0, 'down': Number(energyInfo.powerPercentRate) > 0}">
                {{energyInfo.powerPercentRate}}%
                </span>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
    <!-- 图表区域 -->
    <a-row :gutter="16" style="margin-top: 16px">
      <a-col :span="24">
        <a-card :bordered="false" class="chart-card">
          <template #title>
            <div class="chart-title">
              <span class="title-text">
                <a-badge color="#52c41a" />
                月能耗分析
              </span>
            </div>
          </template>
          <!-- 月能耗分析图 -->
          <div ref="energyDayRef" class="chart-container"></div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 实时监控图表 -->
    <a-row :gutter="16" style="margin-top: 16px">
      <a-col :span="16">
        <a-card :bordered="false" class="chart-card">
          <template #title>
            <div class="chart-title">
              <span class="title-text">
                <a-badge color="#faad14" />
                月电负荷功率实时曲线
              </span>
            </div>
          </template>
          <!-- 实时曲线图 -->
          <div ref="realTimePowerRef" class="chart-container"></div>
        </a-card>
      </a-col>
      <a-col :span="8">
        <a-card :bordered="false" class="chart-card">
          <template #title>
            <div class="chart-title">
              <span class="title-text">
                <a-badge color="#1890ff" />
                当前负载详情
              </span>
            </div>
          </template>
          <!-- 负载详情 -->
          <div class="load-details">
            <div class="load-item">
              <div class="load-label">最大值(kW)</div>
              <div class="load-value">{{energyInfo.maxPower || '暂无数据'}}</div>
            </div>
            <a-divider style="margin: 16px 0" />
            <div class="load-item">
              <div class="load-label">平均值(kW)</div>
              <div class="load-value">{{energyInfo.avgPower || '暂无数据'}}</div>
            </div>
            <a-divider style="margin: 16px 0" />
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted} from 'vue';
  import * as echarts from 'echarts';
  import dayjs from 'dayjs';
  import { energyApi } from '/@/api/business/operate/energy/energy-api';
  //--------------------------------父子通信----------------------------
  const props =defineProps({
    Id:{
      type:[String,Number],
      default:undefined,
    },
  })
  //-----------------------------请求数据--------------------------------
  const energyInfoDefault = {
    thisMonthEnergy: undefined,//本月用电量
    lastMonthEnergy: undefined,//上月用电量
    energyPercent: undefined,//环比节能
    energyPercentRate: undefined,//环比节能比率
    thisMonthPowerAvg: undefined,//本月平均功率
    lastMonthPowerAvg: undefined,//上月平均功率
    powerPercent: undefined,//功率节省
    powerPercentRate: undefined,//功率节省比率
    maxPower: undefined,//最大功率
    avgPower: undefined,//平均功率
    electricityData: [],//电流数据
    voltageData: [],//电压数据
    powerData: [],//功率数据
    EnergyDayData: [],//日能耗数据
  }
  const energyInfo = ref(energyInfoDefault)
  async function getEnergyInfo(){
    const res = await energyApi.energyReporyId(props.Id)
    const data = res.data?.reportData // 获取目标数据
    Object.assign(energyInfo.value,data)
    initCharts()
  }
  //----------------------------能耗数据-----------------------------------
  const energyDayRef = ref(null);
  let energyDayChart = null;
  function getEnergyDayData(){
    const data = energyInfo.value.EnergyDayData;
    const dates = data.map(item=>dayjs(item.x).format('MM-DD')).reverse()
    const values = data.map(item=>Number(item.y).toFixed(4)).reverse()
    return {dates,values}
  }
  function initEnergyDayChart () {
    if (!energyDayRef.value) return;
    if (energyDayChart) {
      energyDayChart.dispose();
    }
    energyDayChart = echarts.init(energyDayRef.value);
    const {dates,values} = getEnergyDayData()
    const option = {
      tooltip: {
        trigger: 'axis',
        formatter: function(params) {
          return `${params[0].name}<br/>${params[0].seriesName}: ${params[0].value} kWh`;
        }
      },
      legend: {
        data: ['日能耗'],
        top: 10
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: dates,
        name: '日',
        nameLocation: 'start',
        axisLabel: {
          showMaxLabel: true,
          interval: 3,
          rotate: 30
        },
        axisTick: {
          alignWithLabel: true
        }
      },
      yAxis: {
        type: 'value',
        name: '能耗(kWh)',
        axisLine: {
          show: true
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed'
          }
        }
      },
      series: [
        {
          name: '日能耗',
          type: 'line',
          step: 'middle',
          data: values,
          lineStyle: {
            width: 3,
            color: '#722ed1'
          },
          itemStyle: {
            color: '#722ed1',
            borderWidth: 2
          },
          symbol: 'circle',
          symbolSize: 8,
          markPoint: {
            symbol: 'pin',
            data: [
              { type: 'max', name: '最大值' },
              { type: 'min', name: '最小值' }
            ]
          },
          markLine: {
            data: [
              { type: 'average', name: '平均值' }
            ]
          }
        }
      ]
    };
    
    energyDayChart.setOption(option);
  };

  // -----------------------------------电流电压统计-----------------------------------
  const realTimePowerRef = ref(null);  
  let realTimePowerChart = null;

  function getPowerDayData(){
    const electricityData = energyInfo.value.electricityData//获取单日电能
    const voltageData = energyInfo.value.voltageData//获取单日电压
    const powerData = energyInfo.value.powerData//获取单日功率
    const electricityDates = electricityData.map(item=>dayjs(item.x).format('MM-DD')).reverse()
    const electricityValues = electricityData.map(item=>Number(item.y).toFixed(4)).reverse()
    const voltageValues = voltageData.map(item=>Number(item.y).toFixed(4)).reverse()
    const powerValues = powerData.map(item=>Number(item.y).toFixed(4)).reverse()
    // 计算功率最大值和平均值
    if (powerValues.length > 0) {
      const numPowerValues = powerValues.map(v => Number(v))
      energyInfo.value.maxPower = Math.max(...numPowerValues).toFixed(2)
      energyInfo.value.avgPower = (numPowerValues.reduce((sum, v) => sum + v, 0) / numPowerValues.length).toFixed(2)
    } else {
      energyInfo.value.maxPower = '暂无数据'
      energyInfo.value.avgPower = '暂无数据'
    }
    return {electricityDates,electricityValues,voltageValues,powerValues}
  }

  function initRealTimePowerChart () {
    if (!realTimePowerRef.value) return;  
    if (realTimePowerChart) {
      realTimePowerChart.dispose();
    }
    realTimePowerChart = echarts.init(realTimePowerRef.value);
    const {electricityDates,electricityValues,voltageValues,powerValues} =  getPowerDayData()    
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        }
      },
      legend: {
        data: ['电流', '电压', '功率'],
        top: 10
      },
      grid: {
        left: '3%',
        right: '8%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: electricityDates,
        name: '日',
        nameLocation: 'start',
        axisLabel: {
          showMaxLabel: true,
          interval: 2,
          rotate: 30
        }
      },
      yAxis: [{
        type: 'value',
        name: '电流/电压',
        min: 0,
        axisLine: {
          show: true
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: '#eee'
          }
        }
      },
      {
        type: 'value',
        name: '功率(W)',
        min: 0,
        position: 'right',
        axisLine: {
          show: true
        },
        splitLine: {
          show: false
        }
      }],
      series: [
        {
          name: '电流',
          type: 'line',
          data: electricityValues,
          smooth: true,
          symbol: 'emptyCircle',
          symbolSize: 6,
          itemStyle: {
            color: '#1890ff'
          },
          markPoint: {
            symbol: 'pin',
            symbolSize: 40,
            data: [
              { type: 'max', name: '最大值' },
              { type: 'min', name: '最小值' }
            ]
          }
        },
        {
          name: '电压',
          type: 'line',
          data: voltageValues,
          smooth: true,
          symbol: 'emptyCircle',
          symbolSize: 6,
          itemStyle: {
            color: '#722ed1'
          },
          markPoint: {
            symbol: 'pin',
            symbolSize: 40,
            data: [
              { type: 'max', name: '最大值' }
            ]
          }
        },
        {
          name: '功率',
          type: 'line',
          yAxisIndex: 1,
          data: powerValues,
          smooth: true,
          symbol: 'emptyCircle',
          symbolSize: 6,
          itemStyle: {
            color: '#52c41a'
          },
          markPoint: {
            symbol: 'pin',
            symbolSize: 40,
            data: [
              { type: 'max', name: '最大值' },
            ]
          }
        }
      ]
    };
    
    realTimePowerChart.setOption(option);
  };

  // 初始化所有图表
  const initCharts = () => {
    initEnergyDayChart();
    initRealTimePowerChart();
  };
  // 窗口大小调整处理
  const handleResize = () => {
    energyDayChart?.resize();
    realTimePowerChart?.resize();
  };

  // 组件挂载
  onMounted(() => {
    getEnergyInfo()
    window.addEventListener('resize', handleResize);
  });

  // 组件卸载
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
    energyDayChart?.dispose();
    realTimePowerChart?.dispose();
  });
</script>

<style scoped>
.energy-dashboard {
  padding: 16px;
  background-color: #f0f2f5;
}
.energy-card {
  height: 100%;
  border-radius: 4px;
}

.card-content {
  display: flex;
  flex-direction: column;
}

.card-title {
  font-size: 14px;
  color: #000000;
  margin-bottom: 8px;
}

.card-value {
  font-size: 24px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 16px;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-label {
  font-size: 12px;
  color: #000000;
}

.card-percent {
  font-size: 12px;
  font-weight: 500;
}

.card-percent.up {
  color: #52c41a;
}

.card-percent.down {
  color: #f5222d;
}

.chart-card {
  height: 100%;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.05);
}

.chart-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-text {
  font-size: 14px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

.chart-container {
  width: 100%;
  height: 300px;
}

.load-details {
  padding: 16px;
}

.load-item {
  text-align: center;
}

.load-label {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  margin-bottom: 8px;
}

.load-value {
  font-size: 32px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 8px;
}

.load-time {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

.load-legend {
  display: flex;
  justify-content: space-around;
  margin-top: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
}

.color-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
}

.legend-value {
  margin-left: 6px;
  font-weight: 500;
}
</style>