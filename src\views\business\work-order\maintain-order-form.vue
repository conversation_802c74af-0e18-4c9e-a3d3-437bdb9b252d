<!--
  * 工单
  *
  * @Author:    yourName
  * @Date:      2025-04-11 11:45:56
  * @Copyright  bdic
-->
<template>
  <a-modal
      :title="form.id ? '编辑' : '添加'"
      :width="500"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }" >
        <a-form-item label="主键编号"  name="id">
          <a-input-number style="width: 100%" v-model:value="form.id" placeholder="主键编号" />
        </a-form-item>
        <a-form-item label="维修单id"  name="faultId">
          <a-input-number style="width: 100%" v-model:value="form.faultId" placeholder="维修单id" />
        </a-form-item>
        <a-form-item label="工单编号"  name="orderNumber">
          <a-input-number style="width: 100%" v-model:value="form.orderNumber" placeholder="工单编号" />
        </a-form-item>
        <a-form-item label="记录状态，0：正常，1：删除"  name="deletedFlag">
          <a-input-number style="width: 100%" v-model:value="form.deletedFlag" placeholder="记录状态，0：正常，1：删除" />
        </a-form-item>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { reactive, ref, nextTick } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { maintainOrderApi } from '/@/api/business/work-order/maintain-order-api';
  import { smartSentry } from '/@/lib/smart-sentry';

  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show(rowData) {
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
    }
    // 使用字典时把下面这注释修改成自己的字典字段 有多个字典字段就复制多份同理修改 不然打开表单时不显示字典初始值
    // if (form.status && form.status.length > 0) {
    //   form.status = form.status.map((e) => e.valueCode);
    // }
    visibleFlag.value = true;
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
      id: undefined, //主键编号
      faultId: undefined, //维修单id
      orderNumber: undefined, //工单编号
      deletedFlag: undefined, //记录状态，0：正常，1：删除
  };

  let form = reactive({ ...formDefault });

  const rules = {
      id: [{ required: true, message: '主键编号 必填' }],
      faultId: [{ required: true, message: '维修单id 必填' }],
      orderNumber: [{ required: true, message: '工单编号 必填' }],
      deletedFlag: [{ required: true, message: '记录状态，0：正常，1：删除 必填' }],
  };

  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    console.log(form)
    SmartLoading.show();
    try {
      if (form.id) {
        await maintainOrderApi.update(form);
      } else {
        await maintainOrderApi.add(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  defineExpose({
    show,
  });
</script>
