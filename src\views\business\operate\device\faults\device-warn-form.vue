<!--
  * 设备分析 设备预警 list
  *
  * @Author:    linwj
  * @Date:      2025-04-21 16:30:26
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
  <a-drawer title="设备详情" :width="820" :visible="detailVisible" :body-style="{ paddingBottom: '80px' }" @close="onDetailClose">
    <a-descriptions title="设备基本信息" bordered>
      <a-descriptions-item label="设备名称">{{ baseInfo.deviceName }}</a-descriptions-item>
      <a-descriptions-item label="设备编号">{{ baseInfo.id }}</a-descriptions-item>
      <a-descriptions-item label="安装区域">{{ baseInfo.regionName }}</a-descriptions-item>
      <a-descriptions-item label="出厂日期">{{ baseInfo.createTime }}</a-descriptions-item>
      <a-descriptions-item label="产品名称">{{ baseInfo.productName }}</a-descriptions-item>
      <a-descriptions-item label="品类名称">{{ baseInfo.categoryName }}</a-descriptions-item>
<!--      <a-descriptions-item label="设备状态">运行中</a-descriptions-item>-->
      <a-descriptions-item label="最后维护时间">{{ baseInfo.updateTime }}</a-descriptions-item>
    </a-descriptions>
    <a-divider />

    <h3>故障历史记录</h3>
          <a-timeline>
            <a-timeline-item v-for="(item, index) in faultHistory" :key="index">
              <p>- {{ item.createTime }}</p>
              <p>
                - {{ item.faultRemark }} -
                <a-tag :color="DEVICE_ERROR_STATUS_ENUM.getValue(item.status).color">
                  {{ DEVICE_ERROR_STATUS_ENUM.getValue(item.status).desc }}
                </a-tag>
              </p>
            </a-timeline-item>
          </a-timeline>

    <div class="drawer-footer">
      <a-button style="margin-right: 8px" @click="onDetailClose">关闭</a-button>
    </div>
  </a-drawer>
</template>
<script setup>
  import { reactive, ref, nextTick } from 'vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { iotDeviceApi } from '/@/api/business/iot/device/iot-device-api.js';
  import { faultApi } from '/@/api/business/operate/device/faults/faults-api.js';
  import { DEVICE_ERROR_STATUS_ENUM } from '/@/views/business/maintain/maintain-fault/device_error_status.js';

  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const detailVisible = ref(false);

  function show(rowData) {
    detailVisible.value = true;
    queryBaseInfo(rowData);
    queryFaultHistory(rowData);
  }

  function onDetailClose() {
    detailVisible.value = false;
  }

  // ------------------------ 查询基本信息 ------------------------
  const baseInfo = reactive({});
  async function queryBaseInfo(deviceId) {
    try {
      SmartLoading.show();
      let param = {
        id: deviceId,
      };
      let res = await iotDeviceApi.getOne(param);
      Object.assign(baseInfo, res.data);
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }

  // 故障历史记录
  const faultHistory = ref([]);
  async function queryFaultHistory(deviceId) {
    try {
      SmartLoading.show();
      let res = await faultApi.queryALl(deviceId);
      faultHistory.value = res.data;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }
  defineExpose({
    show,
  });
</script>
