/**
 * 产品信息表 api 封装
 *
 * @Author:    李帅兵
 * @Date:      2025-03-21 21:15:14
 * @Copyright  中山睿数信息技术有限公司 2025
 */
import { get } from 'lodash';
import { postRequest, getRequest } from '/@/lib/axios';

export const iotProductApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/iotProduct/list', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/iotProduct/save', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/iotProduct/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return postRequest(`/iotProduct/remove`,id);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
      return postRequest('/iotProduct/remove', idList);
  },

  // 获取随机产品编码
  getCode:() => {
    return getRequest(`/iotProduct/getRandomProductKey`);
  },

  // 获取产品密钥
  getSecret:() => {
    return getRequest(`/iotProduct/getRandomProductSecret`);
  },

  // 获取产品物模型
  queryProductDataByKey: (productKey) => {
    return getRequest(`/iotProduct/version/publishedByKey/${productKey}`);
  },

  // 获取产品列表
  list: (param) => {
    return postRequest(`/iotProduct/list`, param);
  },

  // 自定义分页
  page: (param) => {
    return postRequest('/iotProduct/page', param);
  },
  

  // 获取产品详情
  getDetail: (param) => {
    return postRequest('/iotProduct/detail', param);
  },
  //修改产品名称
  updateName: (param)=>{
    return postRequest('/iotProduct/updateName',param)
  },
  //获取产品topic类列表
  getTopicList:(param)=>{
    return postRequest('/iotProduct/topic/list', param)
  },
  //获取产品设备子集
  getSubList:(param)=>{
    return postRequest('/iotProduct/sub/device', param)
  }
};
