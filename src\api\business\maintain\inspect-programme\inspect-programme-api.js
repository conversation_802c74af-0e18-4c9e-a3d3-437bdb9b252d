import { postRequest, getRequest } from '/@/lib/axios';

/**
 * 巡检方案相关API
 */
export const inspectProgrammeApi = {
    /**
     * 新增巡检方案
     * @param {Object} params - 巡检方案信息
     * @returns {Promise} - 请求的Promise对象
     */
    add(params) {
        return postRequest('/inspectProgramme/add', params);
    },

    /**
     * 更新巡检方案
     * @param {Object} params - 巡检方案信息
     * @returns {Promise} - 请求的Promise对象
     */
    update(params) {
        return postRequest('/inspectProgramme/update', params);
    },

    /**
     * 分页查询巡检方案
     * @param {Object} params - 查询参数
     * @returns {Promise} - 请求的Promise对象
     */
    queryPage(params) {
        return postRequest('/inspectProgramme/queryPage', params);
    },

    /**
     * 批量删除巡检方案
     * @param {Array|String} ids - 巡检方案ID数组或单个ID
     * @returns {Promise} - 请求的Promise对象
     */
    batchDelete(ids) {
        return postRequest('/inspectProgramme/batchDelete', ids);
    },

    /**
     * 根据ID查询巡检方案详情
     * @param {String|Number} id - 巡检方案ID
     * @returns {Promise} - 请求的Promise对象
     */
    getById(id) {
        return getRequest(`/inspectProgramme/query/${id}`);
    },
    /**删除巡检方案id**/
    delete(id) {
        return getRequest(`/inspectProgramme/delete/${id}`);
    },
    /**查询巡检方案列表**/
    queryList() {
        return postRequest('/inspectProgramme/queryList');
    },
};