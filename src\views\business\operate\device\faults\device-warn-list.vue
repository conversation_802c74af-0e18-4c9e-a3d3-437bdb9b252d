<!--
  * 设备故障历史表
  *
  * @Author:    linwj
  * @Date:      2025-05-14 20:54:56
  * @Copyright  中山睿数信息技术有限公司
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="设备编号" class="smart-query-form-item">
        <a-input style="width: 200px" placeholder="请输入设备名称" v-model:value="queryForm.deviceId" />
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="onSearch">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格 begin ----------->
    <a-table
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="id"
      bordered
      :loading="tableLoading"
      :pagination="false"
    >
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button @click="showForm(record.deviceId)" type="link">详情</a-button>
          </div>
        </template>
        <template v-if="column.dataIndex === 'status'">
          <a-tag :color="DEVICE_ERROR_STATUS_ENUM.getValue(record.status).color">
            {{ DEVICE_ERROR_STATUS_ENUM.getValue(record.status).desc }}
          </a-tag>
        </template>
      </template>
    </a-table>
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>
    <ErrorHistory ref="formRef" />
  </a-card>
</template>
<script setup>
import { reactive, ref, onMounted } from 'vue';
import { faultApi } from '/@/api/business/operate/device/faults/faults-api.js';
import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
import { smartSentry } from '/@/lib/smart-sentry';
import { DEVICE_ERROR_STATUS_ENUM } from '/@/views/business/maintain/maintain-fault/device_error_status.js';
import ErrorHistory from '/@/views/business/operate/device/faults/device-warn-form.vue';
// ---------------------------- 表格列 ----------------------------

const columns = ref([
  {
    title: '设备编号',
    dataIndex: 'deviceId',
    ellipsis: true,
  },
  {
    title: '产品名称',
    dataIndex: 'productName',
    ellipsis: true,
  },
  {
    title: '预警级别',
    dataIndex: 'status',
    ellipsis: true,
  },
  {
    title: '预警简述',
    dataIndex: 'faultRemark',
    ellipsis: true,
  },
  {
    title: '发生时间',
    dataIndex: 'createTime',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 90,
  },
]);

// ---------------------------- 查询数据表单和方法 ----------------------------

const queryFormState = {
  pageNum: 1,
  pageSize: 10,
  deviceId: '', // 设备编号
};
// 查询表单form
const queryForm = reactive({ ...queryFormState });
// 表格加载loading
const tableLoading = ref(false);
// 表格数据
const tableData = ref([]);
// 总数
const total = ref(0);

// 重置查询条件
function resetQuery() {
  let pageSize = queryForm.pageSize;
  Object.assign(queryForm, queryFormState);
  queryForm.pageSize = pageSize;
  queryData();
}

// 搜索
function onSearch(){
  queryForm.pageNum = 1;
  queryData();
}

// 查询数据
async function queryData() {
  tableLoading.value = true;
  try {
    let queryResult = await faultApi.queryPage(queryForm);
    tableData.value = queryResult.data.list;
    total.value = queryResult.data.total;
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    tableLoading.value = false;
  }
}


onMounted(queryData);

// ---------------------------- 添加/修改 ----------------------------
const formRef = ref();

function showForm(data) {
  formRef.value.show(data);
}

</script>
