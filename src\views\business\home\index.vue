<template>
  <a-layout class="custom-layout">
    <!-- 顶部六个小盒子 -->
    <div class="top-boxes">
      <!-- 管理面积 -->
      <div class="small-box">
        <div class="image">
          <img src="/@/assets/images/home/<USER>" alt="管理面积图标" />
        </div>
        <div class="content" style="position: relative">
          <div class="title">管理面积</div>
          <div class="number">{{ dashboardData.totalArea }}</div>
          <div class="unit">单位：㎡</div>
        </div>
      </div>

      <!-- 边缘计算节点 -->
      <div class="small-box">
        <div class="image">
          <img src="/@/assets/images/home/<USER>" alt="边缘计算节点图标" />
        </div>
        <div class="content" style="position: relative">
          <div class="title">边缘计算节点</div>
          <div class="number">{{ dashboardData.edgeNodeNum }}</div>
          <div class="unit">单位：个</div>
        </div>
      </div>

      <!-- 工单/报警数量 -->
      <div class="small-box">
        <div class="image">
          <img src="/@/assets/images/home/<USER>" alt="工单图标" />
        </div>
        <div class="content" style="position: relative">
          <div class="title">工单数量</div>
          <div class="number">{{ dashboardData.workOrderNum }}</div>
          <div class="unit">单位：个</div>
        </div>
      </div>

      <!-- 区域总数 -->
      <div class="small-box">
        <div class="image">
          <img src="/@/assets/images/home/<USER>" alt="区域图标" />
        </div>
        <div class="content" style="position: relative">
          <div class="title">区域总数</div>
          <div class="number">{{ dashboardData.totalAreaCount }}</div>
          <div class="unit">单位：个</div>
        </div>
      </div>

      <!-- 设备总数 -->
      <div class="small-box">
        <div class="image">
          <img src="/@/assets/images/home/<USER>" alt="设备图标" />
        </div>
        <div class="content" style="position: relative">
          <div class="title">设备总数</div>
          <div class="number">{{ dashboardData.deviceNum }}</div>
          <div class="unit">单位：个</div>
        </div>
      </div>

      <!-- 路灯总数 -->
      <div class="small-box">
        <div class="image">
          <img src="/@/assets/images/home/<USER>" alt="路灯图标" />
        </div>
        <div class="content" style="position: relative">
          <div class="title">路灯总数</div>
          <div class="number">{{ dashboardData.lightNum }}</div>
          <div class="unit">单位：个</div>
        </div>
      </div>
    </div>

    <!-- 底部大布局 -->
    <div class="bottom-container">
      <!-- 左边大盒子 -->
      <div class="left-large-box">
        <a-card title="定位地图" :bordered="false" style="width: 100%">
          <div style="height: 58vh">
            <Map style="height: 100%" />
          </div>
        </a-card>
      </div>

      <!-- 右边两个垂直盒子 -->
      <div class="right-boxes">
        <div class="right-top-box">
          <a-card title="报警信息" :bordered="false" style="width: 100%">
            <a-tabs v-model:activeKey="activeTab" style="margin-top: -20px; padding: 0 10px" @change="handleTabChange">
              <a-tab-pane key="warning" tab="警告">
                <a-table
                  :columns="columns"
                  :data-source="warningData"
                  row-key="id"
                  :bordered="false"
                  :pagination="false"
                  :loading="loading"
                  style="overflow-y: auto; height: 25vh"
                >
                  <template #bodyCell="{  record, column }">
                    <template v-if="column.dataIndex === 'faultHandleStatus'">
                      {{ $smartEnumPlugin.getDescByValue('ERROR_STATUS',  record.faultHandleStatus) }}
                    </template>
                  </template>
                </a-table>
              </a-tab-pane>
              <a-tab-pane key="alert" tab="预警">
                <a-table
                  :columns="columns"
                  :data-source="alertData"
                  row-key="id"
                  :bordered="false"
                  :pagination="false"
                  :loading="loading"
                  style="overflow-y: auto; height: 25vh"
                >
                  <template #bodyCell="{  record, column }">
                    <template v-if="column.dataIndex === 'faultHandleStatus'">
                      {{ $smartEnumPlugin.getDescByValue('ERROR_STATUS',  record.faultHandleStatus) }}
                    </template>
                  </template>
                </a-table>
              </a-tab-pane>
              <a-tab-pane key="advice" tab="建议">
                <a-table
                  :columns="columns"
                  :data-source="adviceData"
                  row-key="id"
                  :bordered="false"
                  :pagination="false"
                  :loading="loading"
                  style="overflow-y: auto; height: 25vh"
                >
                  <template #bodyCell="{  record, column }">
                    <template v-if="column.dataIndex === 'faultHandleStatus'">
                      {{ $smartEnumPlugin.getDescByValue('ERROR_STATUS',  record.faultHandleStatus) }}
                    </template>
                  </template>
                </a-table>
              </a-tab-pane>
              <a-tab-pane key="info" tab="信息">
                <a-table
                  :columns="columns"
                  :data-source="infoData"
                  row-key="id"
                  :bordered="false"
                  :pagination="false"
                  :loading="loading"
                  style="overflow-y: auto; height: 25vh"
                >
                  <template #bodyCell="{  record, column }">
                    <template v-if="column.dataIndex === 'faultHandleStatus'">
                      {{ $smartEnumPlugin.getDescByValue('ERROR_STATUS',  record.faultHandleStatus) }}
                    </template>
                  </template>
                </a-table>
              </a-tab-pane>
            </a-tabs>
          </a-card>
        </div>
        <div class="right-bottom-box">
          <a-card title="数据视窗" :bordered="false" style="width: 100%">
            <!-- 顶部标签页 -->
            <a-tabs v-model:activeKey="activeTab2" style="margin-top: -20px; padding: 0 10px">
              <a-tab-pane key="lamb" tab="路灯状态">
                <div class="status-item">
                  <div class="status-value">
                    <span class="online-count">{{lampCount}}</span>
                    <span class="status-label">路灯状态</span>
                  </div>
                  <div class="status-detail">
                    <span class="online">在线</span>
                    <span class="online-percent">{{ onlineLampCount }}（{{ lampOnlineRate }}%）</span>
                  </div>
                  <a-progress
                    type="circle"
                    :percent="lampOnlineRate"
                    :show-info="false"
                    stroke-color="#52c41a"
                    :trail-color="'#f5222d'"
                    class="status-progress"
                  />
                  <div class="status-legend">
                    <span class="legend-item">
                      <span class="legend-block" style="background-color: #52c41a"></span>
                      在线
                    </span>
                    <span class="legend-item">
                      <span class="legend-block" style="background-color: #f5222d"></span>
                      不在线
                    </span>
                  </div>
                </div>
              </a-tab-pane>

              <a-tab-pane key="wifi" tab="网关状态">
                <div class="status-item">
                  <div class="status-value">
                    <span class="online-count">{{ gatewayCount }}</span>
                    <span class="status-label">网关状态</span>
                  </div>
                  <div class="status-detail">
                    <span class="online">在线</span>
                    <span class="online-percent">{{ gatewayCount }}（{{ gatewayOnlineRate }}%）</span>
                  </div>
                  <a-progress
                    type="circle"
                    :percent="gatewayOnlineRate"
                    :show-info="false"
                    stroke-color="#52c41a"
                    :trail-color="'#f5222d'"
                    class="status-progress"
                  />
                  <div class="status-legend">
                    <span class="legend-item">
                      <span class="legend-block" style="background-color: #52c41a"></span>
                      在线
                    </span>
                    <span class="legend-item">
                      <span class="legend-block" style="background-color: #f5222d"></span>
                      不在线
                    </span>
                  </div>
                </div>
              </a-tab-pane>
            </a-tabs>
          </a-card>
        </div>
      </div>
    </div>
  </a-layout>
</template>

<script setup>
  import { ref, onMounted } from 'vue';
  import { WORK_ORDER_STATUS_ENUM } from '/@/constants/work-order/maintain-order-const';
  import Map from '/@/views/business/light/controlMap/control-map.vue';
  import { indexHomeApi } from '/@/api/system/index-home.js';
  import { ERROR_STATUS } from '/@/views/business/home/<USER>/device_error_status.js';

  // 顶部六个小盒子的数据
  const dashboardData = ref({
    totalArea: 0, // 管理面积
    edgeNodeNum: 0, // 边缘计算节点
    workOrderNum: 0, // 工单数量
    totalAreaCount: 0, // 区域总数
    deviceNum: 0, // 设备总数
    lightNum: 0, // 路灯数量
  });

  // 报警信息相关
  const activeTab = ref('alert');
  const activeTab2 = ref('lamb');
  const loading = ref(false);
  const alertData = ref([]);
  const warningData = ref([]);
  const adviceData = ref([]);
  const infoData = ref([]);

  const columns = ref([
    { title: '区域名', dataIndex: 'regionName', key: 'regionName' },
    { title: '设备名', dataIndex: 'deviceName', key: 'deviceName' },
    { title: '故障信息', dataIndex: 'faultRemark', key: 'faultRemark' },
    { title: '处理状态', dataIndex: 'faultHandleStatus', key: 'faultHandleStatus' },
  ]);

  // 获取顶部数据
  async function getHomeTopData() {
    try {
      const params = {

      };

      const res = await indexHomeApi.homeTopData(params);

      if (res.data) {
        // 处理管理面积（从totalArea对象中获取第一个数值）
        const totalAreaValue = res.data.totalArea ? Number(Object.values(res.data.totalArea)[0]) || 0 : 0;

        // 处理区域总数（totalArea对象的键数量）
        const totalAreaCount = res.data.totalArea ? String(Object.keys(res.data.totalArea)) : 0;
        dashboardData.value = {
          totalArea: totalAreaValue,
          totalAreaCount: totalAreaCount,
          deviceNum: res.data.deviceNum || 0,
          edgeNodeNum: res.data.edgeNodeNum || 0,
          lightNum: res.data.lightNum || 0,
          workOrderNum: res.data.workOrderNum || 0,
        };
      }
    } catch (e) {
      console.error('获取顶部数据失败:', e);
    }
  }

  // 根据tab键获取对应的status值
  const getStatusByTab = (tabKey) => {
    const statusMap = {
      alert: 'alert',
      warning: 'warning',
      advice: 'advice',
      info: 'info',
    };
    return statusMap[tabKey] || '';
  };

  // 根据value值返回对应的desc描述
  function getStatusDesc(statusValue) {
    const numValue = typeof statusValue === 'string' ? parseInt(statusValue, 10) : statusValue;

    if (isNaN(numValue)) return statusValue;

    const statusEntry = Object.values(WORK_ORDER_STATUS_ENUM).find((item) => item.value === numValue);
    return statusEntry ? statusEntry.desc : statusValue;
  }

  // 获取报警数据
  const fetchWarnData = async (tabKey) => {
    loading.value = true;
    try {
      const status = getStatusByTab(tabKey);
      const params = {
        pageNum: 1,
        pageSize: 100,
        status: status,
      };

      const res = await indexHomeApi.queryRegionWarnPage(params);
      console.log('获取报警数据成功:', res);

      const processedData = (res.data.list || []).map((item) => ({
        ...item,
        orderStatus: getStatusDesc(item.orderStatus),
      }));
      switch (tabKey) {
        case 'alert':
          alertData.value = processedData;
          break;
        case 'warning':
          warningData.value = processedData;
          break;
        case 'advice':
          adviceData.value = processedData;
          break;
        case 'info':
          infoData.value = processedData;
          break;
      }
    } catch (error) {
      console.error('获取报警数据失败:', error);
    } finally {
      loading.value = false;
    }
  };

  // tab切换事件
  const handleTabChange = (tabKey) => {
    activeTab.value = tabKey;
    fetchWarnData(tabKey);
  };
  const onlineLampCount=ref(0);
  const onlineGatewayCount=ref(0);
  const lampOnlineRate=ref(0);
  const gatewayOnlineRate=ref(0);
  const lampCount=ref(0);
  const gatewayCount=ref(0);
async function getData(){
  const res=await indexHomeApi.queryDeviceOnlineData();
  lampCount.value=res.data.lampCount;
  onlineLampCount.value=res.data.onlineLampCount
  onlineGatewayCount.value=res.data.onlineGatewayCount
  gatewayCount.value=res.data.gatewayCount;
  lampOnlineRate.value=res.data.lampOnlineRate
  gatewayOnlineRate.value=res.data.gatewayOnlineRate
  
}
  // 初始化加载数据
  onMounted(() => {
    getHomeTopData();
    fetchWarnData(activeTab.value);
    getData();
  });
</script>

<style scoped>
  /* 保持原有样式不变 */
  .custom-layout {
    padding: 16px;
    box-sizing: border-box;
  }

  .top-boxes {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 16px;
    height: 15vh;
    margin-bottom: 16px;
  }

  .small-box {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    background-color: #fff;
    border: 1px solid #1677ff;
    border-radius: 10px;
    display: flex;
    align-items: center;
    padding: 10px;
    gap: 15px;
    height: 100%;
  }

  .image {
    flex: 0 0 auto;
    width: 50%;
    height: 90%;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .image img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }

  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 5px;
  }

  .title {
    font-size: 20px;
    font-weight: bold;
  }

  .number {
    font-size: 22px;
    font-weight: bold;
    margin-top: 20%;
    color: #1677ff;
  }

  .unit {
    font-size: 14px;
    color: gray;
    position: absolute;
    bottom: -20px;
    right: 6px;
  }

  .bottom-container {
    display: flex;
    gap: 16px;
  }

  .left-large-box {
    flex: 2;
    border: 1px solid #91d5ff;
    border-radius: 4px;
    display: flex;
  }

  .right-boxes {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 6px;
    height: 100%;
  }

  .right-top-box,
  .right-bottom-box {
    flex: 1;
    border: 1px solid #b7eb8f;
    border-radius: 4px;
    display: flex;
    height: 45%;
  }

  .right-bottom-box {
    height: 55%;
    border: 1px solid #ffbb96;
  }

  .status-item {
    flex: 1;
    padding: 0 16px;
    height: 100%;
    text-align: center;
    padding-bottom: 20%;
  }

  .status-title {
    font-size: 16px;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.85);
  }

  .status-value {
    margin-bottom: 8px;
  }

  .online-count,
  .offline-count {
    font-size: 24px;
    font-weight: bold;
    margin-right: 8px;
  }

  .online-count {
    color: #52c41a;
  }

  .offline-count {
    color: #f5222d;
  }

  .status-label {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
  }

  .status-detail {
    margin-bottom: 8px;
  }

  .online {
    color: #52c41a;
    margin-right: 8px;
  }

  .online-percent {
    color: rgba(0, 0, 0, 0.65);
  }

  .status-progress {
    margin: 8px 0;
  }

  .status-legend {
    display: flex;
    justify-content: center;
    gap: 16px;
  }

  .legend-item {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.65);
  }

  .legend-block {
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: 4px;
    border-radius: 2px;
  }
</style>