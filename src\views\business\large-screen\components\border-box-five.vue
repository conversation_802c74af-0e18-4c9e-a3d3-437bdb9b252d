<template>
  <div ref="borderBoxContainer" class="border-box-container">
    <Decoration7 class="top-left-decoration" :color="['#18abd9', '#1ba6d0']">
      <text style="margin:1vw;">当前区域下设备总能耗（近七天）</text>
    </Decoration7>
    <div class="border-box-content">
      <BorderBox10 :color="['#3896b3', '#329bbb']">
        <div class="content">
          <capsule-chart :config="chartConfig" class="chart" />
        </div>
      </BorderBox10>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { BorderBox10, Decoration7, CapsuleChart } from '@kjgl77/datav-vue3';
import dayjs from 'dayjs';

const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
});

// ------------------------------------------边框
const borderBoxContainer = ref(null);

onMounted(() => {
  if (!borderBoxContainer.value) {
    console.error('BorderBox container ref not exist');
    return;
  }
  console.log('BorderBox container mounted:', borderBoxContainer.value);
});

//---------------------------------------------------------------数据处理
// 初始化图表颜色和数据
const chartData = ref([]);
const chartConfig = ref({});

watch(() => props.list, () => {
  initializeData();
  console.log('initializeData', chartData.value);
});

// 初始化数据
const initializeData = () => {
  chartData.value = props.list.map(item => ({
    name: item.x,
    value: item.y
  }));
  chartConfig.value = {
    data: chartData.value,
    colors: ['#fcc200', '#02d6e9', '#017aff', '#e83958', '#7b00ff'], // 胶囊柱图的颜色数组
    showValue:  true,
    unit: 'kWh', // 千瓦时
    labelNum: 6, // 标签数量
  };
};

// // 格式化日期
// const formatData = (data) => {
//   return dayjs(data).format('MM-DD');
// };
</script>

<style scoped>
.border-box-container {
  position: absolute;
  top: 36vh;
  right: 1vw;
  width: 25vw;
  height: 28vh;
  z-index: 1000;
}

.top-left-decoration {
  position: absolute;
  color: white;
  font-size: 1.1vw;
  height: 3vh;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.border-box-content {
  position: absolute;
  top: 4.5vh;
  left: 0;
  width: 100%;
  height: calc(100% - 4.5vh);
}

.content {
  width: 100%;
  height: 100%;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chart {
  width: 90%;
  height: 90%;
}
</style>