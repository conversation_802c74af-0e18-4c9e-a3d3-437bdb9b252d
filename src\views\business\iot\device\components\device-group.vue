<template>
    <div>
      <div class="header-extra">
        <a-space>
          <span>设备分组: {{ leftData.length }}</span>
          <span>未分组设备: {{ rightData.length }}</span>
        </a-space>
      </div>
      
      <a-transfer
        v-model:target-keys="targetKeys"
        :data-source="formattedAllDevices"
        :titles="['设备分组', '未分组设备']"
        :row-key="record => record.key"
        @change="handleTransferChange"
        :list-style="{
          width: '48%',
          height: transferHeight
        }"
      >
        <template #children="{ direction, selectedKeys: currentSelectedKeys, onItemSelect }">
          <a-table
            :columns="tableColumns"
            :data-source="direction === 'left' ? leftData : rightData"
            :row-selection="{
              selectedRowKeys: currentSelectedKeys,
              onSelect: (record, selected) => {
                onItemSelect(record.key, selected);
                updateSelectedKeys();
              },
              type: 'checkbox',
              getCheckboxProps: record => ({ disabled: false })
            }"
            :pagination="false"
            size="small"
            bordered
            style="width: 100%"
            :scroll="{ y: tableScrollY }"
            @row-click="(record) => {
              const isSelected = currentSelectedKeys.includes(record.key);
              onItemSelect(record.key, !isSelected);
              updateSelectedKeys();
            }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'deviceStatus'">
                <a-tag :color="getStatusColor(record.status)">
                  {{ getStatusText(record.status) }}
                </a-tag>
              </template>
            </template>
          </a-table>
        </template>
      </a-transfer>
    </div>
  </template>
  
  <script setup>
  import { ref, computed, onMounted , watch } from 'vue';
  import { iotDeviceApi } from '/@/api/business/iot/device/iot-device-api.js';
  
  const targetKeys = ref([]);
  const selectedKeys = ref([]);
  const allDevices = ref([]);
  const selectedDevices = ref([]);
  const props = defineProps({
  deviceId: {
        type: String, // 假设 deviceDetail.id 是字符串类型
        required: true, // 表示 deviceId 是必需的
    },
  });
  const deviceId = computed(() => props.deviceId);

  
  // 全屏相关状态
  const isFullscreen = ref(true);
  const transferHeight = computed(() => isFullscreen.value ? 'calc(80vh - 200px)' : '500px');
  const tableScrollY = computed(() => isFullscreen.value ? 'calc(100vh - 250px)' : '400px');
  
  const params = {
    pageNum: 1,
    pageSize: 100
  };
  
  // 设备状态映射
  const statusMap = {
    0: { text: '离线', color: 'red' },
    1: { text: '在线', color: 'green' },
    2: { text: '未激活', color: 'orange' }
  };
  
  const getStatusColor = (status) => statusMap[status]?.color || 'default';
  const getStatusText = (status) => statusMap[status]?.text || '未知';
  
  // 安全格式化设备数据
  const safeFormatDevice = (item) => {
    if (!item) return null;
    
    return {
      key: item?.id?.toString() || Math.random().toString(36).substr(2, 9), // 确保有唯一key
      title: item.deviceName || '未知设备',
      deviceName: item.deviceName || '未知设备',
      productName: item.productName || '',
      categoryName: item.categoryName || '',
      status: item.deviceStatus ?? 0, // 使用空值合并运算符
      description: item.description || ''
    };
  };
  
  // 格式化所有设备数据（带错误处理）
  const formattedAllDevices = computed(() => {
    try {
      return allDevices.value
        .map(item => safeFormatDevice(item))
        .filter(item => item && item.key); // 过滤无效数据
    } catch (error) {
      console.error('格式化设备数据出错:', error);
      return [];
    }
  });
  
  // 格式化已选设备数据（带错误处理）
  const formattedSelectedDevices = computed(() => {
    try {
      return selectedDevices.value
        .map(item => safeFormatDevice(item))
        .filter(item => item && item.key);
    } catch (error) {
      console.error('格式化已选设备出错:', error);
      return [];
    }
  });
  
  const tableColumns = [
    {
      title: '设备分组',
      dataIndex: 'deviceName',
      key: 'deviceName',
      width: 200,
      ellipsis: true
    },
    {
      title: '分组ID',
      dataIndex: 'deviceSecret',
      key: 'deviceSecret',
      width: 150,
      ellipsis: true
    },
    {
      title: '添加时间',
      dataIndex: 'productName',
      key: 'productName',
      width: 150,
      ellipsis: true
    },
    {
      title: '操作',
      dataIndex: 'deviceType',
      key: 'deviceType',
      width: 100
    }
  ];
  
  // 计算选择数量（带空值处理）
  const leftData = computed(() => {
    try {
      return formattedAllDevices.value.filter(item => 
        item && item.key && !targetKeys.value.includes(item.key)
      );
    } catch (error) {
      console.error('计算左侧数据出错:', error);
      return [];
    }
  });
  
  const rightData = computed(() => {
    try {
      return formattedSelectedDevices.value.filter(item => 
        item && item.key && targetKeys.value.includes(item.key)
      );
    } catch (error) {
      console.error('计算右侧数据出错:', error);
      return [];
    }
  });
  
  // 更新选中keys
  const updateSelectedKeys = () => {
    console.log('当前选中的keys:', selectedKeys.value);
  };
  
  //记录提交数组
  let arr  = [];

  const handleTransferChange = (nextTargetKeys, direction, moveKeys) => {
    console.log('目标keys变化:', nextTargetKeys);//未被选的
    console.log('移动方向:', direction);
    console.log('移动的keys:', moveKeys);//选择的
    targetKeys.value = nextTargetKeys;
    if(direction === 'right'){
        arr = nextTargetKeys;
        console.log("提交数组右移",arr)
    }else if(direction === 'left'){
        arr = moveKeys;
        console.log("提交数组左移",arr)
    }
  };
  
//   // 安全获取设备子集
//   const fetchAllDevices = async () => {
//     try {
//         console.log("123213123213", deviceId.value)
//       const res = await iotDeviceApi.getSubDevice(deviceId.value);
//       console.log('API原始数据:', res); // 调试日志
//       allDevices.value = Array.isArray(res?.data) ? res.data : [];
//     } catch (error) {
//       console.error('获取设备失败:', error);
//       allDevices.value = [];
//     }
//   };
  
//   // 安全获取未分配设备的子设备
//   const fetchSelectedDevices = async () => {
//     try {
//       const res = await iotDeviceApi.getNoGatewayDevice();
//       console.log('未分配设备原始数据:', res); // 调试日志
//       selectedDevices.value = Array.isArray(res?.data) ? res.data : [];
//       targetKeys.value = selectedDevices.value
//         .map(item => item?.id?.toString())
//         .filter(key => key && typeof key === 'string');
//     } catch (error) {
//       console.error('获取未分配设备失败:', error);
//       selectedDevices.value = [];
//       targetKeys.value = [];
//     }
//   };

// //   fetchAllDevices();
//   watch(deviceId, async (newValue) => {
//     if (typeof newValue === 'string' && newValue !== '') {
//         await fetchAllDevices();
//     }
//     }, {
//     immediate: true, // 立即执行一次，确保初始化时调用
//   });
//   fetchSelectedDevices();
  
  </script>
  
  <style scoped>
  .header-extra {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-end;
  }
  
  .ant-transfer {
    display: flex;
    justify-content: space-between;
  }
  
  .ant-table-row {
    cursor: pointer;
  }
  
  .ant-table-row:hover {
    background-color: #f5f5f5;
  }
  
  :deep(.ant-transfer-list) {
    flex: 1;
    margin: 0 8px;
  }
  </style>