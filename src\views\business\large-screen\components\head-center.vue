<template>
  <div ref="headCenter" class="head-center">
    <div class="top-decoration">
      智慧灯杆数据可视化监控平台
    </div>
    <div class="center-container">
      <Decoration2 :dur="3" class="left-decoration" />
      <Decoration5 :dur="3" class="center-decoration" />
      <Decoration2 :dur="3" class="right-decoration" />
    </div>
    <div class="bottom-decoration">
    <Button @click="nextRegion" border="Border2" color="#3896b3" fontSize="23">切换区域：{{current.name}}</Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted,watch } from 'vue';
import { Decoration5, Decoration2,Button } from '@kjgl77/datav-vue3';

const props = defineProps({
  current: {
    type: Object,
    default: () => {},
  },
});

const emits = defineEmits(['nextRegion']);

const nextRegion = () => {
  emits('nextRegion');
};

// -------------------------------------边框
const headCenter = ref(null);

onMounted(() => {
  if (!headCenter.value) {
    console.error('HeadCenter container ref not exist');
    return;
  }
  console.log('HeadCenter container mounted:', headCenter.value);
});
</script>

<style scoped>
.head-center {
  position: absolute;
  top: 5vh;
  left: 50%;
  transform: translate(-50%, -50%);
  width: fit-content;
  height: fit-content;
  z-index: 1000;
}

.top-decoration {
  display: flex;
  justify-content: center;
  margin-top: 4vh;
  font-size: 1.5vw;
  color: #ffffff;
}

.center-container {
  width: 100%;
  display: flex;
  justify-content: space-between;
  height: 4vh;
}

.bottom-decoration {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #ffffff;
}

.right-decoration {
  width: 40vw;
  height: 2vh;
  margin-right: 0.5vw;
}

.left-decoration {
  transform: rotate(-180deg);
  width: 40vw;
  height: 2vh;
  margin-right: 0.5vw;
}

.center-decoration {
  width: 35vw;
  height: 4vh;
}
</style>