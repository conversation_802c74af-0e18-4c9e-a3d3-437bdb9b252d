<!--
  * 巡检单管理
  *
  * @Author:    潘显镇
  * @Date:      2025-04-11
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="设备" class="smart-query-form-item">
        <IotDeviceSelect v-model:value="queryForm.deviceId" style="width: 200px" />
      </a-form-item>
      
      <a-form-item label="巡检时间" class="smart-query-form-item">
        <a-range-picker 
          style="width: 300px"
          v-model:value="timeRange"
          @change="onTimeRangeChange"
          format="YYYY-MM-DD"
          :presets="defaultTimeRanges"
        />
      </a-form-item>
      
      <a-form-item label="巡检结果" class="smart-query-form-item">
        <a-select 
          style="width: 200px" 
          v-model:value="queryForm.inspectionResult" 
          placeholder="请选择巡检结果"
          allowClear
        >
          <a-select-option :value="true">正常</a-select-option>
          <a-select-option :value="false">异常</a-select-option>
        </a-select>
      </a-form-item>
      
      <a-form-item label="巡检人员" class="smart-query-form-item">
        <EmployeeSelect 
          style="width: 200px"
          v-model:value="queryForm.inspectionPersonId" 
          placeholder="请选择巡检人员"
        />
      </a-form-item>
      
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="onSearch">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="showForm()" type="primary" size="small">
          <template #icon>
            <PlusOutlined />
          </template>
          新建
        </a-button>
        <a-button @click="confirmBatchDelete" type="primary" danger size="small" :disabled="selectedRowKeyList.length == 0">
          <template #icon>
            <DeleteOutlined />
          </template>
          批量删除
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <!---------- 表格 begin ----------->
    <a-table
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="id"
      bordered
      :loading="tableLoading"
      :pagination="false"
      :row-selection="{ selectedRowKeys: selectedRowKeyList, onChange: onSelectChange }"
    >
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'inspectionResult'">
          <a-tag :color="text ? 'green' : 'red'">
            {{ text ? '正常' : '异常' }}
          </a-tag>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button @click="showForm(record.id)" type="link">编辑</a-button>
            <a-button @click="showDetailForm(record.id)" type="link">详情</a-button>
            <a-button @click="onDelete(record)" danger type="link">删除</a-button>
          </div>
        </template>
      </template>
    </a-table>
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>

    <InspectForm ref="formRef" @reloadList="queryData" />
    <InspectDetail ref="detailRef" @reloadList="queryData" />
  </a-card>
</template>

<script setup>
  import { reactive, ref, onMounted } from 'vue';
  import { message, Modal } from 'ant-design-vue';
  import { SearchOutlined, ReloadOutlined, PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { inspectApi } from '../../../../api/business/maintain/inspect/inspect-api';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { smartSentry } from '/@/lib/smart-sentry';
  import TableOperator from '/@/components/support/table-operator/index.vue';
  import InspectForm from './components/inspect-form.vue';
  import InspectDetail from './components/inspect-detail.vue';
  import EmployeeSelect from '/@/components/system/employee-select/index.vue';
  import {defaultTimeRanges} from "/@/lib/default-time-ranges.js";
import IotDeviceSelect from '/@/components/business/iot/iot-device-select/index.vue';

  // ---------------------------- 表格列 ----------------------------

  const columns = ref([
    {
      title: '设备名称',
      dataIndex: 'deviceName',
      ellipsis: true,
    },
    {
      title: '巡检方案',
      dataIndex: 'programmeName',
      ellipsis: true,
    },
    {
      title: '巡检时间',
      dataIndex: 'inspectionTime',
      ellipsis: true,
    },
    {
      title: '巡检结果',
      dataIndex: 'inspectionResult',
      ellipsis: true,
    },
    {
      title: '巡检人员',
      dataIndex: 'inspectionPerson',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      ellipsis: true,
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 180,
    },
  ]);

  // ---------------------------- 查询数据表单和方法 ----------------------------

  const queryFormState = {
    deviceId: undefined,
    inspectionTimeBegin: undefined,
    inspectionTimeEnd: undefined,
    inspectionResult: undefined,
    inspectionPersonId: undefined,
    pageNum: 1,
    pageSize: 10,
  };
  
  // 查询表单form
  const queryForm = reactive({ ...queryFormState });
  // 时间范围选择器绑定值
  const timeRange = ref();
  // 表格加载loading
  const tableLoading = ref(false);
  // 表格数据
  const tableData = ref([]);
  // 总数
  const total = ref(0);

  // 时间范围选择变化
  function onTimeRangeChange(dates, dateStrings) {
    queryForm.inspectionTimeBegin = dateStrings[0];
    queryForm.inspectionTimeEnd = dateStrings[1];
  }

  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.pageSize;
    Object.assign(queryForm, queryFormState);
    timeRange.value = undefined;
    queryForm.pageSize = pageSize;
    queryData();
  }

  // 搜索
  function onSearch() {
    queryForm.pageNum = 1;
    queryData();
  }

  // 查询数据
  async function queryData() {
    tableLoading.value = true;
    try {
      let queryResult = await inspectApi.queryPage(queryForm);
      tableData.value = queryResult.data.list;
      total.value = queryResult.data.total;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  onMounted(queryData);

  // ---------------------------- 添加/修改 ----------------------------
  const formRef = ref();

  function showForm(id) {
    formRef.value.show(id);
  }

  // 显示详情
  const detailRef = ref();
  function showDetailForm(id) {
    detailRef.value.show(id);
  }

  // ---------------------------- 单个删除 ----------------------------
  //确认删除
  function onDelete(data) {
    Modal.confirm({
      title: '提示',
      content: '确定要删除该巡检单吗?',
      okText: '删除',
      okType: 'danger',
      onOk() {
        requestDelete(data);
      },
      cancelText: '取消',
      onCancel() {},
    });
  }

  //请求删除
  async function requestDelete(data) {
    SmartLoading.show();
    try {
      await inspectApi.delete(data.id);
      message.success('删除成功');
      queryData();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }

  // ---------------------------- 批量删除 ----------------------------

  // 选择表格行
  const selectedRowKeyList = ref([]);

  function onSelectChange(selectedRowKeys) {
    selectedRowKeyList.value = selectedRowKeys;
  }

  // 批量删除
  function confirmBatchDelete() {
    Modal.confirm({
      title: '提示',
      content: '确定要批量删除这些巡检单吗?',
      okText: '删除',
      okType: 'danger',
      onOk() {
        requestBatchDelete();
      },
      cancelText: '取消',
      onCancel() {},
    });
  }

  //请求批量删除
  async function requestBatchDelete() {
    try {
      SmartLoading.show();
      await inspectApi.batchDelete(selectedRowKeyList.value);
      message.success('删除成功');
      queryData();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }
</script>