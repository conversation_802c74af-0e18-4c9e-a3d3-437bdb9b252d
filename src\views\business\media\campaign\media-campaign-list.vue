<!--
  * 投放计划
  *
  * @Author:    潘显镇
  * @Date:      2025-05-26 20:44:46
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
    <!---------- 查询表单form begin ----------->
    <a-form class="smart-query-form">
        <a-row class="smart-query-form-row">
            <a-form-item label="投放计划名称" class="smart-query-form-item">
                <a-input style="width: 200px" v-model:value="queryForm.mediaCampaignName" placeholder="投放计划名称" />
            </a-form-item>
            <a-form-item class="smart-query-form-item">
                <a-button type="primary" @click="onSearch">
                    <template #icon>
                        <SearchOutlined />
                    </template>
                    查询
                </a-button>
                <a-button @click="resetQuery" class="smart-margin-left10">
                    <template #icon>
                        <ReloadOutlined />
                    </template>
                    重置
                </a-button>
            </a-form-item>
        </a-row>
    </a-form>
    <!---------- 查询表单form end ----------->

    <a-card size="small" :bordered="false" :hoverable="true">
        <!-- 状态标签页 begin -->
        <a-tabs v-model:activeKey="activeTab" type="card" @change="handleTabChange">
            <a-tab-pane key="ALL" tab="全部"></a-tab-pane>
            <a-tab-pane key="PENDING" tab="未提交"></a-tab-pane>
            <a-tab-pane key="SUBMITTED" tab="待审核"></a-tab-pane>
            <a-tab-pane key="REJECTED" tab="审核不通过"></a-tab-pane>
            <a-tab-pane key="APPROVED" tab="审核通过"></a-tab-pane>
            <a-tab-pane key="ISSUED" tab="已下达"></a-tab-pane>
            <a-tab-pane key="COMPLETED" tab="已结束"></a-tab-pane>
        </a-tabs>
        <!-- 状态标签页 end -->

        <!---------- 表格操作行 begin ----------->
        <a-row class="smart-table-btn-block">
            <div class="smart-table-operate-block">
                <a-button @click="showForm()" type="primary" size="small">
                    <template #icon>
                        <PlusOutlined />
                    </template>
                    新建
                </a-button>
                <!--TODO:删掉计划下达相关代码-->
                <!--                <a-button @click="showIssueDeviceModal()" type="primary" size="small">-->
                <!--                    <template #icon>-->
                <!--                        <ArrowDownOutlined />-->
                <!--                    </template>-->
                <!--                    计划下达-->
                <!--                </a-button>-->
                <a-button @click="confirmBatchDelete" type="primary" danger size="small"
                    :disabled="selectedRowKeyList.length == 0">
                    <template #icon>
                        <DeleteOutlined />
                    </template>
                    批量删除
                </a-button>
            </div>
            <div class="smart-table-setting-block">
                <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
            </div>
        </a-row>
        <!---------- 表格操作行 end ----------->

        <!---------- 表格 begin ----------->
        <a-table size="small" :dataSource="tableData" :columns="columns" rowKey="id" bordered :loading="tableLoading"
            :pagination="false" :row-selection="{ selectedRowKeys: selectedRowKeyList, onChange: onSelectChange }">
            <template #bodyCell="{ text, record, column }">
                <template v-if="column.dataIndex === 'reviewStatus'">
                    {{ MEDIA_STATUS_ENUM.getDesc(text) }}
                </template>

                <template v-if="column.dataIndex === 'action'">
                    <div class="smart-table-operate">
                        <a-button v-if="record.reviewStatus === 'PENDING' || record.reviewStatus==='REJECTED'"
                            @click="onSubmit(record)" type="link" size="small">提交</a-button>
                        <a-button v-if="record.reviewStatus==='SUBMITTED'" @click="showReviewModal(record)" type="link"
                            size="small">审核</a-button>
                        <a-button v-if="record.reviewStatus === 'APPROVED'" @click="onIssue(record)" type="link"
                            size="small">下达</a-button>
                        <a-button v-if="record.reviewStatus === 'APPROVED'" @click="onUnapprove(record)" type="link"
                            size="small">反审核</a-button>
                        <a-button v-if="record.reviewStatus === 'ISSUED'" @click="onComplete(record)" type="link"
                            size="small">结束任务</a-button>
                        <a-button v-if="record.reviewStatus === 'ISSUED'" @click="onUnIssue(record)" type="link"
                            size="small">取消下达</a-button>
                        <a-button v-if="record.reviewStatus === 'PENDING'|| record.reviewStatus==='REJECTED'"
                            @click="showForm(record)" type="link" size="small">编辑</a-button>
                        <a-button v-if="record.reviewStatus !== 'COMPLETED'" @click="onDelete(record)" danger
                            type="link" size="small">删除</a-button>
                    </div>
                </template>
            </template>
        </a-table>
        <!---------- 表格 end ----------->

        <div class="smart-query-table-page">
            <a-pagination showSizeChanger showQuickJumper show-less-items :pageSizeOptions="PAGE_SIZE_OPTIONS"
                :defaultPageSize="queryForm.pageSize" v-model:current="queryForm.pageNum"
                v-model:pageSize="queryForm.pageSize" :total="total" @change="queryData" @showSizeChange="queryData"
                :show-total="(total) => `共${total}条`" />
        </div>

        <MediaCampaignForm ref="formRef" @reloadList="queryData" />
        <DeviceListModal ref="deviceListModalRef" :isSingleSelect="true" confirmButtonText="下一步"
            @selectDevice="handleDeviceSelected" />
        <IssueCampaignModal ref="issueCampaignModalRef" @issueSuccess="handleIssueSuccess" @goBack="handleGoBack" />
        <ReviewCampaignModal ref="reviewModalRef" @reviewSuccess="queryData" />
    </a-card>
</template>
<script setup>
    import { reactive, ref, onMounted, nextTick } from 'vue';
    import { message, Modal } from 'ant-design-vue';
    import { SmartLoading } from '/@/components/framework/smart-loading';
    import { mediaCampaignApi } from '/@/api/business/media/campaign/media-campaign-api';
    import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
    import { smartSentry } from '/@/lib/smart-sentry';
    import TableOperator from '/@/components/support/table-operator/index.vue';
    import MediaCampaignForm from './components/media-campaign-form.vue';
    import DeviceListModal from './components/device-list-modal.vue';
    import IssueCampaignModal from './components/issue-campaign-modal.vue';
    import ReviewCampaignModal from './components/review-campaign-modal.vue';
    import { MEDIA_STATUS_ENUM } from '/@/constants/business/media/material/media-material-const';
    import { SearchOutlined, ReloadOutlined, PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue';
    // ---------------------------- 标签页状态 ----------------------------
    const activeTab = ref('ALL');
    
    // 处理标签页切换
    function handleTabChange(key) {
        activeTab.value = key;
        if (key === 'ALL') {
            queryForm.reviewStatus = undefined;
        } else {
            queryForm.reviewStatus = key;
        }
        queryForm.pageNum = 1;
        queryData();
    }
    // ---------------------------- 表格列 ----------------------------

    const columns = ref([
        // {
        //     title: 'id',
        //     dataIndex: 'id',
        //     ellipsis: true,
        // },
        {
            title: '投放计划名称',
            dataIndex: 'mediaCampaignName',
            ellipsis: true,
        },
        {
            title: '播放总时长(秒)',
            dataIndex: 'playDuration',
            ellipsis: true,
        },
        {
            title: '审核人',
            dataIndex: 'reviewerName',
            ellipsis: true,
        },
        {
            title: '审核状态',
            dataIndex: 'reviewStatus',
            ellipsis: true,
        },
        {
            title: '审核时间',
            dataIndex: 'reviewTime',
            ellipsis: true,
        },
        {
            title: '审核意见',
            dataIndex: 'reviewComment',
            ellipsis: true,
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            ellipsis: true,
        },
        // {
        //     title: '创建人id',
        //     dataIndex: 'createUserId',
        //     ellipsis: true,
        // },
        // {
        //     title: '更新人id',
        //     dataIndex: 'updateUserId',
        //     ellipsis: true,
        // },
        // {
        //     title: '备注',
        //     dataIndex: 'remark',
        //     ellipsis: true,
        // },
        // {
        //     title: '更新时间',
        //     dataIndex: 'updateTime',
        //     ellipsis: true,
        // },
        // {
        //     title: '记录状态（normal:正常 deleted:删除）',
        //     dataIndex: 'recordStatus',
        //     ellipsis: true,
        // },
        // {
        //     title: '逻辑删除 0 未删除 1删除',
        //     dataIndex: 'deletedFlag',
        //     ellipsis: true,
        // },
        {
            title: '操作',
            dataIndex: 'action',
            fixed: 'right',
            width: 180,
        },
    ]);

    // ---------------------------- 查询数据表单和方法 ----------------------------

    const queryFormState = {
        mediaCampaignName: undefined, //投放计划名称
        reviewStatus: undefined, //审核状态
        pageNum: 1,
        pageSize: 10,
        sortItemList: [{ column: 'create_time', isAsc: false }],
    };
    // 查询表单form
    const queryForm = reactive({ ...queryFormState });
    // 表格加载loading
    const tableLoading = ref(false);
    // 表格数据
    const tableData = ref([]);
    // 总数
    const total = ref(0);

    // 重置查询条件
    function resetQuery() {
        let pageSize = queryForm.pageSize;
        Object.assign(queryForm, queryFormState);
        queryForm.pageSize = pageSize;
        activeTab.value = 'ALL';
        queryData();
    }

    // 搜索
    function onSearch(){
      queryForm.pageNum = 1;
      queryData();
    }

    // 查询数据
    async function queryData() {
        tableLoading.value = true;
        try {
            let queryResult = await mediaCampaignApi.queryPage(queryForm);
            tableData.value = queryResult.data.list;
            total.value = queryResult.data.total;
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            tableLoading.value = false;
        }
    }


    onMounted(queryData);

    // ---------------------------- 添加/修改 ----------------------------
    const formRef = ref();

    function showForm(data) {
        formRef.value.show(data);
    }

    // ---------------------------- 计划下达 ----------------------------
    const deviceListModalRef = ref();
    const issueCampaignModalRef = ref();

    // 显示设备选择模态框
    function showIssueDeviceModal() {
        deviceListModalRef.value.show();
    }

    // 处理设备选择
    function handleDeviceSelected(device) {
        issueCampaignModalRef.value.show(device);
    }

    // 处理计划下达成功
    function handleIssueSuccess() {
        queryData();
    }

    // 处理返回到设备选择
    function handleGoBack() {
      showIssueDeviceModal();
    }

    // ---------------------------- 单个删除 ----------------------------
    //确认删除
    function onDelete(data){
        Modal.confirm({
            title: '提示',
            content: '确定要删除选吗?',
            okText: '删除',
            okType: 'danger',
            onOk() {
                requestDelete(data);
            },
            cancelText: '取消',
            onCancel() {},
        });
    }

    //请求删除
    async function requestDelete(data){
        SmartLoading.show();
        try {
            let deleteForm = {
                goodsIdList: selectedRowKeyList.value,
            };
            await mediaCampaignApi.delete(data.id);
            message.success('删除成功');
            queryData();
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            SmartLoading.hide();
        }
    }

    // ---------------------------- 批量删除 ----------------------------

    // 选择表格行
    const selectedRowKeyList = ref([]);

    function onSelectChange(selectedRowKeys) {
        selectedRowKeyList.value = selectedRowKeys;
    }

    // 批量删除
    function confirmBatchDelete() {
        Modal.confirm({
            title: '提示',
            content: '确定要批量删除这些数据吗?',
            okText: '删除',
            okType: 'danger',
            onOk() {
                requestBatchDelete();
            },
            cancelText: '取消',
            onCancel() {},
        });
    }

    //请求批量删除
    async function requestBatchDelete() {
        try {
            SmartLoading.show();
            await mediaCampaignApi.batchDelete(selectedRowKeyList.value);
            message.success('删除成功');
            queryData();
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            SmartLoading.hide();
        }
    }

    // ---------------------------- 审核和反审核 ----------------------------
    const reviewModalRef = ref();

    // 显示审核模态框
    function showReviewModal(record) {
        reviewModalRef.value.show(record);
    }

    // 提交
    async function onSubmit(record) {
        SmartLoading.show();
        try {
            await mediaCampaignApi.submittedCampaign(record.id);
            message.success('提交成功');
            queryData();
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            SmartLoading.hide();
        }
    }

    // 反审核
    function onUnapprove(record) {
        Modal.confirm({
            title: '提示',
            content: '确定要反审核该投放计划吗?',
            okText: '确定',
            okType: 'primary',
            onOk() {
                requestUnapprove(record);
            },
            cancelText: '取消',
            onCancel() {},
        });
    }

    // 请求反审核
    async function requestUnapprove(record) {
        SmartLoading.show();
        try {
            await mediaCampaignApi.unapprovedCampaign(record.id);
            message.success('反审核成功');
            queryData();
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            SmartLoading.hide();
        }
    }

    // 下达计划
    function onIssue(record) {
      Modal.confirm({
        title: '提示',
        content: '确定要下达该投放计划吗?',
        okText: '确定',
        okType: 'primary',
        onOk() {
          requestIssue(record);
        },
        cancelText: '取消',
        onCancel() {},
      });
    }

    // 请求下达计划
    async function requestIssue(record) {
      SmartLoading.show();
      try {
        await mediaCampaignApi.issueCampaign(record.id);
        message.success('下达成功');
        queryData();
      } catch (e) {
        smartSentry.captureError(e);
      } finally {
        SmartLoading.hide();
      }
    }

    // 取消计划
    function onUnIssue(record) {
      Modal.confirm({
        title: '提示',
        content: '确定要取消下达该投放计划吗?',
        okText: '确定',
        okType: 'primary',
        onOk() {
          requestUnIssue(record);
        },
        cancelText: '取消',
        onCancel() {},
      });
    }

    // 请求取消计划下达
    async function requestUnIssue(record) {
      SmartLoading.show();
      try {
        await mediaCampaignApi.unIssueCampaign(record.id);
        message.success('取消下达成功');
        queryData();
      } catch (e) {
        smartSentry.captureError(e);
      } finally {
        SmartLoading.hide();
      }
    }

    // 结束计划
    function onComplete(record) {
      Modal.confirm({
        title: '提示',
        content: '确定要结束该投放计划吗?',
        okText: '确定',
        okType: 'primary',
        onOk() {
          requestComplete(record);
        },
        cancelText: '取消',
        onCancel() {},
      });
    }

    // 请求下达计划（结束）
    async function requestComplete(record) {
      SmartLoading.show();
      try {
        await mediaCampaignApi.completedIssueCampaign(record.id);
        message.success('结束计划成功');
        queryData();
      } catch (e) {
        smartSentry.captureError(e);
      } finally {
        SmartLoading.hide();
      }
    }
</script>
