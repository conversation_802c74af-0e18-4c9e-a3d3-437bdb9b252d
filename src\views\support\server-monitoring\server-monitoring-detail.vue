<!--
  * 服务器监控详情
  *
  * @Author:    骆伟林
  * @Date:      2025-04-07 21:36:15
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
<a-card size="big" :bordered="false" :hoverable="true">
  <template #title></template>
  <template #extra>
    <a-space>
      <a-button @click="onClose" type="primary">返回</a-button>
    </a-space>
  </template>
  <a-form ref="formRef" :model="basicInfo" :label-col="{ span: 7 }" :wrapper-col="{ span: 11 }"> 
    <a-divider class="divider"><span>基本信息</span></a-divider>
    <a-descriptions>
      <a-descriptions-item label="主机名" name="hostname">
       {{ basicInfo.hostname}}
      </a-descriptions-item>
      <a-descriptions-item label="是否在线" name="status">
        {{ basicInfo.status==='online'?'在线':'离线' }}
      </a-descriptions-item>
      <a-descriptions-item label="IP地址" name="ip">
        {{ basicInfo.ip }}
      </a-descriptions-item>
      <a-descriptions-item label="简介" name="info">
        {{ basicInfo.info }}
      </a-descriptions-item>
      <a-descriptions-item label="备注" name="remark">
        {{ basicInfo.remark }}
      </a-descriptions-item>
      <a-descriptions-item label="创建时间" name="createTime">
        {{ basicInfo.createTime }}
      </a-descriptions-item>
    </a-descriptions>
    <a-divider class="divider"><span  style="margin-bottom: 20px;">详细配置</span></a-divider>
    <a-tabs v-model:activeKey="activeKey" type="card">
  <a-tab-pane key="1" tab="CPU">
    <a-descriptions bordered :column="2">
      <a-descriptions-item label="CPU总使用率">{{ cpu.usage }}</a-descriptions-item>
      <a-descriptions-item label="用户态占用">{{ cpu.user }}</a-descriptions-item>
      <a-descriptions-item label="内核态占用">{{ cpu.system }}</a-descriptions-item>
      <a-descriptions-item label="空闲率">{{ cpu.idle }}</a-descriptions-item>
      <a-descriptions-item label="物理核心数">{{ cpu.cores }}</a-descriptions-item>
      <a-descriptions-item label="1/5/15分钟平均负载">{{ cpu.loadAvg }}</a-descriptions-item>
      <a-descriptions-item label="温度">{{ cpu.temperature }}</a-descriptions-item>
      <a-descriptions-item label="上下文切换次数/秒">{{ cpu.contextSwitches }}</a-descriptions-item>
    </a-descriptions>
  </a-tab-pane>
  <a-tab-pane key="2" tab="内存">
    <a-descriptions bordered :column="2">
      <a-descriptions-item label="物理内存总量">{{ memory.total }}</a-descriptions-item>
      <a-descriptions-item label="已用内存">{{ memory.used }}</a-descriptions-item>
      <a-descriptions-item label="空闲内存">{{ memory.free }}</a-descriptions-item>
      <a-descriptions-item label="缓存内存">{{ memory.cached }}</a-descriptions-item>
      <a-descriptions-item label="交换分区总量">{{ memory.swapTotal }}</a-descriptions-item>
      <a-descriptions-item label="交换分区已用">{{ memory.swapUsed }}</a-descriptions-item>
      <a-descriptions-item label="交换分区剩余">{{ memory.swapFree }}</a-descriptions-item>
      <a-descriptions-item label="内存使用百分比">{{ memory.usagePercent }}</a-descriptions-item>
    </a-descriptions>
  </a-tab-pane>
  <a-tab-pane key="3" tab="磁盘">
    <a-descriptions bordered :column="2">
      <a-descriptions-item label="磁盘总容量">{{ disk.total }}</a-descriptions-item>
      <a-descriptions-item label="已用空间">{{ disk.used }}</a-descriptions-item>
      <a-descriptions-item label="剩余空间">{{ disk.free }}</a-descriptions-item>
      <a-descriptions-item label="读取速度">{{ disk.readSpeed }}</a-descriptions-item>
      <a-descriptions-item label="写入速度">{{ disk.writeSpeed }}</a-descriptions-item>
      <a-descriptions-item label="IO等待时间占比">{{ disk.ioWait }}</a-descriptions-item>
      <a-descriptions-item label="分区详情">
        <div v-for="(partition, index) in disk.partitions" :key="index">
          {{ partition.mount }}: {{ partition.usage }}
        </div>
      </a-descriptions-item>
      <a-descriptions-item label="inode使用率">{{ disk.inodesUsage }}</a-descriptions-item>
    </a-descriptions>
  </a-tab-pane>
  <a-tab-pane key="4" tab="网络">
    <a-descriptions bordered :column="2">
      <a-descriptions-item label="总接收流量">{{ network.inTotal }}</a-descriptions-item>
      <a-descriptions-item label="总发送流量">{{ network.outTotal }}</a-descriptions-item>
      <a-descriptions-item label="TCP连接数">{{ network.tcpConnections }}</a-descriptions-item>
      <a-descriptions-item label="UDP连接数">{{ network.udpConnections }}</a-descriptions-item>
      <a-descriptions-item label="ESTABLISHED状态连接数">{{ network.established }}</a-descriptions-item>
      <a-descriptions-item label="TIME_WAIT状态连接数">{{ network.timeWait }}</a-descriptions-item>
      <a-descriptions-item label="接收错误">{{ network.errors.rx }}</a-descriptions-item>
      <a-descriptions-item label="发送错误">{{ network.errors.tx }}</a-descriptions-item>
    </a-descriptions>
  </a-tab-pane>
  <a-tab-pane key="5" tab="进程">
    <a-descriptions bordered :column="2">
      <a-descriptions-item label="总进程数">{{ processes.total }}</a-descriptions-item>
      <a-descriptions-item label="运行中进程数">{{ processes.running }}</a-descriptions-item>
      <a-descriptions-item label="睡眠中进程数">{{ processes.sleeping }}</a-descriptions-item>
      <a-descriptions-item label="僵尸进程数">{{ processes.zombie }}</a-descriptions-item>
      <a-descriptions-item label="线程总数">{{ processes.threads }}</a-descriptions-item>
      <a-descriptions-item label="CPU占用最高进程">
        {{ processes.cpuTopProcess.name }} (PID: {{ processes.cpuTopProcess.pid }}) - {{ processes.cpuTopProcess.cpu }}
      </a-descriptions-item>
      <a-descriptions-item label="内存占用最高进程">
        {{ processes.memTopProcess.name }} (PID: {{ processes.memTopProcess.pid }}) - {{ processes.memTopProcess.mem }}
      </a-descriptions-item>
      <a-descriptions-item label="关键服务">
        <div v-for="(service, index) in processes.criticalServices" :key="index">
          {{ service }}
        </div>
      </a-descriptions-item>
    </a-descriptions>
  </a-tab-pane>
  <a-tab-pane key="6" tab="安全">
    <a-descriptions bordered :column="2">
      <a-descriptions-item label="上次登录用户">{{ security.lastLogin.user }}</a-descriptions-item>
      <a-descriptions-item label="上次登录IP">{{ security.lastLogin.ip }}</a-descriptions-item>
      <a-descriptions-item label="上次登录时间">{{ security.lastLogin.time }}</a-descriptions-item>
      <a-descriptions-item label="失败登录尝试次数">{{ security.failedAttempts }}</a-descriptions-item>
      <a-descriptions-item label="SSH会话数">{{ security.sshSessions }}</a-descriptions-item>
      <a-descriptions-item label="防火墙状态">{{ security.firewallStatus }}</a-descriptions-item>
      <a-descriptions-item label="允许root登录">{{ security.rootLogin ? '是' : '否' }}</a-descriptions-item>
      <a-descriptions-item label="可疑进程数">{{ security.suspiciousProcesses }}</a-descriptions-item>
      <a-descriptions-item label="最后更新补丁时间">{{ security.lastPatched }}</a-descriptions-item>
      <a-descriptions-item label="漏洞数量">{{ security.vulnerabilityCount }}</a-descriptions-item>
    </a-descriptions>
  </a-tab-pane>
  <a-tab-pane key="7" tab="时间">
    <a-descriptions bordered :column="2">
      <a-descriptions-item label="系统运行时间">{{ time.uptime }}</a-descriptions-item>
      <a-descriptions-item label="启动时间">{{ time.bootTime }}</a-descriptions-item>
      <a-descriptions-item label="NTP同步状态">{{ time.ntpSync ? '已同步' : '未同步' }}</a-descriptions-item>
      <a-descriptions-item label="时区">{{ time.timezone }}</a-descriptions-item>
      <a-descriptions-item label="最后检查时间">{{ time.lastChecked }}</a-descriptions-item>
      <a-descriptions-item label="时钟漂移">{{ time.clockDrift }}</a-descriptions-item>
      <a-descriptions-item label="日期格式">{{ time.dateFormat }}</a-descriptions-item>
      <a-descriptions-item label="时间格式">{{ time.timeFormat }}</a-descriptions-item>
    </a-descriptions>
  </a-tab-pane>
</a-tabs>
  </a-form>
</a-card>
</template>
<script setup>
import { reactive, ref, nextTick, onMounted, computed, watch } from 'vue';
import { useRoute } from 'vue-router';
import { router } from '/@/router';
import { lightSceneApi } from '/@/api/business/light/scene/light-scene-api.js';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { smartSentry } from '/@/lib/smart-sentry';
// import listConfigure from './light-scene-configure.vue';
import { message } from 'ant-design-vue';
import { useUserStore } from '/@/store/modules/system/user';
const userStore = useUserStore();
let dataSource=ref([])
function getDataSource(data){
  dataSource.value=data
}

const route = useRoute();
const basicInfo=ref({
    id: 1,
    hostname: 'web-server-01',
    ip: '*************',
    status: 'online',
    cpuUsage: '24%',
    memoryUsage: '3.2/8 GB',
    diskUsage: '45%',
    networkIn: '12.5 MB/s',
    networkOut: '8.2 MB/s',
    uptime: '15 days',
    lastChecked: '2023-11-20 09:15:33'
  }
)

  let form = reactive({
    basicInfo: {},
    strategyList:[]
  });
function onClose(){
  userStore.closePage(route, router, '/server-monitoring-list');
  router.push({path: '/server-monitoring-list',});
}
const cpu = ref({});
const memory = ref({});
const disk = ref({});
const network = ref({});
const processes = ref({});
const security = ref({});
const time = ref({});
onMounted(() => {
basicInfo.value=route.query
console.log(basicInfo.value);

if(route.query.id===1){
    cpu.value= {
      usage: "32%",                  
      user: "24%",                   
      system: "8%",                  
      idle: "68%",                   
      cores: 8,                      
      loadAvg: "2.1,1.8,1.5", 
      temperature: "65°C",           
      contextSwitches: 125000        
    },

    // =============== 内存模块 ===============
    memory.value= {
      total: "16GB",                 
      used: "9.2GB",                 
      free: "6.8GB",                 
      cached: "3.1GB",               
      swapTotal: "4GB",              
      swapUsed: "0.5GB",             
      swapFree: "3.5GB",             
      usagePercent: "57.5%"          
    },

    // =============== 磁盘模块 ===============
    disk.value= {
      total: "20TB",
      used: "215GB",                 
      free: "19TB",
      readSpeed: "120MB/s",          
      writeSpeed: "45MB/s",          
      ioWait: "5%",                  
      partitions: [                  
        { mount: "/", usage: "44%" },
        { mount: "/data", usage: "62%" }
      ],
      inodesUsage: "28%"             
    },

    // =============== 网络模块 ===============
    network.value= {
      inTotal: "15.2MB/s",           
      outTotal: "9.8MB/s",           
      tcpConnections: 142,           
      udpConnections: 23,            
      established: 85,               
      timeWait: 32,                  
      errors: {                      
        rx: 2,                       
        tx: 1                        
      },
      interfaces: ["eth0", "eth1"]    
    },

    // =============== 进程模块 ===============
    processes.value= {
      total: 128,                     
      running: 12,                    
      sleeping: 110,                  
      zombie: 1,                      
      threads: 512,                   
      cpuTopProcess: {                
        name: "java",
        pid: 3456,
        cpu: "18%"
      },
      memTopProcess: {                
        name: "mysqld",
        pid: 2345,
        mem: "2.1GB"
      },
      criticalServices: ["nginx", "mysql"] 
    },

    // =============== 安全模块 ===============
    security.value= {
      lastLogin: {                    
        user: "admin",
        ip: "*************",
        time: "2023-11-20 09:30:22"
      },
      failedAttempts: 3,              
      sshSessions: 2,                 
      firewallStatus: "active",       
      rootLogin: false,               
      suspiciousProcesses: 0,         
      lastPatched: "2023-11-15",      
      vulnerabilityCount: 2           
    },

    // =============== 时间模块 ===============
    time.value= {
      uptime: "18 days 7:32:11",      
      bootTime: "2023-11-02 08:15:00",
      ntpSync: true,                  
      timezone: "UTC+8",              
      lastChecked: "2023-11-20 10:30:00", 
      clockDrift: "0.2s",             
      dateFormat: "YYYY-MM-DD",       
      timeFormat: "24H"               
    }
}
else if(route.query.id===2){
    cpu.value= {
      usage: "92%",  // 高负载
      user: "65%",
      system: "27%",
      idle: "8%",
      cores: 16,
      loadAvg: "28.4,25.1,18.9",
      temperature: "82°C",  // 高温警告
      contextSwitches: 890000
    },
    memory.value= {
      total: "64GB",
      used: "61GB",  // 内存紧张
      free: "3GB",
      cached: "8GB",
      swapTotal: "8GB",
      swapUsed: "6.5GB",  // 大量使用swap
      swapFree: "1.5GB",
      usagePercent: "95.3%"
    },
    disk.value= {
      total: "20TB",
      used: "1.8TB",  // 磁盘空间不足
      free: "18TB",
      readSpeed: "450MB/s",  // 高IO
      writeSpeed: "380MB/s",
      ioWait: "32%",  // IO瓶颈
      partitions: [
        { mount: "/", usage: "91%" },
        { mount: "/var/lib/mysql", usage: "98%" }
      ],
      inodesUsage: "65%"
    },
    network.value= {
      inTotal: "85MB/s",  // 高流量
      outTotal: "120MB/s",
      tcpConnections: 1242,
      udpConnections: 45,
      established: 892,
      timeWait: 215,
      errors: { rx: 12, tx: 8 },  // 网络错误
      interfaces: ["eth0", "eth1", "bond0"]
    },
    processes.value= {
      total: 342,
      running: 28,
      sleeping: 310,
      zombie: 2,
      threads: 2048,
      cpuTopProcess: {
        name: "mysqld",
        pid: 5678,
        cpu: "87%"
      },
      memTopProcess: {
        name: "mysqld",
        pid: 5678,
        mem: "48GB"
      },
      criticalServices: ["mysql", "keepalived"]
    },
    security.value= {
      lastLogin: {
        user: "dba",
        ip: "*********",
        time: "2023-11-20 08:45:11"
      },
      failedAttempts: 15,  // 异常登录尝试
      sshSessions: 5,
      firewallStatus: "active",
      rootLogin: true,
      suspiciousProcesses: 1,
      lastPatched: "2023-10-30",  // 补丁过期
      vulnerabilityCount: 5
    },
    time.value= {
      uptime: "62 days 3:12:45",
      bootTime: "2023-09-18 05:02:00",
      ntpSync: true,
      timezone: "UTC",
      lastChecked: "2023-11-20 10:31:22",
      clockDrift: "1.8s",  // 时钟漂移较大
      dateFormat: "YYYY/MM/DD",
      timeFormat: "24H"
    }
}
else if(route.query.id===3){
    cpu.value= {
      usage: "8%",  // 低负载
      user: "5%",
      system: "3%",
      idle: "92%",
      cores: 4,
      loadAvg: "0.2,0.3,0.4",
      temperature: "42°C",
      contextSwitches: 45000
    },
    memory.value= {
      total: "32GB",
      used: "4.2GB",
      free: "27.8GB",
      cached: "12GB",  // 大量缓存
      swapTotal: "4GB",
      swapUsed: "0GB",
      swapFree: "4GB",
      usagePercent: "13.1%"
    },
    disk.value= {
      total: "30TB",
      used: "15TB",
      free: "15TB",
      readSpeed: "25MB/s",
      writeSpeed: "18MB/s",
      ioWait: "0.5%",
      partitions: [
        { mount: "/", usage: "15%" },
        { mount: "/cache", usage: "22%" }
      ],
      inodesUsage: "8%"
    },
    network.value= {
      inTotal: "45MB/s",
      outTotal: "52MB/s",  // 出站流量略高
      tcpConnections: 215,
      udpConnections: 32,
      established: 125,
      timeWait: 42,
      errors: { rx: 0, tx: 0 },
      interfaces: ["eth0"]
    },
    processes.value= {
      total: 87,
      running: 5,
      sleeping: 82,
      zombie: 0,
      threads: 256,
      cpuTopProcess: {
        name: "redis-server",
        pid: 1234,
        cpu: "3%"
      },
      memTopProcess: {
        name: "redis-server",
        pid: 1234,
        mem: "2.8GB"
      },
      criticalServices: ["redis", "memcached"]
    },
    security.value= {
      lastLogin: {
        user: "cacheadmin",
        ip: "************",
        time: "2023-11-19 14:22:10"
      },
      failedAttempts: 0,
      sshSessions: 1,
      firewallStatus: "active",
      rootLogin: false,
      suspiciousProcesses: 0,
      lastPatched: "2023-11-10",  // 最近更新
      vulnerabilityCount: 0
    },
    time.value= {
      uptime: "7 days 12:45:33",
      bootTime: "2023-11-13 01:30:00",
      ntpSync: true,
      timezone: "UTC+0",
      lastChecked: "2023-11-20 10:32:15",
      clockDrift: "0.05s",
      dateFormat: "DD/MM/YYYY",
      timeFormat: "24H"
    }
}
else if(route.query.id===4){
    cpu.value= {
      usage: "N/A",
      user: "N/A",
      system: "N/A",
      idle: "N/A",
      cores: 12,
      loadAvg: "N/A,N/A,N/A",
      temperature: "N/A",
      contextSwitches: 0
    },
    memory.value= {
      total: "48GB",
      used: "N/A",
      free: "N/A",
      cached: "N/A",
      swapTotal: "8GB",
      swapUsed: "N/A",
      swapFree: "N/A",
      usagePercent: "N/A"
    },
    disk.value= {
      total: "24TB",  // 大容量存储
      used: "N/A",
      free: "N/A",
      readSpeed: "N/A",
      writeSpeed: "N/A",
      ioWait: "N/A",
      partitions: [
        { mount: "/", usage: "N/A" },
        { mount: "/backup", usage: "N/A" }
      ],
      inodesUsage: "N/A"
    },
    network.value= {
      inTotal: "N/A",
      outTotal: "N/A",
      tcpConnections: 0,
      udpConnections: 0,
      established: 0,
      timeWait: 0,
      errors: { rx: 0, tx: 0 },
      interfaces: ["igb0", "igb1"]
    },
    processes.value= {
      total: 0,
      running: 0,
      sleeping: 0,
      zombie: 0,
      threads: 0,
      cpuTopProcess: {
        name: "N/A",
        pid: 0,
        cpu: "N/A"
      },
      memTopProcess: {
        name: "N/A",
        pid: 0,
        mem: "N/A"
      },
      criticalServices: ["N/A"]
    },
    security.value= {
      lastLogin: {
        user: "backupadmin",
        ip: "************",
        time: "2023-11-18 23:15:42"  // 离线前最后登录
      },
      failedAttempts: 0,
      sshSessions: 0,
      firewallStatus: "unknown",
      rootLogin: false,
      suspiciousProcesses: 0,
      lastPatched: "2023-11-05",
      vulnerabilityCount: 1
    },
    time.value= {
      uptime: "N/A",
      bootTime: "2023-11-05 10:00:00",
      ntpSync: false,
      timezone: "UTC-5",
      lastChecked: "2023-11-20 03:45:18",  // 最后通信时间
      clockDrift: "N/A",
      dateFormat: "MM-DD-YYYY",
      timeFormat: "12H"
    }
}
else{
    cpu.value= {
      usage: "45%",
      user: "32%",
      system: "13%",
      idle: "55%",
      cores: 4,
      loadAvg: "1.2, 1.0,0.8",
      temperature: "58°C",
      contextSwitches: 185000
    },
    memory.value= {
      total: "32GB",
      used: "22GB",  // 内存使用较高
      free: "10GB",
      cached: "6GB",
      swapTotal: "8GB",
      swapUsed: "2GB",
      swapFree: "6GB",
      usagePercent: "68.8%"
    },
    disk.value= {
      total: "10TB",
      used: "5TB",  // 存储使用较多
      free: "5TB",
      readSpeed: "85MB/s",
      writeSpeed: "65MB/s",
      ioWait: "8%",
      partitions: [
        { mount: "C:", usage: "82%" },
        { mount: "D:", usage: "65%" }
      ],
      inodesUsage: "35%"
    },
    network.value= {
      inTotal: "25MB/s",
      outTotal: "18MB/s",
      tcpConnections: 185,
      udpConnections: 28,
      established: 95,
      timeWait: 45,
      errors: { rx: 3, tx: 2 },
      interfaces: ["Ethernet0", "Ethernet1"]
    },
    processes.value= {
      total: 215,
      running: 25,
      sleeping: 185,
      zombie: 0,
      threads: 1024,
      cpuTopProcess: {
        name: "java",  // 开发环境常见
        pid: 4567,
        cpu: "32%"
      },
      memTopProcess: {
        name: "sqlservr",  // SQL Server进程
        pid: 3456,
        mem: "8GB"
      },
      criticalServices: ["IIS", "SQLSERVER"]
    },
    security.value= {
      lastLogin: {
        user: "devuser",
        ip: "************",
        time: "2023-11-20 09:15:33"
      },
      failedAttempts: 8,  // 较多失败尝试
      sshSessions: 0,  // Windows使用RDP
      firewallStatus: "disabled",  // 开发环境常关闭
      rootLogin: true,
      suspiciousProcesses: 2,  // 可能存在风险
      lastPatched: "2023-10-25",  // 补丁较旧
      vulnerabilityCount: 7  // 漏洞较多
    },
    time.value= {
      uptime: "5 days 2:15:22",
      bootTime: "2023-11-15 08:00:00",
      ntpSync: false,  // 开发环境不同步
      timezone: "UTC+8",
      lastChecked: "2023-11-20 10:33:45",
      clockDrift: "12.5s",  // 时间偏差大
      dateFormat: "YYYY-MM-DD",
      timeFormat: "24H"
    }
}
});


</script>

<style lang="less" scoped>
.divider {
  height: 2px;
  background-color: #eee;
  margin-bottom: 25px;
  margin-top: 25px;
  span {
    display: block;
    border: 1px #eee solid;
    padding: 10px;
    background-color: #fff;
    border-radius: 10px;
    font-weight: 600;
    color: #1b9aee;
  }
}
</style>