import CryptoJS from 'crypto-js';

class MqttConfig {
    constructor(connectMessage) {
        // 从connectMessage中获取必要的值
        // this.productId = connectMessage.productId;
        this.productKey = connectMessage.productKey;
        this.deviceName = connectMessage.deviceName;
        // this.uniqueNo = connectMessage.uniqueNo;
        this.deviceSecret = connectMessage.deviceSecret;
        this.tenantId = connectMessage.tenantId ? connectMessage.tenantId : "00000"; // 默认租户ID
        const timestamp = new Date().getTime();
        this.timestamp = timestamp;
        this.clientId = connectMessage.clientId ? connectMessage.deviceName : connectMessage.clientId + "-" + connectMessage.timestamp
    }

    // 获取MQTT客户端ID
    mqttClientId() {
        // clienttype=2 表示pc和web连接，timestamp为时间戳必须
        return `${this.clientId}@${this.tenantId}|clienttype=2,timestamp=${this.timestamp}|`;
    }

    // 获取用户名
    username() {
        return `${this.deviceName}&${this.productKey}`;
    }

    // 获取密码
    password() {
        const value = `clientId${this.clientId}deviceName${this.deviceName}productKey${this.productKey}timestamp${this.timestamp}`;
        return this.hmacMd5Hex(value, this.deviceSecret);
    }

    // HMAC-MD5加密
    hmacMd5Hex(value, key) {
        const hash = CryptoJS.HmacMD5(value, key);
        return hash.toString(CryptoJS.enc.Hex);
    }

    // 获取完整的MQTT连接选项
    getMqttOptions() {
        return {
            clientId: this.mqttClientId(),
            username: this.username(),
            password: this.password(),
            clean: true,
            keepalive: 60,
            reconnectPeriod: 5000
        };
    }
}

export default MqttConfig;