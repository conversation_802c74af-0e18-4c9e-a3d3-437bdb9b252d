<!--
  * 照明计划详情
  *
  * @Author:    骆伟林
  * @Date:      2025-04-07 21:36:15
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
  <a-card size="big" :bordered="false" :hoverable="true">
    <template #title>{{ route.query.isEdit==='false' ? '新增智能控制' : '编辑智能控制' }} </template>
    <template #extra>
      <a-space>
        <a-button @click="submit"  type="primary">提交</a-button>
        <a-button @click="onClose" type="primary">返回</a-button>
      </a-space>
    </template>
    <a-form ref="formRef" :model="basicInfo" :label-col="{ span: 7 }" :wrapper-col="{ span: 11 }"> 
      <a-divider class="divider"><span>基本信息</span></a-divider>
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="标题" name="label">
            <a-input style="width: 100%" v-model:value="basicInfo.label"placeholder="标题"/>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="是否禁用" name="disabled">
            <a-switch :checked="basicInfo.disabled === 1"checked-children="否"un-checked-children="是"@change="switchChange"/>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="简介" name="info">
            <a-textarea style="width: 100%" v-model:value="basicInfo.info"placeholder="简介"/>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="备注" name="remark">
            <a-textarea style="width: 100%" v-model:value="basicInfo.remark"placeholder="备注"/>
          </a-form-item>
        </a-col>
      </a-row>
      <a-divider class="divider"><span  style="margin-bottom: 20px;">场景配置</span></a-divider>
      <listConfigure @dataSource="getDataSource" :strategyList="sceneList"></listConfigure>
    </a-form>
  </a-card>
</template>

<script setup>
import { reactive, ref, nextTick, onMounted, computed, watch } from 'vue';
import { useRoute } from 'vue-router';
import { router } from '/@/router';
import { lightPlanApi } from '/@/api/business/light/plan/light-plan-api';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { smartSentry } from '/@/lib/smart-sentry';
import { message } from 'ant-design-vue';
import listConfigure from './light-plan-configure.vue';
import { useUserStore } from '/@/store/modules/system/user';
const userStore = useUserStore();
let dataSource=ref([])
function getDataSource(data){
  console.log(data);
  
  dataSource.value=data
  console.log(dataSource.value);
  
}
function switchChange(checked) {
  basicInfo.value.disabled = checked===true? 1 : 0;
}

const route = useRoute();
const basicInfo=ref({
    label: undefined,
    info: undefined,
    remark: undefined,
    sort: undefined,
    createTime:undefined,
    disabled:1
  }
)
const sceneList=ref([])

  let form = reactive({
    basicInfo: {},
    sceneList:[]
  });
   function onClose() {
   userStore.closePage(route, router, '/light/plan');
   goBack()
}
function goBack() {
  if (window.history.length > 1) {
    router.push('/light/plan');
  } else {
    router.back();
  }
}
function submit(){
  form.basicInfo = basicInfo.value; 
  form.sceneList=[];
  dataSource.value.map((item,index)=>{
    form.sceneList.push({
      id:basicInfo.value.id,
      createUserId:item.strategies[0].createUserId,
      updateUserId:item.strategies[0].updateUserId,
      createTime:null,
      updateTime:null,
      tenantId:item.strategies[0].tenantId,
      planId:basicInfo.value.id,
      planLabel:basicInfo.value.label, 
      sceneId:Number(item.strategies[0].id),
      sceneLabel:item.strategies[0].label,
      startDatetime:item.createTime,
      endDatetime:item.endTime,
      recordStatus:item.strategies[0].recordStatus,
      deleteFlag:item.strategies[0].deleteFlag
    })
  })
  save()
}

async function  save() {
  try {
    if (basicInfo.value.id) {
    await lightPlanApi.set(form);
      } else {
        await  lightPlanApi.add(basicInfo.value);
      await lightPlanApi.set(form);
      }
      message.success('操作成功');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  
}
onMounted(async() => {
  if (route.query.isEdit==='false') {
    return
  }
  else{
  const res=await lightPlanApi.detail(route.query.id)
  console.log(res);
  form=res.data
  basicInfo.value=res.data.basicInfo
  sceneList.value=res.data.sceneList
  }
});
</script>

<style lang="less" scoped>
.divider {
  height: 2px;
  background-color: #eee;
  margin-bottom: 25px;
  margin-top: 25px;
  span {
    display: block;
    border: 1px #eee solid;
    padding: 10px;
    background-color: #fff;
    border-radius: 10px;
    font-weight: 600;
    color: #1b9aee;
  }
}
</style>