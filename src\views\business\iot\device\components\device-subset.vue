<template>
  <div>
    <div class="header-extra">
      <a-space>
        <span>未选设备: {{ leftData.length }}</span>
        <span>已选设备: {{ rightData.length }}</span>
      </a-space>
    </div>
    
    <a-transfer
      v-model:target-keys="targetKeys"
      :data-source="formattedAllDevices"
      :titles="['--已有子设备', '--未有子设备']"
      :row-key="record => record.key"
      @change="handleTransferChange"
      :operations="['子设备解绑', '子设备添加']"
      :list-style="{
        width: '38%',
        height: transferHeight
      }"
      :selected-keys="selectedKeys"
      @selectChange="handleSelectChange"
    >
      <template #children="{ direction, selectedKeys: currentSelectedKeys, onItemSelect, onItemSelectAll }">
        <a-table
          :columns="tableColumns"
          :data-source="direction === 'left' ? leftData : rightData"
          :row-selection="{
            selectedRowKeys: currentSelectedKeys,
            onChange: (selectedRowKeys, selectedRows) => {
              handleSelectionChange(selectedRowKeys, selectedRows, onItemSelect, onItemSelectAll);
            },
            onSelect: (record, selected) => {
              onItemSelect(record.key, selected);
              updateSelectedKeys();
            },
            type: 'checkbox',
            getCheckboxProps: record => ({ disabled: false })
          }"
          :pagination="false"
          size="small"
          bordered
          style="width: 100%"
          :scroll="{ y: tableScrollY }"
          @row-click="(record) => {
            const isSelected = currentSelectedKeys.includes(record.key);
            onItemSelect(record.key, !isSelected);
            updateSelectedKeys();
          }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'deviceStatus'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
          </template>
        </a-table>
      </template>
    </a-transfer>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { message } from 'ant-design-vue';
import { iotDeviceApi } from '/@/api/business/iot/device/iot-device-api.js';

const targetKeys = ref([]);
const selectedKeys = ref([]);
const allDevices = ref([]);
const selectedDevices = ref([]);
const props = defineProps({
  deviceId: {
    type: String,
    required: true,
  },
});

const deviceId = computed(() => props.deviceId);

// 全屏相关状态
const isFullscreen = ref(true);
const transferHeight = computed(() => isFullscreen.value ? 'calc(80vh - 200px)' : '500px');
const tableScrollY = computed(() => isFullscreen.value ? 'calc(100vh - 250px)' : '400px');

const params = {
  pageNum: 1,
  pageSize: 100
};

// 设备状态映射
const statusMap = {
  0: { text: '离线', color: 'red' },
  1: { text: '在线', color: 'green' },
  2: { text: '未激活', color: 'orange' }
};

const getStatusColor = (status) => statusMap[status]?.color || 'default';
const getStatusText = (status) => statusMap[status]?.text || '未知';

// 安全格式化设备数据
const safeFormatDevice = (item) => {
  if (!item) return null;
  
  return {
    key: item?.id?.toString() || Math.random().toString(36).substr(2, 9),
    title: item.deviceName || '未知设备',
    deviceName: item.deviceName || '未知设备',
    productName: item.productName || '',
    categoryName: item.categoryName || '',
    status: item.deviceStatus ?? 0,
    description: item.description || '',
    deviceSecret: item.deviceSecret || '',
    deviceType: item.deviceType || '',
    lastOnlineTime: item.lastOnlineTime || ''
  };
};

// 格式化所有设备数据
const formattedAllDevices = computed(() => {
  return allDevices.value
    .map(item => safeFormatDevice(item))
    .filter(item => item && item.key);
});

// 格式化已选设备数据
const formattedSelectedDevices = computed(() => {
  return selectedDevices.value
    .map(item => safeFormatDevice(item))
    .filter(item => item && item.key);
});

const tableColumns = [
  {
    title: '设备名称',
    dataIndex: 'deviceName',
    key: 'deviceName',
    width: 200,
    ellipsis: true
  },
  {
    title: '设备密钥',
    dataIndex: 'deviceSecret',
    key: 'deviceSecret',
    width: 150,
    ellipsis: true
  },
  {
    title: '设备所属产品',
    dataIndex: 'productName',
    key: 'productName',
    width: 150,
    ellipsis: true
  },
  {
    title: '设备类型',
    dataIndex: 'deviceType',
    key: 'deviceType',
    width: 100
  },
  {
    title: '设备状态',
    dataIndex: 'status',
    key: 'deviceStatus',
    ellipsis: true
  },
  {
    title: '最后上线时间',
    dataIndex: 'lastOnlineTime',
    key: 'lastOnlineTime',
    ellipsis: true
  }
];

// 计算选择数量
const leftData = computed(() => {
  return formattedAllDevices.value.filter(item => 
    item && item.key && !targetKeys.value.includes(item.key)
  );
});

const rightData = computed(() => {
  return formattedSelectedDevices.value.filter(item => 
    item && item.key && targetKeys.value.includes(item.key)
  );
});

// 更新选中keys
const updateSelectedKeys = () => {
  console.log('当前选中的keys:', selectedKeys.value);
};

// 处理选择变化
const handleSelectionChange = (selectedRowKeys, selectedRows, onItemSelect, onItemSelectAll) => {
  if (selectedRowKeys.length === 0) {
    // 取消全选
    onItemSelectAll([], false);
  } else if (selectedRowKeys.length === (leftData.value.length || rightData.value.length)) {
    // 全选
    onItemSelectAll(selectedRowKeys, true);
  } else {
    // 部分选择
    selectedRows.forEach(row => {
      onItemSelect(row.key, true);
    });
  }
  updateSelectedKeys();
};

// 处理选择变化
const handleSelectChange = (sourceSelectedKeys, targetSelectedKeys) => {
  selectedKeys.value = [...sourceSelectedKeys, ...targetSelectedKeys];
};

// 处理转移变化
const handleTransferChange = (nextTargetKeys, direction, moveKeys) => {
  console.log('目标keys变化:', nextTargetKeys);
  console.log('移动方向:', direction);
  console.log('移动的keys:', moveKeys);

  targetKeys.value = nextTargetKeys;
  const parentId = deviceId.value;
  let iotSubDeviceIds = [];

  if (direction === 'right') {
    // 向右移动，从左侧移除，添加到右侧
    iotSubDeviceIds = allDevices.value
      .filter(device => !moveKeys.includes(device.id))
      .map(device => device.id);
  } else if (direction === 'left') {
    // 向左移动，从右侧移除，添加到左侧
    iotSubDeviceIds = [...moveKeys, ...allDevices.value.map(device => device.id)];
  }

  updateSubDevice(parentId, iotSubDeviceIds);
};

// 获取所有设备
const fetchAllDevices = async () => {
  try {
    const res = await iotDeviceApi.getSubDevice(deviceId.value);
    allDevices.value = Array.isArray(res?.data) ? res.data : [];
  } catch (error) {
    console.error('获取设备失败:', error);
    allDevices.value = [];
  }
};

// 获取未分配设备的子设备
const fetchSelectedDevices = async () => {
  try {
    const res = await iotDeviceApi.getNoGatewayDevice();
    selectedDevices.value = Array.isArray(res?.data) ? res.data : [];
    // 初始化targetKeys为已选设备的key
    targetKeys.value = selectedDevices.value
      .map(item => item?.id?.toString())
      .filter(key => key && typeof key === 'string');
  } catch (error) {
    console.error('获取未分配设备失败:', error);
    selectedDevices.value = [];
    targetKeys.value = [];
  }
};

// 更新设备子集
const updateSubDevice = async (parentId, iotSubDeviceIds) => {
  try {
    const params = {
      parentId: parentId,
      iotSubDeviceIds: iotSubDeviceIds
    };
    const res = await iotDeviceApi.setSubDevice(params);
    message.success('更新设备子集成功');
    // 刷新数据
    await fetchAllDevices();
    await fetchSelectedDevices();
  } catch (error) {
    console.error('更新设备子集失败:', error);
    message.error('更新设备子集失败');
  }
};

// 监听deviceId变化
watch(deviceId, async (newValue) => {
  if (newValue) {
    await fetchAllDevices();
    await fetchSelectedDevices();
  }
}, { immediate: true });

// 初始化加载数据
onMounted(async () => {
  if (deviceId.value) {
    await fetchAllDevices();
    await fetchSelectedDevices();
  }
});
</script>

<style scoped>
.header-extra {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-end;
}

.ant-transfer {
  display: flex;
  justify-content: space-between;
}

.ant-table-row {
  cursor: pointer;
}

.ant-table-row:hover {
  background-color: #f5f5f5;
}

:deep(.ant-transfer-list) {
  flex: 1;
  margin: 0 8px;
}
</style>