<template>
    <a-form class="smart-query-form" style="margin-bottom: 0;">
        <a-row class="smart-query-form-row">
            <a-form-item label="模块" class="smart-query-form-item">
                <a-select v-model:value="selectedBlock" style="width: 200px" @change="handleBlockChange">
                    <a-select-option v-for="block in blocks" :key="block.id" :value="block.id">
                        {{ block.blockName }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item>
                <a-button type="primary" @click="onModel" style="margin-top: 5px;" class="smart-margin-left10">
                    物模型TSL
                </a-button>
            </a-form-item>
            <a-form-item>
                <a-button type="primary" @click="exportTsl" style="margin-top: 5px;" class="smart-margin-left10">
                    <download-outlined />
                    导出物模型
                </a-button>
            </a-form-item>
        </a-row>
    </a-form>
    <a-card>
        <a-table :columns="columns" :data-source="tableData" :pagination="false" :rowKey="record => record.id" bordered>
            <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'operation'">
                    <a-button type="link" @click="handleView(record)">
                        <eye-outlined />
                        查看
                    </a-button>
                </template>
                <template v-else-if="column.key === 'functionType'">
                    <a-tag :color="iotProductConst.getFunctionTypeColor(record.functionType)">
                        {{ iotProductConst.getFunctionTypeName(record.functionType) }}
                    </a-tag>
                </template>
                <template v-else-if="column.key === 'dataType'">
                    <template v-if="record.functionType === 1">
                        {{ iotProductConst.getFieldTypeName(record) }}
                    </template>
                    <template v-else>
                        -
                    </template>
                </template>
                <template v-else-if="column.key === 'dataSpec'">
                    {{ iotProductConst.getDataTypeName(record) }}
                </template>
            </template>
        </a-table>
    </a-card>
    <!-- 物模型Tsl模态框 -->
    <modelTslForm ref="modelTslFormRef" :modelValue="modelValue" />
    <!-- 功能详情抽屉 -->
    <functionDetailDrawer ref="detailDrawerRef" />
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { EyeOutlined, DownloadOutlined } from '@ant-design/icons-vue';
import { iotProductApi } from '/@/api/business/iot/product/iot-product-api.js';
import iotProductConst from '/@/constants/business/iot/product/iot-product-const.js';
import { exportJson } from '/@/utils/export-json-util.js';
import modelTslForm from '/@/views/business/iot/device/components/modelTsl-form.vue';
import functionDetailDrawer from './function-detail-drawer.vue';
import { Modal } from 'ant-design-vue';

const props = defineProps({
  productInfo: {
    type: Object,
  }
});


// 表格列定义
const columns = [
  { title: '#', dataIndex: 'index', key: 'index', width: 80 },
  { title: '功能类型', dataIndex: 'functionTypeName', key: 'functionType', width: 120 },
  { title: '功能名称', dataIndex: 'name', key: 'name', width: 150 },
  { title: '标识符', dataIndex: 'identifier', key: 'identifier', width: 150 },
  { title: '数据类型', dataIndex: 'dataType', key: 'dataType', width: 150 },
  { title: '数据定义', dataIndex: 'dataSpec', key: 'dataSpec' },
  { title: '操作', key: 'operation', width: 120 },
];

// 根据选中的blockId筛选数据并合并所有功能
const tableData = computed(() => {
  if (!selectedBlock.value) return [];
  
  const properties = productData.value.properties || [];
  const commands = productData.value.commands || [];
  const events = productData.value.events || [];
  
  // 筛选并合并数据
  const filteredProperties = properties.filter(item => item.blockId === selectedBlock.value);
  const filteredCommands = commands.filter(item => item.blockId === selectedBlock.value);
  const filteredEvents = events.filter(item => item.blockId === selectedBlock.value);
  
  // 合并所有数据
  const mergedData = [
    ...filteredProperties,
    ...filteredCommands,
    ...filteredEvents
  ];
  
  // 添加索引
  return mergedData.map((item, index) => ({
    ...item,
    index: index + 1
  }));
});

//物模型数据
const modelValue = ref({});
const productData = ref({});

const blocks = ref([]);
const selectedBlock = ref('');
// 获取产品数据
const getProductData = async () => {
  try {
    console.log(props.productInfo);
    const response = await iotProductApi.queryProductDataByKey(props.productInfo.productKey);
    
    if (response && response.data) {
      productData.value = response.data;
      modelValue.value = response.data;
      blocks.value = response.data.blocks || [];
      
      // 默认选中第一个模块
      if (blocks.value.length > 0) {
        selectedBlock.value = blocks.value[0].id;
      }
    }
  } catch (error) {
    console.error('获取产品数据失败', error);
  }
};

// 模块切换处理
const handleBlockChange = (value) => {
  selectedBlock.value = value;
};

// 详情抽屉ref
const detailDrawerRef = ref(null);

// 查看功能详情
const handleView = (record) => {
  detailDrawerRef.value.show(record);
};

const modelTslFormRef = ref(null);
// 物模型
function onModel() {
    modelTslFormRef.value.showForm();
}

// 导出物模型
const exportTsl = () => {
  Modal.confirm({
    title: '提示',
    content: '是否导出物模型？',
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      if (!modelValue.value || !props.productInfo) return;
      
      const fileName = `product_tsl_${props.productInfo.productName || 'product'}_${props.productInfo.productKey}_v${modelValue.value.profile?.versionNo || '1'}.json`;
      
      exportJson(modelValue.value, fileName);
    }
  });
};

// 监听属性变化
watch(() => props.productInfo, (newVal) => {
  if (newVal && newVal.productKey) {
    getProductData();
  }
}, { immediate: true });

onMounted(() => {
  getProductData();
});
</script>