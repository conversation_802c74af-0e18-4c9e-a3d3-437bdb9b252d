<!--
  * 产品信息表
  *
  * @Author:    丘瑚珊
  * @Date:      2025-03-21 21:15:14
  * @Copyright  中山睿数信息技术有限公司 2025
-->
  <template>

    <a-modal :title="form.id ? '编辑' : '添加'" :width="700" :open="visibleFlag" @cancel="onClose" :maskClosable="false"
      :destroyOnClose="true">

      <a-collapse v-model:activeKey="activeKeys">
        <a-collapse-panel key="1" header="产品信息">
          <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }">
            <!-- 产品基本信息 -->
            <a-form-item label="产品名称" name="productName">
              <a-input style="width: 100%" v-model:value="form.productName" placeholder="产品名称" />
            </a-form-item>
            <a-form-item class="custom-label-form-item" name="productKey">
              <template #label>
                <div class="label-container">
                  <span class="label-text">产品编码</span>
                  <a-tooltip title="留空则系统自动生成12位编码" placement="topLeft" class="label-tooltip">
                    <question-circle-outlined />
                  </a-tooltip>
                </div>
              </template>
              <a-input-group compact>
                <a-input placeholder="请输入产品编码" style="width: calc(100% - 100px);" v-model:value="form.productKey" />
                <a-button type="default" style="width: 100px;" @click="generateProductKey" :loading="generatingKey">
                  自动生成
                </a-button>
              </a-input-group>
            </a-form-item>
            <a-form-item class="custom-label-form-item" name="productKey">
              <template #label>
                <div class="label-container">
                  <span class="label-text">产品密钥</span>
                  <a-tooltip title="留空则系统自动生成16位编码" placement="topLeft" class="label-tooltip">
                    <question-circle-outlined />
                  </a-tooltip>
                </div>
              </template>
              <a-input-group compact>
                <a-input placeholder="请输入产品密钥" style="width: calc(100% - 100px);" v-model:value="form.productSecret" />
                <a-button type="default" style="width: 100px;" @click="generateProductSecret"
                  :loading="generatingSecret">
                  自动生成
                </a-button>
              </a-input-group>
            </a-form-item>
            <a-form-item label="产品品类" name="categoryId">
              <a-input-number style="width: 100%" v-model:value="form.categoryNameFull" placeholder="品类id"
                :disabled="!!form.id" :class="{ 'disabled-input': !!form.id }" @click="showModal" />
            </a-form-item>
            <a-form-item label="产品图标" name="icon">
              <!-- <upload/> -->
              <FileUpload :defaultFileList="form.logo ? [{ fileUrl: form.logo }] : []" @change="fileChange"
                :maxUploadSize="1" />
            </a-form-item>
          </a-form>
        </a-collapse-panel>

        <a-collapse-panel key="2" header="数据信息">
          <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }">
            <a-form-item label="设备类型" name="deviceType">
              <a-radio-group v-model:value="form.deviceType" :disabled="!!form.id">
                <a-radio :value="'direct_co'">直连设备</a-radio>
                <a-radio :value="'gateway'">网关设备</a-radio>
                <a-radio :value="'child'">网关子设备</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item label="连接协议" name="linkProtocol">
              <DictSelect keyCode="7" placeholder="请选择连接协议" v-model:value="form.linkProtocol" width="100%"
                :disabled="!!form.id" />
            </a-form-item>
            <a-form-item label="联网方式" name="connectMode">
              <DictSelect keyCode="5" placeholder="请选择联网方式" v-model:value="form.connectMode" width="100%"
                :disabled="!!form.id" />
            </a-form-item>
            <a-form-item label="数据格式" name="dataType">
              <DictSelect keyCode="6" placeholder="请选择数据格式" v-model:value="form.dataType" width="100%"
                :disabled="!!form.id" />
            </a-form-item>
            <a-form-item label="数据状态" name="status">
              <a-radio-group v-model:value="form.status" :disabled="!!form.id">
                <a-radio :value="0">开发中</a-radio>
                <a-radio :value="1">已上线</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item label="动态注册" name="dynamicRegister">
              <a-radio-group v-model:value="form.dynamicRegister" :disabled="!!form.id">
                <a-radio :value="0">否</a-radio>
                <a-radio :value="1">是</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-form>
        </a-collapse-panel>

        <a-collapse-panel key="3" header="基础信息">
          <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }">
            <a-form-item label="生产厂商" name="manufacturer">
              <a-input style="width: 100%" v-model:value="form.manufacturer" placeholder="请输入生产厂商" />
            </a-form-item>
            <a-form-item label="产品型号" name="model">
              <a-input style="width: 100%" v-model:value="form.model" placeholder="请输入产品型号" />
            </a-form-item>
            <a-form-item label="产品描述" name="description">
              <a-textarea style="width: 100%" v-model:value="form.description" placeholder="请输入产品描述" />
            </a-form-item>
          </a-form>
        </a-collapse-panel>
      </a-collapse>

      <template #footer>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">确定</a-button>
      </template>
    </a-modal>


    <!-- 品类模态框 -->
    <a-modal title="自定义模态框" :open="showCategoryModal" @ok="handleOk" @cancel="handleCancel" ok-text="确定"
      cancel-text="取消" :width="700">
      <!-- 主体内容 -->
      <!-- 搜索框 -->
      <a-form class="smart-query-form">
        <a-row class="smart-query-form-row">
          <a-form-item label="品类名称" class="smart-query-form-item">
            <a-input style="width: 130px" v-model:value="queryForm.categoryName" placeholder="请输入品类名称" />
          </a-form-item>
          <a-form-item label="品类行业" class="smart-query-form-item">
            <DictSelect keyCode="1" placeholder="请选择品类行业" v-model:value="queryForm.categoryIndustry" width="130px" />
          </a-form-item>
          <a-form-item label="品类场景" class="smart-query-form-item">
            <DictSelect keyCode="2" placeholder="请选择品类场景" v-model:value="queryForm.categoryScene" width="130px" />
          </a-form-item>
          <a-form-item class="smart-query-form-item">
            <a-button type="primary" @click="onSearch">
              <template #icon>
                <SearchOutlined />
              </template>
              查询
            </a-button>
            <a-button @click="resetQuery" class="smart-margin-left10">
              <template #icon>
                <ReloadOutlined />
              </template>
              重置
            </a-button>
          </a-form-item>
        </a-row>
      </a-form>

      <!-- 表格 -->
      <!-- 数据表格 -->
      <a-table :columns="columns" :dataSource="data" :pagination="false" :customRow="handleCustomRow"
        :rowClassName="setRowClassName" bordered>
        <template #emptyText>
          <a-empty description="暂无数据" />
        </template>
      </a-table>

      <!-- 分页信息 -->
      <div class="smart-query-table-page">
        <a-pagination showSizeChanger showQuickJumper show-less-items :pageSizeOptions="PAGE_SIZE_OPTIONS"
          :defaultPageSize="queryForm.size" v-model:current="queryForm.current" v-model:pageSize="queryForm.size"
          :total="total" @change="queryData" @showSizeChange="queryData" :show-total="(total) => `共${total}条`" />
      </div>
    </a-modal>

  </template>



<script setup>
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { reactive, ref, nextTick ,defineComponent , onMounted} from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { iotProductApi } from '/@/api/business/iot/product/iot-product-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  import DictSelect from '/@/components/support/dict-select/index.vue';// 字典下拉框
  import { QuestionCircleOutlined } from '@ant-design/icons-vue';
  import { iotCategoryApi } from '/@/api/business/iot/category/iot-category-api.js';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import FileUpload from '/@/components/support/file-upload/index.vue';

  // 折叠面板控制
  const activeKeys = ref(['1','2','3']); // 默认展开所有面板

  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show(rowData) {
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
      console.log("rowData",form)
    }
    // 使用字典时把下面这注释修改成自己的字典字段 有多个字典字段就复制多份同理修改 不然打开表单时不显示字典初始值
    // if (form.status && form.status.length > 0) {
    //   form.status = form.status.map((e) => e.valueCode);
    // }
    visibleFlag.value = true;
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
      productKey: undefined, //产品编码
      productName: undefined, //产品名称
      productDesc: undefined, //产品描述信息
      productSecret: undefined, //产品密钥
      categoryId: undefined, //品类id
      deviceType: undefined, //设备类型，direct_connect：直连设备，gateway：网关设备，gateway_child：网关子设备
      connectMode: undefined, //联网方式，如果是网关子设备，则此参数为空，包含：wifi,cellular_network,ethernet,lora
      dataType: undefined, //数据格式，alink_json：alink协议json或者custom：透传自定义
      logo: undefined, //产品图片
      vendors: undefined, //产品生产厂商
      model: undefined, //产品型号
      createUserId: undefined, //创建人
      dynamicRegister: undefined, //是否支持动态注册
      status: undefined, //数据状态，0：开发中，1：已上线
      linkProtocol: undefined, //连接协议，如果是网关子设备，则此参数为空，包含：mqtt,coap,lwm2m,ble,zwave,modbus,canbus,opcua,other
  };

  let form = reactive({ ...formDefault });

  const rules = {
      productName: [{ required: true, message: '产品名称 必填' }],
      categoryId: [{ required: true, message: '品类id 必填' }],
      deviceType: [{ required: true, message: '设备类型，direct_connect：直连设备，gateway：网关设备，gateway_child：网关子设备 必填' }],
      connectMode: [{ required: true, message: '联网方式，如果是网关子设备，则此参数为空，包含：wifi,cellular_network,ethernet,lora 必填' }],
      dataType: [{ required: true, message: '数据格式，alink_json：alink协议json或者custom：透传自定义 必填' }],
      status: [{ required: true, message: '数据状态 必填' }],
      dynamicRegister: [{ required: true, message: '动态注册 必填' }],
      xieyi: [{ required: true, message: '协议 必填' }],

  };
  



  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      if (form.id) {
        console.log("编辑的form",form)
        await iotProductApi.update(form);
      } else {
        console.log("添加的form",form)
        await iotProductApi.add(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }
//处理图片
function fileChange(file) {
  form.logo = file[0]?.fileUrl || "";
}


  // 自动生成
const generatingKey = ref(false);
const generatingSecret = ref(false);

// 生成随机字符串的辅助函数
const generateRandomString = (length) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// 生成产品编码（本地）
const generateProductKey = () => {
  try {
    generatingKey.value = true;
    // 生成16位产品编码，格式类似：PK-XXXX-XXXX-XXXX
    const part1 = generateRandomString(4);
    const part2 = generateRandomString(4);
    const part3 = generateRandomString(4);
    form.productKey = `PK-${part1}-${part2}-${part3}`;
    message.success('产品编码生成成功');
  } catch (error) {
    message.error('生成产品编码失败');
  } finally {
    generatingKey.value = false;
  }
};

// 生成产品密钥（本地）
const generateProductSecret = () => {
  try {
    generatingSecret.value = true;
    // 生成32位随机字符串作为密钥
    form.productSecret = generateRandomString(32);
    message.success('产品密钥生成成功');
  } catch (error) {
    message.error('生成产品密钥失败');
  } finally {
    generatingSecret.value = false;
  }
};





  //返回键值
  function findDescByValue(value, enumObject) {
    // 遍历枚举对象的键值对
    for (const key in enumObject) {
        if (enumObject[key].value === value) {
        return enumObject[key].desc; // 找到对应的描述后返回
        }
    }
    return null; // 如果找不到对应的值，则返回 null
  }


  // 品类模态框
  // 定义模态框的显示状态
const showCategoryModal = ref(false);

// 点击按钮，显示模态框
const showModal = () => {
  showCategoryModal.value = true;
};

// 点击“确定”按钮的处理逻辑
const handleOk = () => {
  showCategoryModal.value = false; // 关闭模态框
  console.log('点击了“确定”');
};

// 点击“取消”或模态框关闭按钮的处理逻辑
const handleCancel = () => {
  showCategoryModal.value = false; // 关闭模态框
  console.log('点击了“取消”');
};

// 表格列配置
const columns = [
  {
    title: '品类行业',
    dataIndex: 'categoryIndustryName',
    key: 'categoryIndustryName',
    width: 150,
  },
  {
    title: '品类场景',
    dataIndex: 'categorySceneName',
    key: 'categorySceneName',
    width: 150,
  },
  {
    title: '品类名称',
    dataIndex: 'categoryName',
    key: 'categoryName',
    width: 200,
  },
  {
    title: '排序号',
    dataIndex: 'sort',
    key: 'sort',
    width: 100,
  },
];



const queryFormState = {
    categoryName: undefined,
    categoryIndustry: undefined,
    categoryScene: undefined,
    current: 1,
    size: 9,
    sortItemList: [{ isAsc: false, column: 'create_time' }],
  };
  const loading = ref(false);
  // 查询表单form
  const queryForm = reactive({ ...queryFormState });
  // 表格数据
  const data = ref([]);
  // 总数
  const total = ref(0);
  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.size;
    Object.assign(queryForm, queryFormState);
    queryForm.size = pageSize;
    queryData();
  }

  // 搜索
  function onSearch() {
    console.log(queryForm, '查询');
    queryForm.current = 1;
    queryData();
  }

  // 查询数据
  async function queryData() {
    try {
      let queryResult = await iotCategoryApi.page(queryForm);
      console.log(queryResult, '查询结果222');
      total.value = queryResult.data.total;
      data.value = queryResult.data.records;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      loading.value = false;
    }
  }
  onMounted(queryData);


  // 当前选中行的ID
const selectedRowId = ref(null);

  const handleCustomRow = (record) => {
  return {
    onClick: (event) => {
      console.log("行点击事件", record.categoryNameFull);
      form.categoryNameFull = record.categoryNameFull;
      form.categoryId = record.id;
      selectedRowId.value = record.id;
    }
  };
};


// 设置行类名
const setRowClassName = (record) => {
  return record.id === selectedRowId.value ? 'selected-row' : '';
};
  
  defineExpose({
    show,
  });
</script>

<style scoped>
.product-manage {
  max-width: 1200px;
  margin: 0 auto;
}
.upload-tip {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  margin-top: 8px;
}
.mb-4 {
  margin-bottom: 16px;
}

.disabled-input {
  background-color: #f5f5f5;
  color: rgba(0, 0, 0, 0.65);
  cursor: not-allowed;
}


/* 自定义标签容器 */
.label-container {
  position: relative;
  display: inline-block;
  padding-left: 18px; /* 为图标留出空间 */
}

/* 标签文字样式 */
.label-text {
  vertical-align: middle;
}

/* 提示图标定位 */
.label-tooltip {
  position: absolute;
  left: 0;
  top: 0;
  transform: translateY(-30%); /* 向上微调位置 */
}

/* 保持与其他表单项对齐 */
.custom-label-form-item :deep(.ant-form-item-label) {
  padding-bottom: 4px;
  line-height: 1.5;
}

/* 图标样式调整 */
.label-tooltip .anticon {
  font-size: 14px;
  color: #1890ff;
  cursor: help;
}

/* 输入框组宽度适配 */
.custom-label-form-item :deep(.ant-input-group) {
  width: 100%;
}

:deep(.ant-table .selected-row) {
  background-color: #e6f7ff !important; /* 浅蓝色背景 */
}


</style>
