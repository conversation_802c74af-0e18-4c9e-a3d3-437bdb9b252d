/**
 * 品类表 api 封装
 *
 * @Author:    戴松挺
 * @Date:      2025-03-23 17:17:51
 * @Copyright  中山睿数信息技术有限公司 2025
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const iotCategoryApi = {
  /**
   * 分页查询  <AUTHOR>
   */
  queryPage: (param) => {
    return postRequest('/iotCategory/list', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
    return postRequest('/iotCategory/submit', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
    return postRequest('/iotCategory/submit', param);
  },

  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return postRequest(`/iotCategory/remove`, id );
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
    return postRequest('/iotCategory/remove', idList);
  },

  /**
   * 获取详情 <AUTHOR>
   */
  getDetail: (param) => {
    return postRequest('/iotCategory/detail', param);
  },

  /**
   * 获取详情 <AUTHOR>
   */
  page: (param) => {
    return postRequest('/iotCategory/page', param);
  },


};
