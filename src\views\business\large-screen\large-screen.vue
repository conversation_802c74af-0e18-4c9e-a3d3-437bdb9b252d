<template>
  <div className="map-container">
    <div id="container" ref="mapContainer">
      <dv-full-screen-container>
        <!-- 从左往下再到从右往下排列 -->
        <BorderBoxOne :data="boxOneData"/>
        <BorderBoxTwo :list="boxTwoData"/>
        <BorderBoxThree :list="boxThreeData"/>
        <BorderBoxFour :data="boxFourData"/>
        <BorderBoxFive :list="boxFiveData"/>
        <BorderBoxSix :list="boxSixData"/>
        <!--        <BorderBoxCenter :list="boxCenterData"/>-->
        <HeadCenter @nextRegion="getNextRegion" :current="currentRegion"/>
      </dv-full-screen-container>
    </div>
  </div>
</template>
<script setup>
import {ref, onMounted,nextTick} from 'vue';
import AMapLoader from '@amap/amap-jsapi-loader';
import BorderBoxOne from './components/border-box-one.vue';
import BorderBoxTwo from './components/border-box-two.vue';
import BorderBoxThree from './components/border-box-three.vue';
import BorderBoxFour from './components/border-box-four.vue';
import BorderBoxFive from './components/border-box-five.vue';
import BorderBoxSix from './components/border-box-six.vue';
import BorderBoxCenter from './components/border-box-center.vue';
import HeadCenter from './components/head-center.vue';
import {largeScreenApi} from "/@/api/business/large-screen/large-screen.js";

// -----------------------------------------------------------------地图显示-----------------------------------------
let map = null; // 地图实例
let AMapInstance = null; // AMap实例

// 初始化地图
const initMap = () => {
  const container = document.getElementById('container');
  if (!container) {
    console.error('Map container div not exist');
    return;
  }
  window._AMapSecurityConfig = {
    securityJsCode: 'fd83369091f64a0f25572251e0c9eae5',
  };

  AMapLoader.load({
    key: '1778f4eaad1a5a43d9b04ef0c9690b3f',
    version: '2.0',
    plugins: [
      'AMap.MouseTool',
      'AMap.ToolBar',
      'AMap.Scale',
      'AMap.HawkEye',
      'AMap.ControlBar',
      'AMap.Geometry',
      'AMap.AutoComplete',
      'AMap.PlaceSearch',
      'AMap.Marker',
      'AMap.InfoWindow'
    ],
  })
      .then((AMap) => {
        AMapInstance = AMap;
        map = new AMapInstance.Map('container', {
          center: [113.273625, 22.535275],
          zoom: 10,
          viewMode: '3D',
          pitch: 30,
          // mapStyle: 'amap://styles/darkblue',
          mapStyle: 'amap://styles/blue',
        });
      })
      .catch((e) => {
        console.error('地图加载失败:', e);
      });
};

onMounted(() => {
  initMap();
  getMapData();
});

// -----------------------------------------------------------------------------------边框数据请求
const boxOneData = ref({});
const boxTwoData = ref([]);
const boxThreeData = ref([]);
const boxFourData = ref({});
const boxFiveData = ref([]);
const boxSixData = ref([]);
// const boxCenterData = ref([]);

const regionList = ref([]);
const deviceList = ref([]);
const currentRegion = ref({});

// 获取地图数据
async function getMapData() {
  const regionRes = await largeScreenApi.queryRegionPage({
    queryTarget: 'region',
    queryValue: ''
  });
  const firstRegion = regionRes.data.regionList[0];

  const res1 = await largeScreenApi.queryConstructionSituation();
  const res2 = await largeScreenApi.queryDeviceTypePie();
  const res3 = await largeScreenApi.queryWorkOrderLog({
    pageNum: 1,
    pageSize: 50
  });
  const res4 = await largeScreenApi.queryRegionStatistics(firstRegion.id);
  const res5 = await largeScreenApi.querySevenDayDeviceEnergy({
    regionId: firstRegion.id
  });
  // const resC = await largeScreenApi.queryMaterialTop10();
  const res6 = await largeScreenApi.queryRegionWarnPage({
    regionId: firstRegion.id,
    pageNum: 1,
    pageSize: 50
  });


  const deviceRes = await largeScreenApi.queryNearbyDevice({
    longitude: firstRegion.longitude,
    latitude: firstRegion.latitude,
    limit: 1000,
    radius: 5.354693588738934,
    unit: "km",
  });

  boxOneData.value = res1.data;
  boxTwoData.value = res2.data;
  boxThreeData.value = res3.data.list;
  boxFourData.value = res4.data;
  boxFiveData.value = res5;
  boxSixData.value = res6.data.list;
  // boxCenterData.value = resC.data;

  regionList.value = regionRes.data.regionList;
  deviceList.value = deviceRes.data;
  // 设置初始地图中心和绘制区域、设备
  if (regionList.value.length > 0) {
    const firstRegion = regionList.value[0];
    if (firstRegion.longitude && firstRegion.latitude) {
      map.setCenter([firstRegion.longitude, firstRegion.latitude]);
      drawRegionPolygon(firstRegion);
      currentRegion.value = firstRegion;
      drawDeviceMarkers(deviceRes.data);
    }
  }
}

//------------------------------------------------------------------------------------地图绘制
let regionPolygon = null; // 区域多边形
let deviceMarkers = []; // 设备标记数组
let infoWindow = null; // 信息窗口
let currentMarkerIndex = 0; // 当前标记索引

// ----------------------------------------------------------------绘制区域多边形
function drawRegionPolygon(region) {
  if (regionPolygon) {
    regionPolygon.setMap(null);
  }

  // 调试输出原始坐标数据
  console.log('原始区域坐标:', region.latAndLon);

  // 匹配括号内的坐标对
  const coordPairs = region.latAndLon.match(/$([^)]+)$/g) || [];
  if (coordPairs.length < 3) {
    const alternativeMatches = region.latAndLon.match(/[\d.]+,\s*[\d.]+/g) || [];
    if (alternativeMatches.length >= 3) {
      coordPairs.push(...alternativeMatches);
    }
  }

  // 如果还是不足3个点，使用后备数据
  if (coordPairs.length < 3) {
    console.error('无效的区域坐标格式:', region.latAndLon);
    console.warn('使用区域中心点创建圆形作为后备方案');

    // 创建圆形作为后备方案
    regionPolygon = new AMapInstance.Circle({
      center: [region.longitude, region.latitude],
      radius: 500, // 500米半径
      fillOpacity: 0.2,
      fillColor: '#1E90FF',
      strokeWeight: 3,
      strokeColor: '#00BFFF',
    });

    regionPolygon.setMap(map);
    map.setZoom(10);
    return;
  }

  const latAndLonPoints = coordPairs.map(coord => {
    // 移除括号并分割经纬度
    const cleanCoord = coord.replace(/[()]/g, '');
    const [latStr, lonStr] = cleanCoord.split(',').map(s => s.trim());
    return [parseFloat(lonStr), parseFloat(latStr)];
  });

  console.log('解析后的坐标点:', latAndLonPoints);

  regionPolygon = new AMapInstance.Polygon({
    path: latAndLonPoints,
    fillOpacity: 0.2,
    fillColor: '#1E90FF',
    strokeWeight: 3,
    strokeColor: '#00BFFF',
  });
  regionPolygon.setMap(map);

  // 调整地图视野以适应多边形区域
  map.setFitView([regionPolygon]);
}

// -------------------------------------------------------绘制设备标记和信息窗口
function drawDeviceMarkers(devices) {
  // 清除之前的标记和信息窗口
  deviceMarkers.forEach(marker => marker.setMap(null));
  deviceMarkers = [];
  if (infoWindow) {
    infoWindow.close();
  }

  // 添加新的标记并绑定信息窗口
  devices.forEach(device => {
    if (device.longitude && device.latitude) {
      // 创建自定义信息窗口内容
      const infoContent = document.createElement('div');
      infoContent.style.padding = '10px';
      infoContent.style.background = 'linear-gradient(135deg, #0a2b5f 0%, #1a5599 100%)'; // 深蓝色渐变
      infoContent.style.color = '#e6f7ff'; // 浅蓝色文字
      infoContent.style.borderRadius = '5px';
      infoContent.style.border = '1px solid #2a93d5'; // 边框颜色
      infoContent.style.boxShadow = '0 4px 12px rgba(0, 60, 179, 0.8)'; // 深蓝色阴影
      infoContent.style.fontSize = '14px';
      infoContent.style.lineHeight = '1.5';
      // 增强文字对比度和层次
      infoContent.innerHTML = `
    <div style="margin-bottom: 8px; display: flex; align-items: center;">
      <div style="width: 6px; height: 6px; background: #4db3ff; border-radius: 50%; margin-right: 8px;"></div>
      <div>
        <strong style="color: #4db3ff;">设备名:</strong>
        <span>${device.deviceName || '未知'}</span>
      </div>
    </div>
    <div style="margin-bottom: 8px; display: flex; align-items: center;">
      <div style="width: 6px; height: 6px; background: #4db3ff; border-radius: 50%; margin-right: 8px;"></div>
      <div>
        <strong style="color: #4db3ff;">产品名:</strong>
        <span>${device.productName || '未知'}</span>
      </div>
    </div>
    <div style="display: flex; align-items: center;">
      <div style="width: 6px; height: 6px; background: #4db3ff; border-radius: 50%; margin-right: 8px;"></div>
      <div>
        <strong style="color: #4db3ff;">所属区域:</strong>
        <span>${device.regionName || '未知'}</span>
      </div>
    </div>
  `;
      infoWindow = new AMapInstance.InfoWindow({
        content: infoContent,
        offset: new AMapInstance.Pixel(0, -30),
        closeWhenClickOffset: true // 点击信息窗口外部区域关闭
      });

// 创建标记
      const marker = new AMapInstance.Marker({
        position: [device.longitude, device.latitude],
        title: device.deviceName || '设备',
        // icon: '/src/assets/images/large-screen/marker3.png', // 使用默认图标，或替换为自定义图标路径
        // iconStyle: {
        //   width: '1px', // 调整图标宽度
        //   height: '1px' // 调整图标高度
        // },
        offset: new AMapInstance.Pixel(0, -4)
      });

      // // 绑定点击事件打开信息窗口
      // marker.on('click', function () {
      //   infoWindow.open(map, marker.getPosition());
      // });

      marker.setMap(map);
      deviceMarkers.push(marker);
    }
  });

  // 启动定时器，循环显示每个标记点的信息窗体
  startInfoWindowTimer(devices);
}

// 启动定时器，循环显示每个标记点的信息窗体
function startInfoWindowTimer(devices) {
  clearInterval(window.infoWindowTimer); // 清除之前的定时器
  window.infoWindowTimer = setInterval(() => {
    if (deviceMarkers.length === 0) return;

    // 关闭当前信息窗体
    if (infoWindow && infoWindow.isOpen) {
      infoWindow.close();
    }

    // 显示下一个标记点的信息窗体
    currentMarkerIndex = (currentMarkerIndex + 1) % deviceMarkers.length;
    const marker = deviceMarkers[currentMarkerIndex];
    const device = devices[currentMarkerIndex];
    if (marker && device) {
      // 更新信息窗体内容
      const infoContent = document.createElement('div');
      infoContent.style.padding = '10px';
      infoContent.style.background = 'linear-gradient(135deg, #0a2b5f 0%, #1a5599 100%)';
      infoContent.style.color = '#e6f7ff';
      infoContent.style.borderRadius = '5px';
      infoContent.style.border = '1px solid #2a93d5';
      infoContent.style.boxShadow = '0 4px 12px rgba(0, 60, 179, 0.8)';
      infoContent.style.fontSize = '14px';
      infoContent.style.lineHeight = '1.5';
      infoContent.innerHTML = `
    <div style="margin-bottom: 8px; display: flex; align-items: center;">
      <div style="width: 6px; height: 6px; background: #4db3ff; border-radius: 50%; margin-right: 8px;"></div>
      <div>
        <strong style="color: #4db3ff;">设备名:</strong>
        <span>${device.deviceName || '未知'}</span>
      </div>
    </div>
    <div style="margin-bottom: 8px; display: flex; align-items: center;">
      <div style="width: 6px; height: 6px; background: #4db3ff; border-radius: 50%; margin-right: 8px;"></div>
      <div>
        <strong style="color: #4db3ff;">产品名:</strong>
        <span>${device.productName || '未知'}</span>
      </div>
    </div>
    <div style="display: flex; align-items: center;">
      <div style="width: 6px; height: 6px; background: #4db3ff; border-radius: 50%; margin-right: 8px;"></div>
      <div>
        <strong style="color: #4db3ff;">所属区域:</strong>
        <span>${device.regionName || '未知'}</span>
      </div>
    </div>
  `;
      infoWindow = new AMapInstance.InfoWindow({
        content: infoContent,
        offset: new AMapInstance.Pixel(0, -30),
        closeWhenClickOffset: true // 点击信息窗口外部区域关闭
      });
      infoWindow.open(map, marker.getPosition());
    }
  }, 3000); // 3秒切换一次
}

//--------------------------------------------------------------区域切换
// 区域切换
async function getNextRegion() {
  if (regionList.value.length === 0) {
    console.error('无可用的区域');
    return;
  }

  // 确定新区域：将原数组第一项移到末尾后，新第一项即为要切换的区域
  const regions = regionList.value;
  const nextRegion = regions.length > 1 ? regions[1] : regions[0];

  // 并行执行所有异步更新任务
  Promise.all([
    updateDeviceData(nextRegion),
    updateRegionStatistics(nextRegion),
    updateRegionEnergy(nextRegion),
    updateRegionWarn(nextRegion)
  ])
      .then(() => {
        console.log('所有异步更新任务完成');
        // 执行数组移位和视图更新
        const first = regions.shift();
        regions.push(first);
        currentRegion.value = nextRegion; // 当前区域设置为新区域

        //更新地图
        map.setCenter([nextRegion.longitude, nextRegion.latitude]);
        drawRegionPolygon(nextRegion);
      })
      .catch((error) => {
        console.error('异步更新任务失败:', error);
      });


}

// 更新设备数据
async function updateDeviceData(region) {
  try {
    const deviceRes = await largeScreenApi.queryNearbyDevice({
      longitude: region.longitude,
      latitude: region.latitude,
      limit: 1000,
      radius: 5.354693588738934,
      unit: "km",
    });
    drawDeviceMarkers(deviceRes.data);
  } catch (error) {
    console.error('设备数据更新失败:', error);
  }
}

// 更新区域统计数据
async function updateRegionStatistics(region) {
  try {
    const regionStatsRes = await largeScreenApi.queryRegionStatistics(region.id);
    boxFourData.value = regionStatsRes.data;
  } catch (error) {
    console.error('区域统计数据更新失败:', error);
  }
}

// 更新区域设备总能耗
async function updateRegionEnergy(region) {
  try {
    const regionEnergyRes = await largeScreenApi.querySevenDayDeviceEnergy({
      regionId: region.id
    });
    boxFiveData.value = regionEnergyRes;
  } catch (error) {
    console.error('区域设备总能耗更新失败:', error);
  }
}

// 更新区域设备报警信息
async function updateRegionWarn(region) {
  try {
    const regionWarnRes = await largeScreenApi.queryRegionWarnPage({
      regionId: region.id,
      pageNum: 1,
      pageSize: 50
    });
    boxSixData.value = regionWarnRes.data.list;
  } catch (error) {
    console.error('区域设备报警信息更新失败:', error);
  }
}

</script>

<style scoped>
.map-container {
  position: relative;
  width: 100%;
  height: 95vh;
}

#container {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>