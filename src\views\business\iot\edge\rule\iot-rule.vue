<!--
  * 设备管理
  *
  * @Author:    林丽专
  * @Date:      2025-03-27 20:31:09
  * @Copyright  2025 电子科技大学中山学院大数据与智能计算实验室
-->

<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="标题" class="smart-query-form-item">
        <a-input style="width: 200px" v-model:value="queryForm.label" placeholder="请输入标题" />
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="onSearch">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>

  <a-card size="small" :bordered="false" :hoverable="true">
    <!-- 表格操作行 begin -->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="showForm" type="primary" size="small">
          <template #icon>
            <PlusOutlined />
          </template>
          新建
        </a-button>
        <!--        <a-button @click="confirmBatchDelete" type="primary" danger size="small"-->
        <!--                  :disabled="selectedRowKeyList.length == 0">-->
        <!--          <template #icon>-->
        <!--            <DeleteOutlined />-->
        <!--          </template>-->
        <!--          批量删除-->
        <!--        </a-button>-->
      </div>
    </a-row>

    <!---------- 表格操作行 end ----------->

    <a-list :grid="{ gutter: 10, xs: 1, sm: 1, md: 2, lg: 2, xl: 3, xxl: 4 }" :data-source="data" :loading="initLoading">
      <template #renderItem="{ item, index }">
        <a-list-item>
          <a-card hoverable :loading="loading" size="default" :style="{
            background: 'linear-gradient(to bottom, rgba(88, 158, 255, 0.1), white)',
            boxShadow: '0px 4px 8px rgba(0, 0, 0, 0.1)'
          }">
            <template #title>
              <a class="titleBox">{{ item.label }}</a>
            </template>
            <template #extra>
              <div style="width: inherit; position: absolute; right: 30px; top: 15px">No{{ index + 1 }}
              </div>
              <a-checkbox v-model:checked="item.checked" style="position: absolute; right: 5px; top: 3px"
                @change="onSelectChange(item)" />
            </template>
            <div style="height: 100%; width: 100%; display: flex">
              <div style="flex: 2; display: flex; flex-direction: column">
                <span class="span-multiline-ellipsis">来源：{{ item.source }}</span>
                <span class="span-multiline-ellipsis">类型：{{item.type }}</span>
                <span class="span-multiline-ellipsis">是否禁用：
                  <a-switch :checked="item.disabled" checked-children="是" un-checked-children="否"
                    @change="switchChange(item)" /></span>
                <span class="span-multiline-ellipsis">排序号：
                  <a-input-number v-model:value="item.sort" min="1" style="margin-left: 8px; width: 80px;"
                    @blur="sorted(item)" />
                </span>
                <span class="span-multiline-ellipsis">部署时间：{{ item.deployTime }}</span>
              </div>
              <div style="flex: 1; display: flex; justify-content: center; align-items: center; z-index: 1;">
                  <img src="/@/assets/images/category/icon4.svg" alt="" style="max-width: 150px; max-height: 150px; object-fit: contain" />
              </div>
            </div>
            <template #actions>
              <span style="color: #108ee9" @click="showDetail(item.id)"><setting-outlined key="setting" />
                配置</span>
              <span style="color: #108ee9" @click="showForm(item)">
                <FormOutlined /> 编辑
              </span>
              <span style="color: #f56c6c" @click="onDelete(item)">
                <DeleteOutlined /> 删除
              </span>
            </template>
          </a-card>
        </a-list-item>
      </template>
    </a-list>
    <div class="smart-query-table-page">
      <a-pagination showSizeChanger showQuickJumper show-less-items :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.size" v-model:current="queryForm.current" v-model:pageSize="queryForm.size"
        :total="total" @change="queryData" @showSizeChange="queryData" :show-total="(total) => `共${total}条`" />
    </div>

    <iotRuleForm ref="formRef" @reloadList="queryData" />
  </a-card>
</template>
<script setup>
import { reactive, ref, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { smartSentry } from '/@/lib/smart-sentry';
import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
import TableOperator from '/@/components/support/table-operator/index.vue';
import { iotRuleApi } from '/@/api/business/iot/edge/iot-rule-api.js';
import iotRuleForm from './components/iot-rule-form.vue';
import {useIotStore} from '/@/store/modules/business/iot/iot.js';
// ---------------------------- 表格列 ----------------------------
const initLoading = ref(false);
const loading = ref(false);

// ---------------------------- 查询数据表单和方法 ----------------------------

const queryFormState = {
  source: 'rule',
  current: 1,
  size: 10,
  label: ""
};
// 查询表单form
const queryForm = reactive({ ...queryFormState });
// 表格数据
const data = ref([]);
// 总数
const total = ref(0);
// 重置查询条件
function resetQuery() {
  let size = queryForm.size;
  Object.assign(queryForm, queryFormState);
  queryForm.size = size;
  queryData();
}

// 搜索
function onSearch() {
  console.log(queryForm, "查询")
  queryForm.current = 1;
  queryData();
}

// 查询数据
async function queryData() {
  initLoading.value = true;
  loading.value = true;
  try {
    let queryResult = await iotRuleApi.query(queryForm);
    total.value = queryResult.data.total;
    data.value = queryResult.data.records;
    console.log("queryResult.data.records",queryResult.data.records)
    selected = [];
    data.value.forEach((item) => {
      selected.push({
        id: item.id,
        checked: false,
      });
    });
  } catch (e) {
    console.log('e', e);
    smartSentry.captureError(e);
  } finally {
    initLoading.value = false;
    loading.value = false;
  }
}
onMounted(queryData);

// ---------------------------- 添加/修改 ----------------------------
const formRef = ref();
const detailRef = ref();

function showForm(data) {
  console.log("打开from表单")
  formRef.value.show(data);
}

async function sorted(data) {
  try {
    await iotRuleApi.sort({
      id: data.id,
      sort: data.sort
    });
    // message.success('操作成功');
    queryData();
  } catch (err) {
    smartSentry.captureError(err);
  }
}
async function switchChange(data) {
  try {
    data.disabled= !data.disabled
    await iotRuleApi.disabled({
      id: data.id,
      disabled: data.disabled,
      flowData: data.flowData
    });
    console.log(data.status, 'data.status');
    queryData();
  } catch (err) {
    console.log(err);
    smartSentry.captureError(err);
  }
}
// ---------------------------- 单个删除 ----------------------------
//确认删除
function onDelete(data) {
  Modal.confirm({
    title: '提示',
    content: '确定要删除选吗?',
    okText: '删除',
    okType: 'danger',
    onOk() {
      requestDelete(data);
    },
    cancelText: '取消',
    onCancel() { },
  });
}

//请求删除
async function requestDelete(data) {
  console.log("调用删除接口啦")
  SmartLoading.show();
  try {
    // let deleteForm = {
    //   goodsIdList: selectedRowKeyList.value,
    // };
    console.log("data.id", data.id);
    let arr = []
    arr.push(data.id)
    await iotRuleApi.delete(arr);
    message.success('删除成功');
    queryData();
  } catch (e) {
    console.log("e", e);
    smartSentry.captureError(e);
  } finally {
    SmartLoading.hide();
  }
}

// ---------------------------- 批量删除 ----------------------------

// 选择表格行
const selectedRowKeyList = ref([]);
let selected = [];

function onSelectChange(item) {
  const index = selected.findIndex((select) => select.id === item.id);
  if (selected[index].checked) {
    selected[index].checked = false;
    selectedRowKeyList.value = selectedRowKeyList.value.filter((id) => id !== item.id);
  } else {
    selected[index].checked = true;
    selectedRowKeyList.value.push(item.id);
  }
}

// // 批量删除
// function confirmBatchDelete() {
//   Modal.confirm({
//     title: '提示',
//     content: '确定要批量删除这些数据吗?',
//     okText: '删除',
//     okType: 'danger',
//     onOk() {
//       requestBatchDelete();
//     },
//     cancelText: '取消',
//     onCancel() { },
//   });
// }

// //请求批量删除
// async function requestBatchDelete() {
//     console.log("调用批量删除接口啦")
//     try {
//         SmartLoading.show();
//         await iotCategoryApi.batchDelete(selectedRowKeyList.value);
//         message.success('删除成功');
//         queryData();
//     } catch (e) {
//         smartSentry.captureError(e);
//     } finally {
//         SmartLoading.hide();
//     }
// }


// 页面跳转
//http://node-red.iot.wisedataacademic.com:1880/#flow/
// 4485b9db0a4388de7b008f1ad6038c4a(id)
// ?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJibGFkZXguY24iLCJhdWQiOlsiYmxhZGV4Il0sInRva2VuX3R5cGUiOiJhY2Nlc3NfdG9rZW4iLCJjbGllbnRfaWQiOiJzYWJlcjMiLCJ0ZW5hbnRfaWQiOiIwMDAwMDAiLCJ1c2VyX2lkIjoiMTEyMzU5ODgyMTczODY3NTIwMSIsImRlcHRfaWQiOiIxMTIzNTk4ODEzNzM4Njc1MjAxIiwicG9zdF9pZCI6IjExMjM1OTg4MTc3Mzg2NzUyMDEiLCJyb2xlX2lkIjoiMTEyMzU5ODgxNjczODY3NTIwMSIsImFjY291bnQiOiJhZG1pbiIsInVzZXJfbmFtZSI6ImFkbWluIiwibmlja19uYW1lIjoi566h55CG5ZGYIiwicmVhbF9uYW1lIjoi566h55CG5ZGYIiwicm9sZV9uYW1lIjoiYWRtaW5pc3RyYXRvciIsImRldGFpbCI6eyJ0eXBlIjoid2ViIn0sImV4cCI6MTc0NjQ1MjQ4MCwibmJmIjoxNzQ2NDQ4ODgwfQ.RNbnf7OtwAZAZ9ETY0-1wxLqyP0swQQ5Xd_2f-q6Igw
// 正确获取 token 的方式
const iotStore = useIotStore(); // 获取 store 实例
iotStore.getIotToken();  // 调用方法获取 token 
const token = iotStore.iotToken; // 获取 token 值

// 然后在 showDetail 函数中使用
function showDetail(id) {
    const baseUrl = 'http://node-red.iot.wisedataacademic.com:1880/#flow/';
    const fullUrl = `${baseUrl}${id}?token=${token}`;
    // console.log("打印网站地址1", fullUrl,id);
    window.open(fullUrl);
    // console.log("打印网站地址2","1",token);
}
</script>

<style scoped lang="less">
:deep(.ant-card-body) {
  padding: 10px 20px;
}

.scroll-container {
  height: 580px;
  /* 设置容器的高度 */
  overflow-y: auto;
  /* 启用 y 轴滚动 */
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 8px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.span-multiline-ellipsis {
     // display: flex;
     // width: 230px;
     // margin: 5px;
     display: -webkit-box;          /* Flexbox 模式 */
     -webkit-box-orient: vertical; /* 设置盒子为垂直方向 */
     overflow: hidden;             /* 隐藏多余内容 */
     text-overflow: ellipsis;      /* 增加省略号 */
     -webkit-line-clamp: 1;        /* 限制显示2行，多出的内容隐藏 */
     max-width: 100%;              /* 设置最大宽度 */
     line-height: 1.5;             /* 设置行高（根据需要调整） */
     max-height: calc(1.5em * 2);  /* 与行高和行数匹配 */
     word-break: break-word;       /* 防止单词溢出容器 */
     font-size: 0.8em;             /* 设置字体大小 */
     margin-bottom: 10px;           /* 增加下边距 */
   }

   .detail{
     display: inline-block;
     padding: 5px 10px;
     background-color: rgba(88, 158, 255, 0.1); /* 设置背景颜色为淡蓝色 */
     border: 1px solid rgba(88, 158, 255, 0.1); /* 边框颜色 */
     border-radius: 8px; /* 圆角 */
     color: #2c77f1; /* 字体颜色 */
     font-size: 16px; /* 字体大小 */
     text-align: center; /* 文字居中 */
     font-weight: bold; /* 加粗字体 */
     box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
   }

.titleBox {
  border: 1px solid #d4e3fc;
  padding: 0 8px;
  background-color: #e9f1fd;
}
</style>