import { postRequest, getRequest } from '/@/lib/axios';

export const largeScreenApi = {

    // 建设情况查询
    queryConstructionSituation: (params) => {
        return postRequest('/home/<USER>/state',params);
    },

    // 设备运行日志
    queryDeviceRunLog: (params) => {
        return postRequest('/iotDevice/log/message', params);
    },

    // 工单处理进度日志
    queryWorkOrderLog: (params) => {
        return postRequest('/orderLog/queryPage', params);
    },

    // 分页查询区域下设备的报警信息
    queryRegionWarnPage: (params) => {
        return postRequest('/maintainFault/queryRegionWarnPage', params);
    },

    //  分页查询区域列表
    queryRegionPage: (params) => {
        return postRequest('/light/queryLightInformation', params);
    },

    // 获取指定坐标附近x KM的设备
    queryNearbyDevice: (params) => {
        return postRequest('/iotDevice/getDeviceByCoordinate', params);
    },

    // 获取区域所在的数据统计信息
    queryRegionStatistics: (id) => {
        return getRequest(`/iotRegion/getRegionDeviceTotalInfo/${id}`);
    },

    // 素材投放top10
    queryMaterialTop10: () => {
        return getRequest('/mediaCampaign/mediaMaterialTop');
    },

    // 设备类型饼状图
    queryDeviceTypePie: () => {
        return getRequest('/iotDevice/deviceTypeNumber');
    },

    // 近七天总设备能耗(可根据区域id查询)
    querySevenDayDeviceEnergy: (regionId) => {
        return postRequest('/energy/chart/energyConsumeLastWeek',regionId);
    }

};
