<template>
  <a-row>
    <a-cascader
      v-model:value="identifier"
      :options="dataCommand"
      @change="commandChange"
      style="width: 100%"
      placeholder="请选择命令"
    />
  </a-row>
  <a-row style="margin: 16px 0">
    <code-editor ref="editorRef" v-model:value="code" :json="true" theme="nord" height="400px"/>
  </a-row>
  <a-row>
    <a-button type="primary" @click="sendCommand">发送指令</a-button>
    <a-button class="smart-margin-left10" @click="prettyCode">格式化数据</a-button>
    <a-button class="smart-margin-left10" @click="resetCommand">重置数据</a-button>
  </a-row>
</template>

<script setup>
import { ref, watch } from 'vue';
import { message } from 'ant-design-vue';
import CodeEditor from '/@/components/business/code-editor/index.vue';
import { validatejson, validatenull } from '/@/utils/validate';

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['sendCommand']);

const code = ref('');
const command = ref({});
const identifier = ref([]);
const dataCommand = ref([]);
const editorRef = ref(null);

// 监听模型数据变化
watch(() => props.modelValue, (newVal) => {
  if (validatenull(newVal)) {
    return;
  }
  loadCommand();
}, { deep: true });

// 加载命令数据
function loadCommand() {
  dataCommand.value = props.modelValue.blocks.map(block => ({
    value: block.id,
    label: block.blockName,
    children: props.modelValue.commands
      .filter(command => command.blockId === block.id)
      .map(item => ({
        value: item.identifier,
        label: item.name + ' - ' + item.identifier,
      }))
  }));
}

// 命令选择变化
function commandChange(value) {
  if (validatenull(value) || value.length === 0) {
    return;
  }
  identifier.value = value;
  command.value = props.modelValue.commands.find(item => item.identifier === value[1]);
}

// 发送命令
function sendCommand() {
  if (validatenull(identifier.value)) {
    message.warning('请先选择命令');
    return;
  }
  if (validatenull(code.value)) {
    message.warning('请填写调试数据');
    return;
  }
  if (!validatejson(code.value)) {
    message.warning('Json格式不正确');
    return;
  }
  const input = JSON.stringify(JSON.parse(code.value));
  const output = JSON.stringify({ time: new Date().getTime() });
  const data = {
    commandName: command.value.name,
    identifier: identifier.value[1],
    input,
    output
  };
  emit('sendCommand', data);
  message.success("发送成功");
}

// 格式化代码
function prettyCode() {
  editorRef.value?.prettyCode();
}

// 重置命令
function resetCommand() {
  code.value = '';
}

// 暴露方法供外部使用
// defineExpose({
//   loadCommand,
//   resetCommand
// });
</script> 