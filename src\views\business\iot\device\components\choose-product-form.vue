<!--
  * 设备表
  *
  * @Author:    李帅兵
  * @Date:      2025-03-21 22:21:07
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
  <a-modal title="请选择所属产品" :open="showCategoryModal" @ok="handleOk" @cancel="handleCancel" ok-text="确定" cancel-text="取消"
    :width="800" :zIndex="1200">
    <a-form class="smart-query-form" ref="chooseProductFormRef">
      <a-row class="smart-query-form-row">
        <a-form-item label="名称" class="smart-query-form-item">
          <a-input style="width: 140px;" v-model:value="queryForm.productName" placeholder="请输入产品名称" />
        </a-form-item>
        <a-form-item label="编码" class="smart-query-form-item">
          <a-input style="width: 140px;" v-model:value="queryForm.productKey" placeholder="请输入产品编码" />
        </a-form-item>
        <a-form-item label="密钥" class="smart-query-form-item">
          <a-input style="width: 140px;" v-model:value="queryForm.productSecret" placeholder="请输入产品密钥" />
        </a-form-item>
        <a-form-item class="smart-query-form-item">
          <a-button type="primary" @click="onSearch">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
          <a-button @click="resetQuery" class="smart-margin-left10">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
        </a-form-item>
      </a-row>
    </a-form>

    <!-- 表格 -->
    <!-- 数据表格 -->
    <a-table :columns="columns" :dataSource="data" :pagination="false" :customRow="handleCustomRow"
      :rowClassName="setRowClassName" bordered>
      <template #emptyText>
        <a-empty description="暂无数据" />
      </template>
    </a-table>

    <!-- 分页信息 -->
    <div class="smart-query-table-page">
      <a-pagination showSizeChanger showQuickJumper show-less-items :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.size" v-model:current="queryForm.current" v-model:pageSize="queryForm.size"
        :total="total" @change="queryData" @showSizeChange="queryData" :show-total="(total) => `共${total}条`" />
    </div>
  </a-modal>
</template>
  <script setup>
    import { reactive, ref, nextTick , onMounted} from 'vue';
    import _ from 'lodash';
    import { message } from 'ant-design-vue';
    import { SmartLoading } from '/src/components/framework/smart-loading';
    import { iotProductApi } from '/@/api/business/iot/product/iot-product-api';
    // import { iotDeviceApi } from '/@/api/business/iot/device/iot-device-api';
    import { smartSentry } from '/@/lib/smart-sentry';
    import SmartWangeditor from '/@/components/framework/wangeditor/index.vue';
    import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
    import TableOperator from '/@/components/support/table-operator/index.vue';
    // import upload from '/@/components/upload/upload.vue';
    // ------------------------ 事件 ------------------------
  
    const emits = defineEmits(['emitSelectedProduct']);
  
    // ------------------------ 显示与隐藏 ------------------------
    // 是否显示
    const showCategoryModal = ref(false);
  
    function show() {
      showCategoryModal.value = true;
    }
  
    function onClose() {
      showCategoryModal.value = false;
    }
  
    // ------------------------ 表单 ------------------------
  
    // 组件ref
    const chooseProductFormRef = ref();
    // 品类模态框

// 点击“确定”按钮的处理逻辑
const handleOk = () => {
  showCategoryModal.value = false; // 关闭模态框
  console.log('点击了“确定”');
};

// 点击“取消”或模态框关闭按钮的处理逻辑
const handleCancel = () => {
  showCategoryModal.value = false; // 关闭模态框
  console.log('点击了“取消”');
};

// 表格列配置
const columns = [
  {
    title: '产品名称',
    dataIndex: 'productName',
    key: 'productName',
    width: 200,
  },
  {
    title: '产品编码',
    dataIndex: 'productKey',
    key: 'productKey',
    width: 200,
  },
  {
    title: '产品密钥',
    dataIndex: 'productSecret',
    key: 'productSecret',
    width: 200,
  },
  {
    title: '上线时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    width: 200,
  },
];



const queryFormState = {
    productName: undefined,
    productKey: undefined,
    productSecret: undefined,
    current: 1,
    size: 9,
    sortItemList: [{ isAsc: false, column: 'create_time' }],
  };
  const loading = ref(false);
  // 查询表单form
  const queryForm = reactive({ ...queryFormState });
  // 表格数据
  const data = ref([]);
  // 总数
  const total = ref(0);
  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.size;
    Object.assign(queryForm, queryFormState);
    queryForm.size = pageSize;
    queryData();
  }

  // 搜索
  function onSearch() {
    console.log(queryForm, '查询');
    queryForm.current = 1;
    queryData();
  }

  // 查询数据
  async function queryData() {
    try {
        // let queryResult = 123;
      let queryResult = await iotProductApi.page(queryForm);
      console.log(queryResult, '查询结果222');
      total.value = queryResult.data.total;
      data.value = queryResult.data.records;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      loading.value = false;
    }
  }
  onMounted(queryData);


  // 当前选中行的ID
const selectedRowId = ref(null);

  const handleCustomRow = (record) => {
  return {
    onClick: (event) => {
      console.log("行点击事件", record);
      emits('emitSelectedProduct', {
        productKey: record.productKey,
        productName: record.productName,
    });
      selectedRowId.value = record.id;
    }
  };
};


// 设置行类名
const setRowClassName = (record) => {
  return record.id === selectedRowId.value ? 'selected-row' : '';
};
  

  
    defineExpose({
      show,
    });
  </script>
  <style scoped>
     .label-container {
    position: relative;
    display: inline-block;
    padding-left: 18px; /* 为图标留出空间 */
  }
  
  /* 提示图标定位 */
  .label-tooltip {
    position: absolute;
    left: 0;
    top: 0;
  }
  
  /* 保持与其他表单项对齐 */
  .custom-label-form-item :deep(.ant-form-item-label) {
    /* padding-bottom: 4px; */
    line-height: 2.5;
  }
  
  /* 图标样式调整 */
  .label-tooltip .anticon {
    font-size: 14px;
    color: #1890ff;
    cursor: help;
    
  }
  
  /* 输入框组宽度适配 */
  .custom-label-form-item :deep(.ant-input-group) {
    width: 100%;
  
  }

  /* 输入框组宽度适配 */
.custom-label-form-item :deep(.ant-input-group) {
  width: 100%;
}

:deep(.ant-table .selected-row) {
  background-color: #e6f7ff !important; /* 浅蓝色背景 */
}
  </style>