<template>
  <a-card size="small" :bordered="false" :hoverable="true" style="min-height: 1200px;"> 
    <a-space style="margin-bottom: 10px">
      <a-button type="text" @click="back"> <left-outlined /> 返回 </a-button>
      <a-divider type="vertical" style="border-color: #dcdfe6" />
      <span class="title">{{ deviceStats.regionName || '设备列表' }}</span>
    </a-space>
    <a-divider style="margin: 5px 0 10px 0" />

    <!-- 数据卡片 -->
    <a-row :gutter="[16, 16]" class="data-cards">
      <a-col :span="6">
        <a-card class="data-card" hoverable>
          <div class="data-card-content">
            <div class="data-card-value">{{ deviceStats.deviceNum }}</div>
            <div class="data-card-title">设备数量</div>
          </div>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card class="data-card" hoverable>
          <div class="data-card-content">
            <div class="data-card-value">{{ deviceStats.productNum }}</div>
            <div class="data-card-title">产品数量</div>
          </div>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card class="data-card" hoverable>
          <div class="data-card-content">
            <div class="data-card-value">{{ deviceStats.lightNum }}</div>
            <div class="data-card-title">总灯杆数</div>
          </div>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card class="data-card" hoverable>
          <div class="data-card-content">
            <div class="data-card-value">约{{ deviceStats.regionAreas }} m²</div>
            <div class="data-card-title">区域面积</div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 图表区域 -->
    <a-row :gutter="[16, 16]" style="margin-top: 16px">
      <a-col :span="12">
        <a-card class="chart-card" hoverable>
          <div class="chart-header">
            <span class="chart-title">地图热力图</span>
            <div class="chart-desc">展示智能灯杆的地理分布密度。</div>
          </div>
          <div id="mapContainer" class="map-container"></div>
        </a-card>
      </a-col>
      <a-col :span="12">
        <a-card class="chart-card" hoverable>
          <div class="chart-header">
            <span class="chart-title">饼图/条形图</span>
            <div class="chart-desc">显示不同设备类型的占比。</div>
          </div>
          <div ref="pieChart" class="chart-container"></div>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="[16, 16]" style="margin-top: 16px">
      <a-col :span="24">
        <a-card class="chart-card" hoverable>
          <div class="chart-header">
            <span class="chart-title">设备列表</span>
            <div class="chart-desc">列出设备数量、安装时间、供应商等基础信息。</div>
          </div>
          <a-table
            :columns="columns"
            :data-source="deviceList"
            :pagination="false"
            style="min-height: 400px;"
            :scroll="{ y: 400 }"
            size="small"
            :loading="tableLoading"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'deviceStatus'">
                <a-tag :color="getDeviceStatusEnumDesc(record.deviceStatus).color">
                  {{ getDeviceStatusEnumDesc(record.deviceStatus).desc }}
                </a-tag>
              </template>
              <template v-if="column.dataIndex === 'deviceType'">
                {{ getDeviceTypeDesc(record.deviceType) }}
              </template>
              <template v-if="column.dataIndex === 'status'">
                {{ getDeviceEnumDesc(record.status) }}
              </template>
              <template v-if="column.dataIndex === 'createUserId'">
                <EmployeeSelect v-model:value="record.createUserId"  disabled/>
              </template>
              <template v-if="column.dataIndex === 'buildTime'">
                {{ record.buildTime ? dayjs(record.buildTime).format('YYYY-MM-DD') : '' }}
              </template>
            </template>
          </a-table>
          <div class="smart-query-table-page">
            <a-pagination
              showSizeChanger
              showQuickJumper
              show-less-items
              :pageSizeOptions="PAGE_SIZE_OPTIONS"
              :defaultPageSize="queryForm.pageSize"
              v-model:current="queryForm.pageNum"
              v-model:pageSize="queryForm.pageSize"
              :total="deviceStats.deviceNum"
              @change="getDeviceList"
              :show-total="(total) => `共${total}条`"
            />
        </div>
        </a-card>
      </a-col>
    </a-row>
  </a-card>
</template>

<script setup>
  import { ref, reactive, onMounted, nextTick, onUnmounted } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { LeftOutlined } from '@ant-design/icons-vue';
  import { useUserStore } from '/@/store/modules/system/user';
  import * as echarts from 'echarts';
  import { propertyReportApi } from '/@/api/business/operate/property/property-report-api';
  import dayjs from 'dayjs';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import {
    getDeviceTypeDesc,
    getDeviceStatusEnumDesc,
    getDeviceEnumDesc,
  } from '/@/constants/business/iot/device/iot-device-const.js';
  import AMapLoader from '@amap/amap-jsapi-loader';
  import { message } from 'ant-design-vue';
  import EmployeeSelect from '/@/components/system/employee-select/index.vue';
  // 路由相关
  const router = useRouter();
  const route = useRoute();
  const regionId = route.query.regionId;
  const timeValue = route.query.timeValue;
  const lng = route.query.lng;
  const lat = route.query.lat;
  // 处理时间范围 - 使用dayjs简化时间处理
  const { startTime, endTime } = (() => {
    const date = timeValue ? dayjs(timeValue) : dayjs();
    return {
      startTime: date.startOf('month').format('YYYY-MM-DD'),
      endTime: date.endOf('month').format('YYYY-MM-DD')
    };
  })();

  // 返回上一页
  const back = () => {
    router.push({
      path: '/operate/device/property',
      query: {}
    });
    useUserStore().closeTagNav(route.name, false);
    deviceList.value = [];
  };

//----------------------------------- 数据统计 -----------------------------------
  const deviceStats = reactive({}); 
  const deviceNum = ref(0);
  async function getDeviceInfo(){
    const params ={
      regionId: regionId,
      beginTime: startTime,
      endTime: endTime
    }
    const res = await propertyReportApi.propertyReportBaseInfo(params);
    if(res.data){
      Object.assign(deviceStats, res.data);
      if (deviceStats.regionAreas) {
        // 将区域面积四舍五入到最接近的百位数
        deviceNum.value = deviceStats.deviceNum;
        console.log('设备数量',deviceNum.value)
        deviceStats.regionAreas = Math.round(deviceStats.regionAreas / 100) * 100;
      }
    }
    await getDeviceList(deviceNum.value);
    await queryDeviceType(deviceNum.value);
    await queryLocation();
  }
  
//----------------------------------- 地图相关 -----------------------------------
  // 地图实例和热力图实例
  let map = null;
  let heatmap = null;
  let AMapInstance = null;

  // 初始化地图
  const initMap = () => {
    // 安全配置
    window._AMapSecurityConfig = {
      securityJsCode: 'fd83369091f64a0f25572251e0c9eae5',
    };
    
    // 加载地图
    AMapLoader.load({
      key: '1778f4eaad1a5a43d9b04ef0c9690b3f',
      version: '2.0',
      plugins: ['AMap.HeatMap', 'AMap.ToolBar', 'AMap.Scale']
    }).then((AMap) => {
      AMapInstance = AMap;
      
      // 创建地图实例
      map = new AMap.Map('mapContainer', {
        center: [lng,lat], // 默认中心点
        zoom: 14,
        viewMode: '2D'
      });
      
      // 添加控件
      map.addControl(new AMap.Scale({
        position: 'LB'
      }));
      
      map.addControl(new AMap.ToolBar({
        position: 'RB'
      }));
      
      // 初始化热力图
      initHeatMap();
    }).catch(e => {
      console.error('地图初始化失败', e);
      message.error('地图初始化失败');
    });
  };
  
  // 初始化热力图
  const initHeatMap = () => {
    if (!map || !AMapInstance) return;
    
    // 清除已有热力图
    if (heatmap) {
      heatmap.setMap(null);
      heatmap = null;
    }
    
    // 生成模拟热力数据
    // const heatmapData = location.value
    const heatmapData = generateHeatmapData();

    // 创建热力图实例
    heatmap = new AMapInstance.HeatMap(map, {
      radius: 25, // 热力图的半径
      opacity: [0, 0.8], // 热力图的透明度
      gradient: {
        0.5: 'blue',
        0.65: 'rgb(117,211,248)',
        0.7: 'rgb(0, 255, 0)',
        0.9: '#ffea00',
        1.0: '#e26d0e'
      }
    });
    
    // 设置热力图数据
    heatmap.setDataSet({
      data: heatmapData,
      max: 10
    });
  };


  // 生成模拟热力数据
  const generateHeatmapData = () => {
    const centerLng = 113.277324;
    const centerLat = 22.534139;
    // 中心点坐标
    const points = [];
    
    // 生成100个随机点
    for (let i = 0; i < 100; i++) {
      // 随机偏移量，模拟设备分布
      const offsetLng = (Math.random(1.7,4.1123) -0.5) * 0.001;
      const offsetLat = (Math.random(0.4,1.6) - 0.545) * 0.0025;
      // 使靠近中心的点更密集（模拟中心区域设备密度更高）
      const distanceFromCenter = Math.sqrt(offsetLng * offsetLng + offsetLat * offsetLat);
      const weight = Math.max(0.1, 1 - distanceFromCenter * 20); // 权重与距离成反比
      // 随机点的权重
      const randomWeight = Math.round(Math.random() * 1.1 + weight * 5);
      
      points.push({
        lng: centerLng + offsetLng,
        lat: centerLat + offsetLat,
        count: randomWeight
      });
    }
    return points;
  };
  
//----------------------------------- 设备列表 -----------------------------------
  // 设备列表
  const columns = [
    { title: '产品名称', dataIndex: 'productName', key: 'productName' },
    { title: '设备名称', dataIndex: 'deviceName', key: 'deviceName' },
    { title: '设备类型', dataIndex: 'type', key: 'type' },
    { title: '安装时间', dataIndex: 'buildTime', key: 'buildTime', render: (text) => text ? dayjs(text).format('YYYY-MM-DD') : '' },
    { title: '经度', dataIndex: 'lng', key: 'lng' },
    { title: '纬度', dataIndex: 'lat', key: 'lat' },
  ];
  const deviceList = ref([]);
  const tableLoading = ref(false);
  const queryForm = ref({
    pageNum: 1,
    pageSize: 10,
    sortItemList: [{ isAsc: false, column: 'create_time' }],
  });
  async function getDeviceList(){
    const res = await propertyReportApi.reportRefQueryPage({
      ...queryForm.value,
      deviceNum: deviceNum.value
    });
    if(res.data){
      deviceList.value = res.data.list;
    }
  }

  const deviceTypeList = ref([]);
  async function queryDeviceType(deviceNum) {
    const res = await propertyReportApi.queryDeviceType(deviceNum)
    deviceTypeList.value = Object.keys(res.data).map(item => {
      return {
       name: item,
       value: res.data[item]
      }
    })
  }

  const location = ref()
  async function queryLocation(){
    console.log("1111");
    const res = await propertyReportApi.queryLocation()
    location.value = res.data
  }
//----------------------------------- 初始化饼图 -----------------------------------
  const pieChart = ref(null);
  // 图表实例
  let pieChartInstance = null;
  // 初始化饼图
  const initPieChart = () => {
    if (pieChart.value) {
      pieChartInstance = echarts.init(pieChart.value);
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
        },
        series: [
          {
            name: '设备类型',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: deviceTypeList.value
          }
        ]
      };
      pieChartInstance.setOption(option);
    }
  };
  // 窗口大小改变时重绘图表
  const resizeCharts = () => {
    if (pieChartInstance) pieChartInstance.resize();
    if (map) {
      map.resize();
    }
  };

  // 页面挂载和卸载
  onMounted(async () => {
    await getDeviceInfo();
    window.addEventListener('resize', resizeCharts);
    nextTick(() => {
      initMap(); // 初始化地图
      initPieChart(); // 只初始化饼图
    });
  });

  onUnmounted(() => {
    window.removeEventListener('resize', resizeCharts);
    pieChartInstance?.dispose();
    // 销毁地图实例
    if (map) {
      map.destroy();
      map = null;
    }
  });
</script>

<style lang="less" scoped>
  .title {
    color: #606266;
    font-weight: bold;
    font-size: 18px;
  }

  .data-cards {
    margin-bottom: 20px;
  }

  .data-card {
    background: linear-gradient(rgba(64, 158, 255, 0.1), rgba(64, 158, 255, 0.05));
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .data-card-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 20px 0;
    }

    .data-card-value {
      font-size: 28px;
      font-weight: bold;
      color: #409eff;
      margin-bottom: 10px;
    }

    .data-card-title {
      font-size: 14px;
      color: #606266;
    }
  }

  .chart-card {
    margin-bottom: 16px;
    border-radius: 8px;
    
    .chart-header {
      padding: 0 0 16px 0;
    }

    .chart-title {
      font-size: 16px;
      font-weight: bold;
      color: #303133;
    }

    .chart-desc {
      font-size: 12px;
      color: #909399;
      margin-top: 5px;
    }

    .chart-container {
      height: 350px;
      width: 100%;
    }
  }
  
  .map-container {
    height: 350px;
    width: 100%;
    position: relative;
  }

  :deep(.ant-card-body) {
    padding: 16px;
  }
</style>
