<!--
  * 设备分析 设备预警 list
  *
  * @Author:    linwj
  * @Date:      2025-04-21 16:30:26
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="设备编号" class="smart-query-form-item">
        <a-input style="width: 200px" placeholder="请输入设备名称" />
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="onSearch">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格 begin ----------->
    <a-table size="small" :dataSource="tableData" :columns="columns" rowKey="id" bordered :loading="tableLoading" :pagination="true">
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'warnLevel'">
          <a-tag :color="getWarnLevelColor(record.warnLevel)">
            {{ record.warnLevel }}
          </a-tag>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button @click="showDetail(record)" type="link">详情</a-button>
          </div>
        </template>
      </template>
    </a-table>

    <DeviceWarnForm ref="formRef" @reloadList="queryData" />

    <!---------- 详情抽屉 begin ----------->
    <a-drawer title="设备详情" :width="720" :visible="detailVisible" :body-style="{ paddingBottom: '80px' }" @close="onDetailClose">
      <a-descriptions title="设备基本信息" bordered>
        <a-descriptions-item label="设备编号">{{ currentDevice.deviceNumber }}</a-descriptions-item>
        <a-descriptions-item label="设备类型">{{ currentDevice.deviceType }}</a-descriptions-item>
        <a-descriptions-item label="安装位置">A区-1号楼-3层</a-descriptions-item>
        <a-descriptions-item label="发生区域">电子科技大学清水河校区</a-descriptions-item>
        <a-descriptions-item label="生产厂家">中山睿数信息技术有限公司</a-descriptions-item>
        <a-descriptions-item label="出厂日期">2023-01-15</a-descriptions-item>
        <a-descriptions-item label="设备状态">运行中</a-descriptions-item>
        <a-descriptions-item label="最后维护时间">2025-04-15</a-descriptions-item>
        <a-descriptions-item label="维护人员">张三</a-descriptions-item>
      </a-descriptions>
      <a-divider />

      <h3>故障历史记录</h3>
      <a-timeline>
        <a-timeline-item v-for="(item, index) in faultHistory" :key="index" :color="getTimelineColor(item.warnLevel)">
          <p>{{ item.occurTime }}</p>
          <p>
            {{ item.warnType }} -
            <a-tag :color="getWarnLevelColor(item.warnLevel)">{{ item.warnLevel }}</a-tag>
          </p>
          <p>持续时间：{{ item.durationTime }}分钟</p>
          <p>处理结果：{{ item.status }}</p>
        </a-timeline-item>
      </a-timeline>

      <div class="drawer-footer">
        <a-button style="margin-right: 8px" @click="onDetailClose">关闭</a-button>
      </div>
    </a-drawer>
    <!---------- 详情抽屉 end ----------->
  </a-card>
</template>

<script setup>
  import { reactive, ref } from 'vue';
  import { message, Modal } from 'ant-design-vue';
  import { SearchOutlined, ReloadOutlined } from '@ant-design/icons-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { orderLogApi } from '/@/api/business/work-order-log/order-log-api.js';
  import { smartSentry } from '/@/lib/smart-sentry';
  import DeviceWarnForm from './device-warn-form.vue';

  // 设备数据
  const devices = [
    {
      id: '1',
      deviceNumber: 'LJ-001',
      deviceType: '传感器',
      warnType: '过温预警',
      warnLevel: '紧急',
      occurTime: '2025-04-21 16:30:26',
      durationTime: 20,
      status: '已忽略',
      location: 'A区-1号楼-3层',
      manufacturer: '中山睿数信息技术有限公司',
      productionDate: '2023-01-15',
      deviceStatus: '运行中',
      lastMaintenance: '2025-04-15',
      maintainer: '张三',
    },
    {
      id: '2',
      deviceNumber: 'LJ-002',
      deviceType: '控制器',
      warnType: '通信中断',
      warnLevel: '警告',
      occurTime: '2025-03-15 09:12:45',
      durationTime: 45,
      status: '已修复',
      location: 'B区-2号楼-1层',
      manufacturer: '中山睿数信息技术有限公司',
      productionDate: '2023-02-10',
      deviceStatus: '运行中',
      lastMaintenance: '2025-04-10',
      maintainer: '李四',
    },
    {
      id: '3',
      deviceNumber: 'LJ-003',
      deviceType: '传感器',
      warnType: '电压异常',
      warnLevel: '注意',
      occurTime: '2025-02-08 14:20:33',
      durationTime: 10,
      status: '自动恢复',
      location: 'C区-3号楼-2层',
      manufacturer: '中山睿数信息技术有限公司',
      productionDate: '2023-01-20',
      deviceStatus: '运行中',
      lastMaintenance: '2025-04-05',
      maintainer: '王五',
    },
    {
    id: '4',
    deviceNumber: '1920303855352987650',
    deviceType: '433M增强版单灯控制器',
    warnType: '电流不稳定',
    warnLevel: '注意',
    occurTime: '2025-05-14 17:39:33',
    durationTime: 3,
    status: '自动恢复',
    location: 'C区-3号楼-2层',
    manufacturer: '中山睿数信息技术有限公司',
    productionDate: '2023-01-20',
    deviceStatus: '运行中',
    lastMaintenance: '2025-05-14 17:42:57',
    maintainer: '胡克',
  },
  ];

  // 故障历史数据
  const faultHistories = {
    'LJ-001': [
      {
        warnType: '过温预警',
        warnLevel: '紧急',
        occurTime: '2025-04-21 16:30:26',
        durationTime: 20,
        status: '已忽略',
      },
      {
        warnType: '通信异常',
        warnLevel: '警告',
        occurTime: '2025-03-10 10:15:30',
        durationTime: 30,
        status: '已修复',
      },
    ],
    'LJ-002': [
      {
        warnType: '通信中断',
        warnLevel: '警告',
        occurTime: '2025-03-15 09:12:45',
        durationTime: 45,
        status: '已修复',
      },
    ],
    'LJ-003': [
      {
        warnType: '电压异常',
        warnLevel: '注意',
        occurTime: '2025-02-08 14:20:33',
        durationTime: 10,
        status: '自动恢复',
      },
      {
        warnType: '信号弱',
        warnLevel: '注意',
        occurTime: '2025-01-15 08:10:22',
        durationTime: 15,
        status: '自动恢复',
      },
    ],
  };

  // ---------------------------- 表格列 ----------------------------
  const columns = ref([
    { title: '设备编号', dataIndex: 'deviceNumber', ellipsis: true },
    { title: '设备类型', dataIndex: 'deviceType', ellipsis: true },
    { title: '预警类型', dataIndex: 'warnType', ellipsis: true },
    { title: '预警级别', dataIndex: 'warnLevel', ellipsis: true },
    { title: '发生时间', dataIndex: 'occurTime', ellipsis: true },
    { title: '持续时间（分钟）', dataIndex: 'durationTime', ellipsis: true },
    { title: '状态', dataIndex: 'status', ellipsis: true },
    { title: '操作', dataIndex: 'action', ellipsis: true },
  ]);

  // ---------------------------- 数据状态 ----------------------------
  const tableLoading = ref(false);
  const tableData = ref([...devices]);
  const detailVisible = ref(false);
  const currentDevice = ref({});
  const faultHistory = ref([]);

  // ---------------------------- 工具方法 ----------------------------
  const getWarnLevelColor = (level) => {
    const colors = { 紧急: 'red', 警告: 'orange', 注意: 'green' };
    return colors[level] || 'blue';
  };

  const getTimelineColor = (level) => {
    const colors = { 紧急: 'red', 警告: 'orange', 注意: 'green' };
    return colors[level] || 'blue';
  };

  // ---------------------------- 详情抽屉 ----------------------------
  const showDetail = (record) => {
    currentDevice.value = devices.find((d) => d.deviceNumber === record.deviceNumber) || {};
    faultHistory.value = faultHistories[record.deviceNumber] || [];
    detailVisible.value = true;
  };

  const onDetailClose = () => {
    detailVisible.value = false;
  };

  // ---------------------------- 查询方法 ----------------------------
  const resetQuery = () => {
    console.log('重置');
  };

  const onSearch = () => {
    console.log('查询');
  };

  const queryData = async () => {
    tableLoading.value = true;
    try {
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 500));
      tableData.value = [...devices];
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  };

  // ---------------------------- 其他方法 ----------------------------
  const formRef = ref();
  const showForm = (data) => {
    formRef.value.show(data);
  };
</script>

<style scoped>
  .drawer-footer {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 100%;
    border-top: 1px solid #e9e9e9;
    padding: 10px 16px;
    background: #fff;
    text-align: right;
    z-index: 1;
  }
</style>
