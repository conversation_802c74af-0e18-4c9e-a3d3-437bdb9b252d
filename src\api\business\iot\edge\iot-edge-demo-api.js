/**
 * 网关 api 封装
 *
 * @Author:    潘茜茜
 * @Date:      2025-03-27 22:35:25
 * @Copyright  中山睿数信息技术有限公司 2025
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const iotEdgeDemoApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  list : (data) => {
    return postRequest('/gateway/list', data);
  },
  /**
   *排序  <AUTHOR>
   */
  sort : (data) => {
    return postRequest('/gateway/sort', data);
  },
  /**
   *禁用  <AUTHOR>
   */
   disabled : (data) => {
    return postRequest('/gateway/disabled', data);
  },
  /**
   * 新增  <AUTHOR>
   */
  save : (data) => {
    return postRequest('/gateway/save', data);
  },
  /**
   * 修改  <AUTHOR>
   */
  update : (data) => {
    return postRequest('/gateway/update', data);
  },
  /**
   * 删除  <AUTHOR>
   */
  remove : (data) => {
    return postRequest('/gateway/remove', data);
  },

};
