/*
 * 物联网平台
 *
 * @Author:    谢志豪
 * @Date:      2025-05-05 20:54:50
 */
import { defineStore } from 'pinia';
import { iotDeviceApi } from '/@/api/business/iot/device/iot-device-api';

export const useIotStore = defineStore({
  id: 'iot',
  state: () => ({
    iotToken: null
  }),

  actions: {
    async getIotToken() {
      if (this.iotToken) {
        return this.getIotToken;
      }
      const res =  await iotDeviceApi.getIotToken();
      this.iotToken = res.data;
    },
  },
});
