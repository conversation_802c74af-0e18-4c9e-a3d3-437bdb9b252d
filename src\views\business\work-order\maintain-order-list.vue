<!--
  * 工单
  *
  * @Author:    yourName
  * @Date:      2025-04-11 11:45:56
  * @Copyright  bdic
-->
<template>
    <!---------- 查询表单form begin ----------->
    <a-form class="smart-query-form">
        <a-row class="smart-query-form-row">
            <a-form-item label="工单编号" class="smart-query-form-item">
                <a-select 
                v-model:value="queryForm.order_number"
                placeholder="工单编号"
                :options="orderNumberOptions"
                :allowClear="true"
                style="width: 150px"
                />
            </a-form-item>
            <a-form-item class="smart-query-form-item">
                <a-button type="primary" @click="onSearch">
                    <template #icon>
                        <SearchOutlined />
                    </template>
                    查询
                </a-button>
                <a-button @click="resetQuery" class="smart-margin-left10">
                    <template #icon>
                        <ReloadOutlined />
                    </template>
                    重置
                </a-button>
            </a-form-item>
        </a-row>
    </a-form>
    <!---------- 查询表单form end ----------->

    <a-card size="small" :bordered="false" :hoverable="true">
        <!---------- 表格操作行 begin ----------->
        <a-row class="smart-table-btn-block">
            <div class="smart-table-operate-block">
                <a-button @click="showForm({},0)" type="primary" size="small">
                    <template #icon>
                        <PlusOutlined />
                    </template>
                    新建
                </a-button>
                <a-dropdown>
                    <template #overlay>
                        <a-menu @click="handleMenuClick" class="smart-dropdown-menu">
                            <a-menu-item 
                                v-for="item in Object.values(WORK_ORDER_STATUS_ENUM).filter(item => typeof item !== 'function'&&item.value!==0&&item.value!==1)" 
                                :key="item.value"
                                :data-key="item.value"
                            >
                                {{ item.desc }}
                            </a-menu-item>
                        </a-menu>
                    </template>
                    <a-button>
                        <span>修改工单状态</span>
                        <DownOutlined />
                    </a-button>
                </a-dropdown>
                <a-button @click="confirmBatchDelete" type="primary" danger size="small" :disabled="selectedRowKeyList.length == 0">
                    <template #icon>
                        <DeleteOutlined />
                    </template>
                    批量删除
                </a-button>
            </div>
            <div class="smart-table-setting-block">
                <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
            </div>
        </a-row>
        <!---------- 表格操作行 end ----------->

        <!---------- 表格 begin ----------->
        <a-table
            size="small"
            :dataSource="tableData"
            :columns="columns"
            rowKey="id"
            bordered
            :loading="tableLoading"
            :pagination="false"
            :row-selection="{ selectedRowKeys: selectedRowKeyList, onChange: onSelectChange }"
        >
            <template #bodyCell="{ text, record, column }">
                <!-- 图片部分 -->
                <template v-if="column.dataIndex === 'situationImg'">
                    <div class="image-preview-container">
                        <file-preview :file-list="!_.isEmpty(record.situationImg) ? [record.situationImg[0]] : []" type="picture"/>
                    </div>
                </template>
                <!-- 工单状态 -->
                <template v-if="column.dataIndex==='status'">
                    {{Object.values(WORK_ORDER_STATUS_ENUM,record).find(item=>item.value===Number(record.status)).desc}}
                </template>
                <!-- 派单人 -->
                 <template v-if="column.dataIndex==='createUserId'">
                    {{ employeeMap[Number(record.createUserId)] }}
                 </template>
                <template v-if="column.dataIndex === 'action'">
                    <div class="smart-table-operate">
                        <a-button @click="showForm(record,1)" type="link">编辑</a-button>
                        <a-button @click="onDelete(record)" danger type="link">删除</a-button>
                    </div>
                </template>
            </template>
        </a-table>
        <!---------- 表格 end ----------->

        <div class="smart-query-table-page">
            <a-pagination
                showSizeChanger
                showQuickJumper
                show-less-items
                :pageSizeOptions="PAGE_SIZE_OPTIONS"
                :defaultPageSize="queryForm.pageSize"
                v-model:current="queryForm.pageNum"
                v-model:pageSize="queryForm.pageSize"
                :total="total"
                @change="queryData"
                @showSizeChange="queryData"
                :show-total="(total) => `共${total}条`"
            />
        </div>

        <MaintainOrderAddForm  ref="formAddRef" @reloadList="queryData"/>
        <MaintainOrderEditForm  ref="formEditRef" @reloadList="queryData"/>
    </a-card>
</template>
<script setup>
    import { reactive, ref, onMounted } from 'vue';
    import _ from 'lodash';
    import { message, Modal } from 'ant-design-vue';
    import { SmartLoading } from '/@/components/framework/smart-loading';
    import { maintainOrderApi } from '/@/api/business/work-order/maintain-order-api';
    import { employeeApi } from '/@/api/system/employee-api';
    import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
    import { smartSentry } from '/@/lib/smart-sentry';
    import TableOperator from '/@/components/support/table-operator/index.vue';
    import MaintainOrderAddForm from './maintain-order-add-form.vue';
    import MaintainOrderEditForm from './maintain-order-edit-form.vue'
    import {WORK_ORDER_STATUS_ENUM} from '/@/constants/work-order/maintain-order-const'
    import filePreview from '/@/components/support/file-preview/index.vue';
    // ---------------------------- 表格列 ----------------------------

    const columns = ref([
    {
        title: '工单编号',
        dataIndex: 'orderNumber',
        ellipsis: true,
    },
    {
        title: '设备名称',
        dataIndex: 'deviceName',
        ellipsis: true,
    },
    {
        title: '区域名称',
        dataIndex: 'regionName',
        ellipsis: true,
    },
    
    {
        title: '维修人',
        dataIndex: 'maintainer',
        ellipsis: true,
    },
    {
        title: '维修情况',
        dataIndex: 'faultRemark',
        ellipsis: true,
    },
    {
        title: '维修情况照片',
        dataIndex: 'situationImg',
        align: 'center',
        ellipsis: true,
    },
    {
        title: '维修结束时间',
        dataIndex: 'finishedTime',
        ellipsis: true,
    },
    {
        title: '工单状态',
        dataIndex: 'status',
        ellipsis: true,
    },
    {
        title:'派单人',
        dataIndex: 'createUserId',
        ellipsis: true,
    },
    {
        title: '派单时间',
        dataIndex: 'createTime',
        ellipsis: true,
    },
    {
        title: '操作',
        dataIndex: 'action',
        fixed: 'right',
        width: 90,
    },
    ]);

    // ---------------------------- 查询数据表单和方法 ----------------------------

    const queryFormState = {
        order_number: undefined, //工单编号
        createUserId:undefined,//指派人id
        pageNum: 1,
        pageSize: 10,
    };
    // 查询表单form
    const queryForm = reactive({ ...queryFormState });
    // 表格加载loading
    const tableLoading = ref(false);
    // 表格数据
    const tableData = ref([]);
    // 总数
    const total = ref(0);
    //工单下拉框数组
    const orderNumberOptions = ref([]);

    // 重置查询条件
    function resetQuery() {
        let pageSize = queryForm.pageSize;
        Object.assign(queryForm, queryFormState);
        queryForm.pageSize = pageSize;
        queryData();
    }

    // 搜索
    function onSearch(){
      queryForm.pageNum = 1;
      queryData();
    }

    // 查询数据
    async function queryData() {
        tableLoading.value = true;
        try {
            let queryResult = await maintainOrderApi.queryPage(queryForm);
            tableData.value = queryResult.data.list;
            total.value = queryResult.data.total;
            //查询派单人数据
            orderNumberOptions.value = tableData.value.map(item => ({
            label: item.orderNumber,
            value: item.orderNumber
        }));
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            tableLoading.value = false;
        }
    }
    //查询派单人数据
    const employeeMap = ref({});
    async function queryEmployee() {
    try {
        let queryResult = await employeeApi.queryAll();
        employeeMap.value = queryResult.data.reduce((acc, item) => {
            acc[item.employeeId] = item.actualName; 
            return acc;
        }, {});
    } catch (e) {
        smartSentry.captureError(e);
    }
}
    onMounted(()=>{
        queryData();
        queryEmployee();
    }
    );

    // ---------------------------- 添加/修改 ----------------------------
    //添加模态框
    const formAddRef = ref();
    //修改模态框
    const formEditRef = ref();
    function showForm(data,key) {
        if(key===1){
            formEditRef.value.show(data);
        }else{
            formAddRef.value.show();
        }
    }

    // ---------------------------- 单个删除 ----------------------------
    //确认删除
    function onDelete(data){
        Modal.confirm({
            title: '提示',
            content: '确定要删除选吗?',
            okText: '删除',
            okType: 'danger',
            onOk() {
                requestDelete(data);
            },
            cancelText: '取消',
            onCancel() {},
        });
    }

    //请求删除
    async function requestDelete(data){
        SmartLoading.show();
        try {
            let deleteForm = {
                goodsIdList: selectedRowKeyList.value,
            };
            await maintainOrderApi.delete(data.id);
            message.success('删除成功');
            queryData();
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            SmartLoading.hide();
        }
    }

    // ---------------------------- 批量删除 ----------------------------

    // 选择表格行
    const selectedRowKeyList = ref([]);

    function onSelectChange(selectedRowKeys) {
        selectedRowKeyList.value = selectedRowKeys;
    }
    // 清空选中项
    function clearSelected() {
        selectedRowKeyList.value = [];
    }
    // 新增 priorityStr 定义
    const priorityStr = ref(''); // 初始化为空字符串

    // 请求修改订单优先级
    async function updateOrder(id) {
        try {
            const param = {
                id:id,
                status: priorityStr.value, // 使用已定义的 priorityStr
            };
            await maintainOrderApi.updateStatus(param);
            message.success('修改成功');
            await queryData(); // 调用查询方法刷新数据
        } catch (e) {
            smartSentry.captureError(e);
        }
    }

 // 获得点击的优先级数据
 function handleMenuClick(data) {
        const key = data.key; // 修改为正确的枚举对象
        if (selectedRowKeyList.value.length === 0) {
            message.warning('请先选择修改的数据');
            return;
        }
        priorityStr.value = key; // 更新 priorityStr 值
        selectedRowKeyList.value.forEach(item=>{
            updateOrder(item); // 调用更新方法
        })

        clearSelected(); // 清空选中项
    }
    // 批量删除
    function confirmBatchDelete() {
        Modal.confirm({
            title: '提示',
            content: '确定要批量删除这些数据吗?',
            okText: '删除',
            okType: 'danger',
            onOk() {
                requestBatchDelete();
            },
            cancelText: '取消',
            onCancel() {},
        });
    }

    //请求批量删除
    async function requestBatchDelete() {
        try {
            SmartLoading.show();
            await maintainOrderApi.batchDelete(selectedRowKeyList.value);
            message.success('删除成功');
            queryData();
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            SmartLoading.hide();
        }
    }
</script>
<style scoped>
  .smart-dropdown-menu {
    z-index: 10;
  }
  </style>
