/**
 * 控制地图 api 封装
 *
 * @Author:    李帅兵
 * @Date:      2025-03-22 17:51:27
 * @Copyright  中山睿数信息技术有限公司 2025
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const lightApi = {

  /**
   * 通过灯光编号获取灯光信息 <AUTHOR>
   */
  queryLightInformation : (param) => {
    return postRequest('/light/queryLightInformation', param);
  },
  /**
   * 区域视图 分页查询 <AUTHOR>
   */
  queryRegionViewsPage : (param) => {
    return postRequest('/iotDevice/queryRegionViewsPage', param);
  },
    /**
   * 获取设备详情 <AUTHOR>
   */
    queryDetail : (param) => {
      return postRequest('/iotDevice/queryDetail', param);
    },
  /**
   * 获取近7天的能耗情况 <AUTHOR>
   */
  queryEnergyConsumptionLastWeek : (param) => {
    return postRequest('/iotDevice/queryEnergyConsumptionLastWeek', param);
    },
   /**
   * 修改灯光的开关状态 <AUTHOR>
   */
   SwitchLamp : (param) => {
    return postRequest('/iotDevice/SwitchLamp', param);
    },
       /**
   * 修改灯光的开关状态 <AUTHOR>
   */
     changeLightBrightness : (param) => {
    return postRequest('/iotDevice/changeLightBrightness', param);
    },
    /**
   * 获取指定坐标附近x m的设备 <AUTHOR>
   */
    getDeviceByCoordinate : (param) => {
    return postRequest('/iotDevice/getDeviceByCoordinate', param);
    },
    /**
   * 能耗统计-按天统计
   */
    energyConsumeLastWeek : (param) => {
    return postRequest('/energy/chart/energyConsumeLastWeek', param);
    },
  /**
   * 获取指定坐标附近x KM的摄像头 <AUTHOR>
   */
  getCameraByCoordinate : (param) => {
  return postRequest('/iotDevice/getCameraByCoordinate', param);
  },
    /**
   * 获取指定坐标附近x KM的区域 <AUTHOR>
   */
    getRegionByCoordinate : (param) => {
      return postRequest('/iotRegion/getRegionByCoordinate', param);
      },
          /**
   * 通过区域id获取区域摘要信息   <AUTHOR>
   */
          queryRegionLightSummaryByRegionId : (regionId) => {
      return postRequest(`/light/queryRegionLightSummaryByRegionId/${regionId}`);
      },
   /**
   *区域视图 分页查询 <AUTHOR>
   */
   regionDeviceInfo : (param) => {
      return postRequest(`/light/regionDeviceInfo`, param);
       },
    /**
   *controllerLamp
   */
   controllerLamp : (param) => {
    return postRequest(`/light/controllerLamp`, param);
     },
};
