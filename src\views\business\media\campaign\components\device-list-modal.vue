<!--
  * 设备列表选择模态框
  *
  * @Author:    潘显镇
  * @Date:      2025-05-30 
  * @Copyright  2025 
-->
  
<template>
  <a-modal
    title="选择设备"
    :width="1200"
    :open="visibleFlag"
    @cancel="onClose"
    :maskClosable="false"
    :destroyOnClose="true"
  >
    <!---------- 查询表单form begin ----------->
    <a-form class="smart-query-form">
      <a-row class="smart-query-form-row">
        <a-form-item class="custom-label-form-item smart-query-form-item">
          <template #label>
            <div class="label-container">
              <span>设备名称</span>
              <a-tooltip 
                title="英文字母，并可包含数字、下划线（_）、中划线（-）、点号（.）、半角冒号（:）以及特殊字符@，整体长度需控制在4至32个字符之间" 
                placement="topLeft"
                class="label-tooltip"
              >
                <question-circle-outlined />
              </a-tooltip>
            </div>
          </template>
          <a-input style="width: 155px;" v-model:value="queryForm.deviceName" placeholder="请输入设备名称" />
        </a-form-item>
        <a-form-item class="custom-label-form-item smart-query-form-item">
          <template #label>
            <div class="label-container">
              <span>唯一编号</span>
              <a-tooltip 
                title="设备唯一信息编号，比如mac地址，sn码等，标识设备唯一性" 
                placement="topLeft"
                class="label-tooltip"
              >
                <question-circle-outlined />
              </a-tooltip>
            </div>
          </template>
          <a-input style="width: 155px;" v-model:value="queryForm.uniqueNo" placeholder="请输入唯一编号" />
        </a-form-item>
        <a-form-item class="custom-label-form-item smart-query-form-item">
          <template #label>
            <div class="label-container">
              <span>所属产品</span>
              <a-tooltip 
                title="可选择已发布的产品数据，将设备绑定后继承其物模型" 
                placement="topLeft"
                class="label-tooltip"
              >
                <question-circle-outlined />
              </a-tooltip>
            </div>
          </template>
          <a-input style="width: 155px;" placeholder="请选择所属产品" @click="showProductForm" v-if="queryForm.productName == undefined" />
          <a-input style="width: 155px;" placeholder="请选择所属产品" @click="showProductForm" :value="`${queryForm.productName} : ${queryForm.productKey}`" v-else />
        </a-form-item>
        <!-- <a-form-item label="设备类型" class="smart-query-form-item" v-show="!isCollapsed">
          <SmartEnumSelect width="120px" v-model:value="queryForm.deviceType" placeholder="请选择设备类型" enum-name="DEVICE_TYPE_ENUM" />
        </a-form-item> -->
        <a-form-item label="是否启用" class="smart-query-form-item" v-show="!isCollapsed">
          <SmartEnumSelect width="120px" v-model:value="queryForm.status" placeholder="请选择是否启用" enum-name="STATUS_ENUM" />
        </a-form-item>
        <a-form-item label="设备状态" class="smart-query-form-item" v-show="!isCollapsed">
          <SmartEnumSelect width="120px" v-model:value="queryForm.deviceStatus" placeholder="请选择是否启用" enum-name="DEVICE_STATUS_ENUM" />
        </a-form-item>
        <a-form-item class="smart-query-form-item">
          <a-button type="primary" @click="onSearch">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
          <a-button @click="resetQuery" class="smart-margin-left10">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
          <a-button @click="toggleCollapse" class="smart-margin-left16" style="margin-left: 10px">
            <template #icon>
              <MenuFoldOutlined v-if="!isCollapsed" />
              <MenuUnfoldOutlined v-else />
            </template>
            {{ isCollapsed ? "展开" : "收起" }}
          </a-button>
        </a-form-item>
      </a-row>
    </a-form>

    <!-- 表格替换卡片列表 -->
    <a-table
      :columns="columns"
      :data-source="data"
      :loading="initLoading"
      :pagination="false"
      :row-selection="rowSelection"
      row-key="id"
      bordered
      size="middle"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'deviceName'">
          <a class="device-name">{{ record.deviceName }}</a>
        </template>
        <template v-else-if="column.key === 'deviceType'">
          {{ findDescByValue(record.deviceType, DEVICE_TYPE) }}
        </template>
        <template v-else-if="column.key === 'status'">
          <span v-if="record.status === 1">是</span>
          <span v-else>否</span>
        </template>
        <template v-else-if="column.key === 'deviceStatus'">
          <a-tag v-if="record.deviceStatus === 0" color="orange">未激活</a-tag>
          <a-tag v-if="record.deviceStatus === 1" color="green">在线</a-tag>
          <a-tag v-if="record.deviceStatus === 2" color="red">离线</a-tag>
          <a-tag v-if="record.deviceStatus === 3" color="gray">停用</a-tag>
        </template>
      </template>
    </a-table>
    
    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>

    <chooseProductForm ref="chooseProductFormRef" @emitSelectedProduct="handleProductSelected" />

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button 
          type="primary" 
          @click="onSubmit" 
          :disabled="isSingleSelect ? selectedRowKeyList.length !== 1 : selectedRowKeyList.length === 0"
        >
          {{ confirmButtonText }}
        </a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
import { reactive, ref, nextTick, computed } from 'vue';
import { message } from 'ant-design-vue';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { smartSentry } from '/@/lib/smart-sentry';
import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
import { iotDeviceApi } from '/@/api/business/iot/device/iot-device-api.js';
import chooseProductForm from '../../../iot/device/components/choose-product-form.vue';
import { DEVICE_TYPE } from '/@/constants/business/iot/device/iot-device-const.js';
import SmartEnumSelect from '/@/components/framework/smart-enum-select/index.vue';
import { SearchOutlined, ReloadOutlined, MenuFoldOutlined, MenuUnfoldOutlined, QuestionCircleOutlined } from '@ant-design/icons-vue';

// 接收props
const props = defineProps({
  // 是否为单选模式
  isSingleSelect: {
    type: Boolean,
    default: false
  },
  // 确认按钮文本
  confirmButtonText: {
    type: String,
    default: '确定'
  }
});

// 计算属性：行选择配置
const rowSelection = computed(() => {
  return {
    type: props.isSingleSelect ? 'radio' : 'checkbox',
    selectedRowKeys: selectedRowKeyList.value,
    onChange: handleSelectionChange
  };
});

// ---------------------------- 表格列定义 ----------------------------
const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    width: 50,
    customRender: ({ index }) => index + 1
  },
  {
    title: '设备ID',
    dataIndex: 'id',
    key: 'id',
    width: 150,
  },
  {
    title: '设备名称',
    dataIndex: 'deviceName',
    key: 'deviceName',
    width: 150,
  },
  {
    title: '备注名称',
    dataIndex: 'deviceNoteName',
    key: 'deviceNoteName',
    width: 150,
  },
  {
    title: '所属产品',
    dataIndex: 'productName',
    key: 'productName',
    width: 150,
  },
  {
    title: '设备类型',
    dataIndex: 'deviceType',
    key: 'deviceType',
    width: 100,
  },
  {
    title: '是否启用',
    dataIndex: 'status',
    key: 'status',
    width: 80,
  },
  {
    title: '设备状态',
    dataIndex: 'deviceStatus',
    key: 'deviceStatus',
    width: 100,
  },
  {
    title: '所属区域名称',
    dataIndex: 'regionName',
    key: 'regionName',
    width: 150,
  },
  {
    title: '最后上线时间',
    dataIndex: 'lastOnlineTime',
    key: 'lastOnlineTime',
    width: 150,
  }
];

// ---------------------------- 查询数据表单和方法 ----------------------------
const initLoading = ref(false);
const loading = ref(false);

const queryFormState = {
  deviceName: undefined,
  uniqueNo: undefined,
  productKey: undefined,
  deviceStatus: undefined,
  deviceType: undefined,
  status: undefined,
  productName: undefined,
  pageNum: 1,
  pageSize: 10,
  categoryName: "户外广告屏幕",
  sortItemList: [{ isAsc: false, column: 'create_time' }],
};
// 查询表单form
const queryForm = reactive({ ...queryFormState });
// 表格数据
const data = ref([]);
// 总数
const total = ref(0);
// 重置查询条件
function resetQuery() {
  let pageSize = queryForm.pageSize;
  Object.assign(queryForm, queryFormState);
  queryForm.pageSize = pageSize;
  queryData();
}

// 搜索
function onSearch() {
  queryForm.pageNum = 1;
  queryData();
}

// 查询数据
async function queryData() {
  initLoading.value = true;
  loading.value = true;
  try {
    let queryResult = await iotDeviceApi.queryPage(queryForm);
    total.value = queryResult.data.total;
    data.value = queryResult.data.list;
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    initLoading.value = false;
    loading.value = false;
  }
}

// ---------------------------- 显示与隐藏 ----------------------------
// 是否显示
const visibleFlag = ref(false);

function show() {
  visibleFlag.value = true;
  nextTick(() => {
    queryData();
  });
}

function onClose() {
  visibleFlag.value = false;
}

// ---------------------------- 选择设备 ----------------------------
const chooseProductFormRef = ref();

function showProductForm() {
  chooseProductFormRef.value.show();
}

// 选择表格行
const selectedRowKeyList = ref([]);
const selectedDevices = ref([]);

function handleSelectionChange(selectedRowKeys, selectedRows) {
  selectedRowKeyList.value = selectedRowKeys;
  selectedDevices.value = selectedRows;
}

// 确认选择
function onSubmit() {
  if (props.isSingleSelect) {
    // 单选模式
    if (selectedDevices.value.length !== 1) {
      message.warning('请选择一个设备');
      return;
    }
    emits('selectDevice', selectedDevices.value[0]);
  } else {
    // 多选模式
    if (selectedDevices.value.length === 0) {
      message.warning('请至少选择一个设备');
      return;
    }
    emits('selectDevices', selectedDevices.value);
  }
  onClose();
}

//返回键值
function findDescByValue(value, enumObject) {
  // 遍历枚举对象的键值对
  for (const key in enumObject) {
    if (enumObject[key].value === value) {
      return enumObject[key].desc; 
    }
  }
  return null; 
}

// 收缩，展开
const isCollapsed = ref(true);
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};

// 接受子组件数据
function handleProductSelected(selectedProduct) {
  queryForm.productKey = selectedProduct.productKey;
  queryForm.productName = selectedProduct.productName;
}

// ------------------------ 事件 ------------------------
const emits = defineEmits(['selectDevice', 'selectDevices']);

defineExpose({
  show,
});
</script>

<style scoped lang="less">
.device-name {
  color: #2c77f1;
  font-weight: bold;
}

/* 自定义标签容器 */
.label-container {
  position: relative;
  display: inline-block;
  padding-left: 18px; /* 为图标留出空间 */
}

/* 提示图标定位 */
.label-tooltip {
  position: absolute;
  left: 0;
  top: 0;
}

/* 保持与其他表单项对齐 */
.custom-label-form-item :deep(.ant-form-item-label) {
  line-height: 2.5;
}

/* 图标样式调整 */
.label-tooltip .anticon {
  font-size: 14px;
  color: #1890ff;
  cursor: help;
}

/* 输入框组宽度适配 */
.custom-label-form-item :deep(.ant-input-group) {
  width: 100%;
}
</style>
