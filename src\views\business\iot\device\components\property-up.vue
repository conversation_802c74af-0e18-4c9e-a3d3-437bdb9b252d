<template>
    <!-- 模块选择 -->
    <a-form-item label="模块选择" class="smart-query-form-item">
        <a-select v-model:value="currentBlockId">
            <a-select-option v-for="block in blocks" :key="block.id" :value="block.id">
                {{ block.blockName }}
            </a-select-option>
        </a-select>
    </a-form-item>

    <a-form class="smart-query-form">
        <!-- 属性列表 -->
        <template v-for="property in filteredProperties" :key="property.id">
            <a-form-item :label="`${property.name}(${property.identifier})`" class="smart-query-form-item">
                <a-row>
                    <a-col :span="20">
                        <!-- 数值类型输入 -->
                        <template v-if="['int32', 'float', 'double'].includes(property.specFunction.fieldType)">
                            <a-input-number v-model:value="propertyValues[property.identifier]"
                                :min="Number(property.specFunction.specMin)"
                                :max="Number(property.specFunction.specMax)"
                                :step="property.specFunction.fieldType === 'int32' ? 1 : 0.1"
                                :placeholder="`请输入${property.name} (${property.specFunction.fieldType})`"
                                style="width: 100%" />
                        </template>

                        <!-- 文本类型输入 -->
                        <template v-else-if="property.specFunction.fieldType === 'text'">
                            <a-input v-model:value="propertyValues[property.identifier]"
                                :placeholder="`请输入${property.name}`" :maxLength="property.specFunction.specLength" />
                        </template>

                        <!-- 布尔类型选择 -->
                        <template v-else-if="property.specFunction.fieldType === 'bool'">
                            <a-select v-model:value="propertyValues[property.identifier]"
                                :placeholder="`请选择${property.name}`" allowClear style="width: 100%">
                                <a-select-option :value="1">
                                    {{ property.specFunction.specBoolTrue || '开' }}-1
                                </a-select-option>
                                <a-select-option :value="0">
                                    {{ property.specFunction.specBoolFalse || '关' }}-0
                                </a-select-option>
                            </a-select>
                        </template>

                        <!-- 枚举类型选择 -->
                        <!-- <template v-else-if="property.specFunction.fieldType === 'enum'">
                            <a-select v-model:value="propertyValues[property.identifier]"
                                :placeholder="`请选择${property.name}`" allowClear style="width: 100%">
                                <a-select-option v-for="option in parseEnumOptions(property.specFunction.specEnum)"
                                    :key="option.value" :value="option.value">
                                    {{ option.label }}
                                </a-select-option>
                            </a-select>
                        </template> -->

                        <!-- 数组类型输入 -->
                        <!-- <template v-else-if="property.specFunction.fieldType === 'array'">
                            <a-space direction="vertical" style="width: 100%">
                                <a-input v-for="index in parseInt(property.specFunction.specSize || 1)" :key="index"
                                    v-model:value="getArrayValue(property.identifier, index - 1)"   
                                    :type="getInputType(property.specFunction.specItemType)"
                                    :placeholder="`请输入第${index}个${property.name}(${property.specFunction.specItemType})`" />
                            </a-space>
                        </template> -->
                    </a-col>
                    <a-col :span="4">
                        <a-dropdown placement="bottomRight" :trigger="['hover']">
                            <a-button type="link">调试</a-button>
                            <template #overlay>
                                <a-menu>
                                    <a-menu-item key="1" @click="reportSingle(property)">
                                        <span>{{ props.mode === 'up' ? '上报' : '下发' }}</span>
                                    </a-menu-item>
                                </a-menu>
                            </template>
                        </a-dropdown>
                    </a-col>
                </a-row>
            </a-form-item>
        </template>

        <!-- 操作按钮 -->
        <a-form-item class="smart-query-form-item">
            <a-button type="primary" @click="reportAll" class="smart-margin-left10">发送指令</a-button>
            <a-button @click="resetProperties" class="smart-margin-left10">重置属性</a-button>
        </a-form-item>
    </a-form>
</template>

<script setup>
import { ref, computed, defineEmits, defineProps, onMounted, watch } from 'vue';
import { message } from 'ant-design-vue';

// Props 定义
const props = defineProps({
    modelValue: {
        type: Object,
        default: () => ({})
    },
    mode: {
        type: String,
        default: 'up'
    }
});

// Emits 定义
const emit = defineEmits(['report']);

// 响应式数据
const blocks = ref([]); // 模块列表
const properties = ref([]); // 属性列表
const currentBlockId = ref(''); // 当前选中的模块ID
const propertyValues = ref({}); // 属性值对象

// 侦听器
watch(() => props.modelValue, (newValue) => {
    blocks.value = newValue.blocks;
    properties.value = newValue.properties;
    currentBlockId.value = newValue.currentBlockId;
    console.log(newValue)
}, {
    immediate: true
});

// 计算属性
const filteredProperties = computed(() => {
    console.log(properties.value)
    return properties.value?.filter(item => item.blockId === currentBlockId.value);
});



// 业务方法 - 上报相关
const reportSingle = (property) => {
    const value = propertyValues.value[property.identifier];
    if (!value && value !== 0) {
        message.warning(`请输入${property.name}的值`);
        return;
    }
    const reportData = {
        type: 'single',
        identifier: property.identifier,
        value: value,
        blockId: currentBlockId.value,
        blockName: blocks.value.find(b => b.id === currentBlockId.value)?.blockName || '未知模块'
    };

    emit('report', reportData);
    message.success('上报成功');
};

const reportAll = () => {
    const validProperties = {};
    let hasValidData = false;

    for (const property of filteredProperties.value) {
        const value = propertyValues.value[property.identifier];
        if (value || value === 0) {
            validProperties[property.identifier] = value;
            hasValidData = true;
        }
    }

    if (!hasValidData) {
        message.warning('请至少输入一个属性值');
        return;
    }

    const reportData = {
        type: 'batch',
        properties: validProperties,
        blockId: currentBlockId.value,
        blockName: blocks.value.find(b => b.id === currentBlockId.value)?.blockName || '未知模块'
    };

    emit('report', reportData);
    message.success('上报成功');
};

// 业务方法 - 工具方法
const resetProperties = () => {
    const newValues = {};
    properties.value.forEach(item => {
        newValues[item.identifier] = null;
    });
    propertyValues.value = newValues;
    message.success('已重置所有属性值');
};


/* 暂时注释的方法，以后可能会用到
// 工具方法
const parseEnumOptions = (enumStr) => {
    try {
        return JSON.parse(enumStr || '[]');
    } catch {
        return [];
    }
};

const getInputType = (itemType) => {
    switch (itemType) {
        case 'int32':
        case 'float':
        case 'double':
            return 'number';
        default:
            return 'text';
    }
};

const getArrayValue = (identifier, index) => {
    if (!propertyValues.value[identifier]) {
        propertyValues.value[identifier] = [];
    }
    return propertyValues.value[identifier][index];
};
*/
</script>

<style scoped></style>