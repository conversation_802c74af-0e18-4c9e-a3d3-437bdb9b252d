<template>
  <div class="map-right-graph-container">
    <div class="map-right-title">
      <span>设备列表</span>
    </div>
    <div class="device-list">
      <a-table
        :columns="columns"
        :data-source="sortedDeviceList"
        :pagination="false"
        size="small"
        :scroll="{ y: 500 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'deviceStatus'">
            <a-tag :color="getStatusColor(record.deviceStatus)">
              {{ getStatusText(record.deviceStatus) }}
            </a-tag>
          </template>
        </template>
      </a-table>
    </div>
    
    <!-- 添加产品类型分布饼图 -->
    <div class="map-right-title" style="margin-top: 10px;">
      <span>按区域内产品品类分</span>
    </div>
    <div ref="pieContainer" class="pie-container"></div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, watch, nextTick, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  deviceList: {
    type: Array,
    default: () => []
  }
});

// 饼图容器引用
const pieContainer = ref(null);
let pieChart = null;

// 表格列定义
const columns = [
  {
    title: '设备名称',
    dataIndex: 'deviceName',
    key: 'deviceName',
    ellipsis: true,
    width: 120
  },
  {
    title: '状态',
    dataIndex: 'deviceStatus',
    key: 'deviceStatus',
    width: 70
  },
  {
    title: '产品类型',
    dataIndex: 'productName',
    key: 'productName',
    ellipsis: true,
    width: 110
  }
];

// 按活跃时间排序的设备列表
const sortedDeviceList = computed(() => {
  if (!props.deviceList || props.deviceList.length === 0) {
    return [];
  }
  
  return [...props.deviceList]
    .filter(device => device.deviceName) // 过滤掉没有名称的设备
    .sort((adevice, bdevice) => {
      // 按活跃时间降序排序
      if (adevice.activeTime && bdevice.activeTime) {
        return new Date(bdevice.activeTime) - new Date(adevice.activeTime);
      } else if (adevice.activeTime) {
        return -1;
      } else if (bdevice.activeTime) {
        return 1;
      }
      return 0;
    })
    .map((device, index) => ({
      ...device,
      key: device.id || index.toString(),
      productName: device.productName // 使用产品名称作为类别名称
    }));
});

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 1:
      return '在线';
    case 0:
      return '离线';
    case 2:
      return '故障';
    default:
      return '未知';
  }
};

// 获取状态颜色
const getStatusColor = (status) => {
  switch (status) {
    case 1:
      return 'green';
    case 0:
      return 'gray';
    case 2:
      return 'red';
    default:
      return 'orange';
  }
};

// 计算产品类型分布数据
const calculateProductDistribution = () => {
  const productCount = {};
  
  if (!props.deviceList || props.deviceList.length === 0) {
    return [];
  }
  
  props.deviceList.forEach(device => {
    if (device.productName) {
      productCount[device.productName] = (productCount[device.productName] || 0) + 1;
    }
  });
  
  // 转换为echarts所需的数据格式
  return Object.keys(productCount).map(name => ({
    name,
    value: productCount[name]
  }));
};

// 初始化饼图
const initPieChart = () => {
  if (!pieContainer.value) return;
  
  const productData = calculateProductDistribution();
  
  pieChart = echarts.init(pieContainer.value);
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: 0,
      left: 'center',
      itemWidth: 10,
      itemHeight: 10,
      textStyle: {
        fontSize: 10
      }
    },
    series: [
      {
        name: '产品类型',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '40%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 4,
          borderColor: '#fff',
          borderWidth: 1
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 12,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: productData
      }
    ]
  };
  
  pieChart.setOption(option);
};

// 更新饼图
const updatePieChart = () => {
  if (!pieChart) {
    initPieChart();
    return;
  }
  
  const productData = calculateProductDistribution();
  
  pieChart.setOption({
    series: [
      {
        data: productData
      }
    ]
  });
};

// 响应窗口大小变化
const handleResize = () => {
  if (pieChart) pieChart.resize();
};

// 监听设备列表变化，更新饼图
watch(() => props.deviceList, () => {
  nextTick(() => {
    updatePieChart();
  });
}, { deep: true, immediate: true });

onMounted(() => {
  nextTick(() => {
    initPieChart();
  });
  window.addEventListener('resize', handleResize);
});

// 组件卸载时移除事件监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
  if (pieChart) {
    pieChart.dispose();
    pieChart = null;
  }
});
</script>

<style scoped>
.map-right-graph-container {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 300px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  padding: 8px;
  display: flex;
  flex-direction: column;
  opacity: 0.95;
  z-index: 90;
}

.map-right-title {
  font-weight: bold;
  font-size: 14px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 8px;
}

.device-list {
  width: 100%;
  height:50%;
}

.pie-container {
  height: 160px;
  width: 100%;
}

:deep(.ant-table-thead > tr > th) {
  padding: 8px 8px;
  font-size: 13px;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 4px 8px;
  font-size: 12px;
}

:deep(.ant-tag) {
  margin-right: 0;
}
</style>
