export const WEEK_DAY_ENUM = {
    MONDAY: { value: 1, label: '星期一' },
    TUESDAY: { value: 2, label: '星期二' },
    WEDNESDAY: { value: 3, label: '星期三' },
    THURSDAY: { value: 4, label: '星期四' },
    FRIDAY: { value: 5, label: '星期五' },
    SATURDAY: { value: 6, label: '星期六' },
    SUNDAY: { value: 7, label: '星期日' },

    getOptions() {
        return Object.values(this).reduce((options, item) => {
            // 过滤掉函数，并检查是否有 value 和 label 属性
            if (item && typeof item === 'object' && 'value' in item && 'label' in item) {
                options.push({ value: item.value, label: item.label });
            }
            return options;
        }, []);
    },
};