/**
 * 设备表 api 封装
 *
 * @Author:    李帅兵
 * @Date:      2025-03-21 22:21:07
 * @Copyright  中山睿数信息技术有限公司 2025
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const iotDeviceApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/iotDevice/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/iotDevice/add', param);
  },
  /**
   * 增加  <AUTHOR>
   */
  submit: (param) => {
      return postRequest('/iotDevice/submit', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/iotDevice/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/iotDevice/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
      return postRequest('/iotDevice/batchDelete', idList);
  },
  

  // 获取随机设备名称
  getRandomName: () => {
      return getRequest('/iotDevice/getRandomDeviceName');
  },


  // 获取随机备注名称
  getRandomRemark: () => {
      return getRequest('/iotDevice/getRandomRemarkName');
  },

  // 获取随机唯一编号
  getRandomCode: () => {
      return getRequest('/iotDevice/getRandomUniqueCode');
  },
  // 获取云端日志
  getCloudLog: (param) => {
    return postRequest('/iotDevice/log/message', param);
  },
  // 获取设备详情
  queryDetail: (param) =>{
    return postRequest('/iotDevice/queryDetail',param)
  },
  // 根据位置获取设备
  getDeviceByCoordinate: (param)=>{
    return postRequest('/iotDevice/getDeviceByCoordinate',param)
  },
  queryList: () => {
    return getRequest('/iotDevice/getAllDevice');
  },

  //根据设备名获取设备详情
  queryDetailByName: (deviceName) => {
    return getRequest(`/iotDevice/getDeviceCode?deviceName=${deviceName}`);
  },

  //获取改网关下的子设备
  getSubDevice: (deviceId) => {
    return getRequest(`/iotDevice/getSubsetDevice?deviceId=${deviceId}`);
  },

  //获取没有网关的设备
  getNoGatewayDevice: () => {
    return getRequest(`/iotDevice/getUnbindSubsetDevice`);
  },

  //设置设备为子集
  setSubDevice: (param) => {
    return postRequest('/iotDevice/setSubsetDevice', param);
  },
  //获取所有设备
  getAllDevice: () =>{
    return getRequest('/iotDevice/getAllDevice');
  },
  //查询设备的Topic列表
  getTopicList: (param)=>{
    return postRequest('/iotDevice/topic', param);
  },
  //获取设备物模型列表
  getTslDevice:(param)=>{
    return postRequest('/iotDevice/product/version', param);
  },
  //获取设备mqtt连接参数
  getMqttLinks: (deviceId)=>{
    return getRequest(`/iotDevice/mqtt/links/${deviceId}`);
  },
    //通过灯光编号获取灯光信息  
    queryLightInformation: (param)=>{
      return postRequest('/light/queryLightInformation', param);
    },
        //设置设备坐标
        setCoordinate: (param)=>{
          return postRequest('/iotDevice/setCoordinate', param);
        },
    //修改设备的备注名
    updateNoteName: (param)=>{
        return postRequest('/iotDevice/updateNoteName', param);
    },
    /**
     * 获取物联网token
     */
    getIotToken: () =>{
        return getRequest('/iot/getToken');
    },
  /**
   * 设备详情
   */
  getOne: (param) =>{
    return postRequest('/iotDevice/queryOne',param);
  },
      /**
     * 通过设备id获取所在区域的信息 <AUTHOR>
     */
      getRegionByDevice: (deviceId) =>{
        return getRequest(`/iotRegion/getRegionByDevice/${deviceId}`);
    },
    /**
     * 分页查询区域下设备信息
     */
    queryDeviceByRegion: (param) =>{
      return postRequest('/iotDevice/queryDeviceByRegion', param);
    },
    /**
     * 新增区域信息 <AUTHOR>
     */
    addRegion: (param) =>{
      return postRequest('/iotRegion/add', param);
    },
};
