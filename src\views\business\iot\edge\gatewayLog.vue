<template>
    <a-card>
      <!-- 顶部搜索区域 -->
      <div class="search-container">
        <a-form layout="inline">
          <a-form-item label="网关名字">
            <a-input 
              v-model:value="searchParams.gatewayName" 
              placeholder="请输入网关名字"
              @pressEnter="handleSearch"
            />
          </a-form-item>
          <a-form-item label="网关级别">
            <a-select
              v-model:value="searchParams.gatewayLevel"
              style="width: 150px"
              placeholder="请选择网关级别"
              @change="handleSearch"
            >
              <a-select-option value="">全部</a-select-option>
              <a-select-option value="1">一级</a-select-option>
              <a-select-option value="2">二级</a-select-option>
              <a-select-option value="3">三级</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="信息检索">
            <a-input 
              v-model:value="searchParams.infoSearch" 
              placeholder="请输入检索信息"
              @pressEnter="handleSearch"
            />
          </a-form-item>
          <a-form-item label="时间范围">
            <a-date-picker
              v-model:value="searchParams.startTime"
              format="YYYY-MM-DD"
              placeholder="开始时间"
              @change="handleSearch"
            />
            <span style="margin: 0 8px">-</span>
            <a-date-picker
              v-model:value="searchParams.endTime"
              format="YYYY-MM-DD"
              placeholder="结束时间"
              @change="handleSearch"
            />
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="handleSearch">搜索</a-button>
            <a-button style="margin-left: 8px" @click="resetSearch">重置</a-button>
          </a-form-item>
        </a-form>
      </div>
  
      <!-- 底部表格区域 -->
      <div class="table-container">
        <a-table
          :columns="columns"
          :data-source="filteredData"
          :pagination="pagination"
          row-key="id"
          bordered
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'gatewayLevel'">
              <a-tag :color="getLevelColor(record.gatewayLevel)">
                {{ getLevelText(record.gatewayLevel) }}
              </a-tag>
            </template>
            <template v-else-if="column.dataIndex === 'createTime'">
              {{ formatDate(record.createTime) }}
            </template>
            <template v-else-if="shouldHighlight(column.dataIndex)">
              <span v-html="highlightText(record[column.dataIndex], column.dataIndex)"></span>
            </template>
            <template v-else>
              {{ record[column.dataIndex] }}
            </template>
          </template>
        </a-table>
      </div>
    </a-card>
  </template>
  
  <script setup>
  import { ref, computed } from 'vue';
  import dayjs from 'dayjs';
  
  // 表头配置（调整了列顺序，时间放在ID后面）
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: "10%",
      align: 'center'
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: "15%",
    },
    {
      title: '网关名字',
      dataIndex: 'gatewayName',
      key: 'gatewayName',
      width: "12%",
    },
    {
      title: '网关级别',
      dataIndex: 'gatewayLevel',
      key: 'gatewayLevel',
      width: "8%",
    },
    {
      title: '网关日志信息',
      dataIndex: 'gatewayInfo',
      key: 'gatewayInfo',
      ellipsis: true,
    }
  ];
  
  // 写死的数据（保持不变）
  const rawData = [
    {
      id: 1,
      gatewayName: '北京核心网关',
      gatewayLevel: '1',
      gatewayInfo: '负责北京地区核心业务流量转发',
      createTime: '2023-01-15 10:30:00'
    },
    {
      id: 2,
      gatewayName: '上海边缘网关',
      gatewayLevel: '2',
      gatewayInfo: '上海地区边缘节点接入网关',
      createTime: '2023-02-20 14:15:00'
    },
    {
      id: 3,
      gatewayName: '广州测试网关',
      gatewayLevel: '3',
      gatewayInfo: '广州地区测试环境专用网关',
      createTime: '2023-03-10 09:00:00'
    },
    {
      id: 4,
      gatewayName: '深圳备份网关',
      gatewayLevel: '2',
      gatewayInfo: '深圳地区灾备环境网关',
      createTime: '2023-04-05 16:45:00'
    },
    {
      id: 5,
      gatewayName: '杭州业务网关',
      gatewayLevel: '1',
      gatewayInfo: '杭州地区主要业务网关',
      createTime: '2023-05-12 11:20:00'
    },
  ];
  
  // 搜索参数（新增时间范围筛选参数）
  const searchParams = ref({
    gatewayName: '',
    gatewayLevel: '',
    infoSearch: '',
    startTime: null,
    endTime: null
  });
  
  // 分页配置（保持不变）
  const pagination = ref({
    current: 1,
    pageSize: 10,
    total: rawData.length,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: total => `共 ${total} 条数据`
  });
  
  // 判断是否需要高亮的列（保持不变）
  const shouldHighlight = (dataIndex) => {
    return ['gatewayName', 'gatewayInfo'].includes(dataIndex);
  };
  
  // 高亮文本方法（保持不变）
  const highlightText = (text, field) => {
    if (!text) return text;
    
    let searchText = '';
    if (field === 'gatewayName') searchText = searchParams.value.gatewayName;
    if (field === 'gatewayInfo') searchText = searchParams.value.infoSearch;
    
    if (!searchText) return text;
    
    // 转义特殊字符
    const escapedSearchText = searchText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const regex = new RegExp(escapedSearchText, 'gi');
    return text.replace(regex, match => `<span class="highlight">${match}</span>`);
  };
  
  // 过滤数据（新增时间范围筛选逻辑）
  const filteredData = computed(() => {
    return rawData.filter(item => {
      const nameMatch = item.gatewayName.includes(searchParams.value.gatewayName);
      const levelMatch = searchParams.value.gatewayLevel === '' || 
                        item.gatewayLevel === searchParams.value.gatewayLevel;
      const infoMatch = item.gatewayInfo.includes(searchParams.value.infoSearch);
      const timeMatch = (!searchParams.value.startTime || dayjs(item.createTime).isAfter(dayjs(searchParams.value.startTime).subtract(1, 'day'))) &&
                        (!searchParams.value.endTime || dayjs(item.createTime).isBefore(dayjs(searchParams.value.endTime).add(1, 'day')));
      
      return nameMatch && levelMatch && infoMatch && timeMatch;
    });
  });
  
  // 搜索方法（保持不变）
  const handleSearch = () => {
    pagination.value.current = 1;
  };
  
  // 重置搜索（新增时间范围筛选参数的重置）
  const resetSearch = () => {
    searchParams.value = {
      gatewayName: '',
      gatewayLevel: '',
      infoSearch: '',
      startTime: null,
      endTime: null
    };
    handleSearch();
  };
  
  // 网关级别文本转换（保持不变）
  const getLevelText = (level) => {
    const levelMap = {
      '1': '一级',
      '2': '二级',
      '3': '三级'
    };
    return levelMap[level] || level;
  };
  
  // 网关级别颜色（保持不变）
  const getLevelColor = (level) => {
    const colorMap = {
      '1': 'red',     // 一级用红色
      '2': 'orange',  // 二级用橙色
      '3': 'green'    // 三级用绿色
    };
    return colorMap[level] || 'blue';
  };
  
  // 日期格式化（保持不变）
  const formatDate = (dateStr) => {
    return dayjs(dateStr).format('YYYY-MM-DD HH:mm:ss');
  };
  </script>
  
  <style scoped>
  .search-container {
    margin-bottom: 20px;
  }
  .table-container {
    margin-top: 20px;
  }
  .highlight {
    color: #ff4d4f;
    font-weight: bold;
    background-color: #fff2f0;
    padding: 0 2px;
    border-radius: 2px;
  }
  
  /* 自定义标签样式（保持不变） */
  :deep(.ant-tag-red) {
    background-color: #fff1f0;
    border-color: #ffa39e;
    color: #f5222d;
  }
  :deep(.ant-tag-orange) {
    background-color: #fff7e6;
    border-color: #ffd591;
    color: #fa8c16;
  }
  :deep(.ant-tag-green) {
    background-color: #f6ffed;
    border-color: #b7eb8f;
    color: #52c41a;
  }
  </style>