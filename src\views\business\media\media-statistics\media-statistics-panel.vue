<template>
  <div class="media-statistics-container">
    <a-row :gutter="16">
      <a-col :span="24">
        <a-card class="statistics-card" title="数据总览">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-statistic title="素材总数" :value="statisticsData.totalMaterials" :precision="0">
                <template #suffix>
                  <span>个</span>
                </template>
              </a-statistic>
            </a-col>
            <a-col :span="6">
              <a-statistic title="投放计划总数" :value="statisticsData.totalCampaigns" :precision="0">
                <template #suffix>
                  <span>个</span>
                </template>
              </a-statistic>
            </a-col>
            <a-col :span="6">
              <a-statistic title="覆盖设备数" :value="statisticsData.totalDevices" :precision="0">
                <template #suffix>
                  <span>台</span>
                </template>
              </a-statistic>
            </a-col>
            <a-col :span="6">
              <a-statistic title="今日播放次数" :value="statisticsData.todayPlayCount" :precision="0">
                <template #suffix>
                  <span>次</span>
                </template>
              </a-statistic>
            </a-col>
          </a-row>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="16" style="margin-top: 16px">
      <!-- 完播率统计 -->
      <a-col :span="12">
        <a-card class="statistics-card" title="素材完播率统计">
          <div ref="completionRateChartRef" style="height: 300px"></div>
        </a-card>
      </a-col>
      <!-- 素材类型分布 -->
      <a-col :span="12">
        <a-card class="statistics-card" title="素材类型分布">
          <div ref="materialTypeChartRef" style="height: 300px"></div>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="16" style="margin-top: 16px">
      <!-- 素材使用排行 -->
      <a-col :span="12">
        <a-card class="statistics-card" title="素材使用排行TOP10">
          <div ref="materialRankChartRef" style="height: 300px"></div>
        </a-card>
      </a-col>
      <!-- 广告效果趋势 -->
      <a-col :span="12">
        <a-card class="statistics-card" title="广告效果趋势">
          <div ref="adEffectChartRef" style="height: 300px"></div>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="16" style="margin-top: 16px">
      <!-- 设备在线状态 -->
      <a-col :span="12">
        <a-card class="statistics-card" title="设备在线状态">
          <div ref="deviceStatusChartRef" style="height: 300px"></div>
        </a-card>
      </a-col>
      <!-- 播放时段分布 -->
      <a-col :span="12">
        <a-card class="statistics-card" title="播放时段分布">
          <div ref="playTimeDistributionRef" style="height: 300px"></div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, onUnmounted } from 'vue';
import * as echarts from 'echarts';
import { SmartLoading } from '/@/components/framework/smart-loading';
// import { mediaStatisticsApi } from '/@/api/business/media/media-statistics/media-statistics-api';
import { smartSentry } from '/@/lib/smart-sentry';

// 图表引用
const completionRateChartRef = ref(null);
const materialTypeChartRef = ref(null);
const materialRankChartRef = ref(null);
const adEffectChartRef = ref(null);
const deviceStatusChartRef = ref(null);
const playTimeDistributionRef = ref(null);

// 统计数据
const statisticsData = reactive({
  totalMaterials: 0,
  totalCampaigns: 0,
  totalDevices: 0,
  todayPlayCount: 0,
});

// 初始化图表
const initCharts = () => {
  initCompletionRateChart();
  initMaterialTypeChart();
  initMaterialRankChart();
  initAdEffectChart();
  initDeviceStatusChart();
  initPlayTimeDistribution();

  // 窗口大小变化时重新调整图表大小
  window.addEventListener('resize', handleResize);
};

// 处理窗口大小变化
const handleResize = () => {
  const charts = [
    completionRateChartRef.value && echarts.getInstanceByDom(completionRateChartRef.value),
    materialTypeChartRef.value && echarts.getInstanceByDom(materialTypeChartRef.value),
    materialRankChartRef.value && echarts.getInstanceByDom(materialRankChartRef.value),
    adEffectChartRef.value && echarts.getInstanceByDom(adEffectChartRef.value),
    deviceStatusChartRef.value && echarts.getInstanceByDom(deviceStatusChartRef.value),
    playTimeDistributionRef.value && echarts.getInstanceByDom(playTimeDistributionRef.value)
  ];
  
  charts.forEach(chart => chart && chart.resize());
};

// 初始化完播率图表
const initCompletionRateChart = () => {
  const chart = echarts.init(completionRateChartRef.value);
  chart.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    legend: {
      data: ['完播率', '观看人次']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['素材A', '素材B', '素材C', '素材D', '素材E', '素材F', '素材G']
    },
    yAxis: [
      {
        type: 'value',
        name: '完播率',
        min: 0,
        max: 100,
        axisLabel: { formatter: '{value}%' }
      },
      {
        type: 'value',
        name: '观看人次',
        axisLabel: { formatter: '{value}次' }
      }
    ],
    series: [
      {
        name: '完播率',
        type: 'bar',
        data: [95.2, 86.5, 92.1, 78.3, 89.7, 91.2, 85.6]
      },
      {
        name: '观看人次',
        type: 'line',
        yAxisIndex: 1,
        data: [1200, 980, 1500, 850, 1300, 1750, 1100]
      }
    ]
  });
};

// 初始化素材类型分布图表
const initMaterialTypeChart = () => {
  const chart = echarts.init(materialTypeChartRef.value);
  chart.setOption({
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      data: ['视频', '图片']//文本云盘
    },
    series: [
      {
        name: '素材类型',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 55, name: '视频' },
          { value: 45, name: '图片' }//文本
        ]
      }
    ]
  });
};

// 初始化素材使用排行图表
const initMaterialRankChart = () => {
  const chart = echarts.init(materialRankChartRef.value);
  chart.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      boundaryGap: [0, 0.01]
    },
    yAxis: {
      type: 'category',
      data: ['素材G', '素材F', '素材E', '素材D', '素材C', '素材B', '素材A'],
      inverse: true
    },
    series: [
      {
        name: '使用次数',
        type: 'bar',
        data: [120, 180, 240, 320, 450, 580, 720],
        itemStyle: {
          color: function(params) {
            const colorList = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452'];
            return colorList[params.dataIndex % colorList.length];
          }
        }
      }
    ]
  });
};

// 初始化广告效果趋势图表
const initAdEffectChart = () => {
  const chart = echarts.init(adEffectChartRef.value);
  chart.setOption({
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['曝光量', '点击量', '转化率']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: [
      {
        type: 'value',
        name: '数量',
        position: 'left'
      },
      {
        type: 'value',
        name: '转化率',
        position: 'right',
        axisLabel: {
          formatter: '{value}%'
        }
      }
    ],
    series: [
      {
        name: '曝光量',
        type: 'line',
        data: [3200, 4500, 5800, 6300, 7100, 8000, 6500],
        smooth: true
      },
      {
        name: '点击量',
        type: 'line',
        data: [320, 450, 580, 630, 710, 800, 650],
        smooth: true
      },
      {
        name: '转化率',
        type: 'line',
        yAxisIndex: 1,
        data: [10, 10, 10, 10, 10, 10, 10],
        smooth: true
      }
    ]
  });
};

// 初始化设备在线状态图表
const initDeviceStatusChart = () => {
  const chart = echarts.init(deviceStatusChartRef.value);
  chart.setOption({
    tooltip: {
      trigger: 'item'
    },
    legend: {
      top: '5%',
      left: 'center'
    },
    series: [
      {
        name: '设备状态',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 85, name: '在线' },
          { value: 10, name: '离线' },
          { value: 5, name: '故障' }
        ]
      }
    ]
  });
};

// 初始化播放时段分布图表
const initPlayTimeDistribution = () => {
  const chart = echarts.init(playTimeDistributionRef.value);
  chart.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['0-3', '3-6', '6-9', '9-12', '12-15', '15-18', '18-21', '21-24']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '播放次数',
        type: 'bar',
        data: [120, 80, 580, 780, 690, 850, 920, 450],
        itemStyle: {
          color: function(params) {
            const colorList = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4'];
            return colorList[params.dataIndex % colorList.length];
          }
        }
      }
    ]
  });
};

// 获取统计数据
const fetchStatisticsData = async () => {
  try {
    SmartLoading.show();
    // 模拟数据
    statisticsData.totalMaterials = 256;
    statisticsData.totalCampaigns = 68;
    statisticsData.totalDevices = 125;
    statisticsData.todayPlayCount = 3582;
  } catch (error) {
    smartSentry.captureError(error);
  } finally {
    SmartLoading.hide();
  }
};

onMounted(() => {
  fetchStatisticsData();
  // 等待DOM渲染完成后初始化图表
  setTimeout(initCharts, 100);
});

onUnmounted(() => {
  // 移除窗口大小变化监听器
  window.removeEventListener('resize', handleResize);
});
</script>

<style lang="less" scoped>
.media-statistics-container {
  padding: 16px;
  background-color: #f0f2f5;
  min-height: 100%;
}

.statistics-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  margin-bottom: 16px;

  :deep(.ant-card-head) {
    border-bottom: 1px solid #f0f0f0;
    padding: 0 16px;
    font-weight: bold;
    font-size: 16px;
  }
}
</style>
