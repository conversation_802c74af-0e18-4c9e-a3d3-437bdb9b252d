<!--
  * 照明场景
  *
  * @Author:    骆伟林
  * @Date:      2025-04-07 21:36:15
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
    <!---------- 查询表单form begin ----------->
    <a-form class="smart-query-form">
        <a-row class="smart-query-form-row">
            <a-form-item label="标题" class="smart-query-form-item">
                <a-input style="width: 200px" v-model:value="queryForm.label" placeholder="标题" />
            </a-form-item>
            <a-form-item label="类型" class="smart-query-form-item">
                <a-input style="width: 200px" v-model:value="queryForm.type" placeholder="类型" />
            </a-form-item>
            <a-form-item class="smart-query-form-item">
                <a-button type="primary" @click="onSearch">
                    <template #icon>
                        <SearchOutlined />
                    </template>
                    查询
                </a-button>
                <a-button @click="resetQuery" class="smart-margin-left10">
                    <template #icon>
                        <ReloadOutlined />
                    </template>
                    重置
                </a-button>
            </a-form-item>
        </a-row>
    </a-form>
    <!---------- 查询表单form end ----------->
  
    <a-card size="small" :bordered="false" :hoverable="true">
        <!---------- 表格操作行 begin ----------->
        <a-row class="smart-table-btn-block">
            <div class="smart-table-operate-block">
                <a-button @click="showForm" type="primary" size="small">
                    <template #icon>
                        <PlusOutlined />
                    </template>
                    新建
                </a-button>
                <a-button @click="confirmBatchDelete" type="primary" danger size="small" :disabled="selectedRowKeyList.length == 0">
                    <template #icon>
                        <DeleteOutlined />
                    </template>
                    批量删除
                </a-button>
            </div>
            <div class="smart-table-setting-block">
                <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
            </div>
        </a-row>
        <!---------- 表格操作行 end ----------->
  
        <!---------- 表格 begin ----------->
        <a-list :grid="{ gutter: 10, xs: 1, sm: 1, md: 2, lg: 2, xl: 2, xxl: 3 }" :data-source="tableData" :loading="initLoading">
            <template #renderItem="{ item, index }">
                <a-list-item>
                <a-card
                    hoverable
                    :loading="loading"
                    size="default"
                    :style="
                    item.disabled == 1
                        ? 'background: linear-gradient(rgba(88, 158, 255, 0.1), white)'
                        : item.disabled == 0
                        ? 'background: linear-gradient(rgba(128, 128, 128, 0.1), white)'
                        : 'background: linear-gradient(rgba(128, 128, 128, 0.1), white)'
                    "
                >
                    <template #title>
                    <a  class="detail">{{ item.label }}</a>
                    </template>
                    <template #extra>
                    <div style="width: inherit; position: absolute; right: 30px; top: 15px">No{{ index + 1 }}</div>
                    <a-checkbox v-model:checked="item.checked" style="position: absolute; right: 5px; top: 3px" @change="onSelectChange(item)"
                    /></template>
                    <div style="height: 100%; width: 100%; display: flex;" >
                      <div style="flex: 1; display: flex; flex-direction: column;z-index: 2;">
                          <span class="span-multiline-ellipsis"
                          >是否启用：
                          <a-switch 
                              :checked="item.disabled === 1" checked-children="否" un-checked-children="是" @change="switchChange(item)"
                          />
                           </span>
                           <span class="span-multiline-ellipsis">简介：{{ item.info }}</span>
                           <span class="span-multiline-ellipsis">来源：{{ item.source==='rule'?'用户自建':'系统自带'}}</span>
                          <span class="span-multiline-ellipsis">创建时间：{{ item.createTime }}</span>
                       
                      </div>
                      <div style="flex: 1; display: flex; justify-content: center; align-items: center; z-index: 1;">
                          <img src="/@/assets/images/catagory/icon7.svg" alt="" style="max-width: 150px; max-height: 150px; object-fit: contain" />
                      </div>
                    </div>
                    <template #actions>
                    <span style="color: #108ee9" @click="onSet(item)"><setting-outlined key="setting" /> 编辑</span>
                    <span style="color: #f56c6c" @click="onDelete(item)"><DeleteOutlined /> 删除</span>
                    </template>
                </a-card>
                </a-list-item>
            </template>
        </a-list>
        <div class="smart-query-table-page">
          <a-pagination
              showSizeChanger
              showQuickJumper
              show-less-items
              :pageSizeOptions="PAGE_SIZE_OPTIONS"
              :defaultPageSize="queryForm.pageSize"
              v-model:current="queryForm.pageNum"
              v-model:pageSize="queryForm.pageSize"
              :total="total"
              @change="queryData"
              @showSizeChange="queryData"
              :show-total="(total) => `共${total}条`"
          />
      </div>
  
    </a-card>
  </template>
  <script setup>
    import { reactive, ref, onMounted,watch} from 'vue';
    import { message, Modal } from 'ant-design-vue';
    import { SmartLoading } from '/@/components/framework/smart-loading';
    import { lightSceneApi } from '/@/api/business/light/scene/light-scene-api.js';
    import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
    import { smartSentry } from '/@/lib/smart-sentry';
    import TableOperator from '/@/components/support/table-operator/index.vue';
    import { CATEGORY_INDUSTRY_ENUM, DICT_TYPE_ENUM, PUBLISH_STATUS_ENUM } from '/@/constants/business/iot/category/iot-category-const.js';
    import { useUserStore } from '/@/store/modules/system/user';
    import {useRouter} from "vue-router";
    //import FilePreview from '/@/components/support/file-preview/index.vue'; // 图片预览组件
    const router = useRouter();
    // ---------------------------- 表格列 ----------------------------
  
    // ---------------------------- 查询数据表单和方法 ----------------------------
  
    const queryFormState = {
        label: undefined, //标题
        type: undefined,
        pageNum: 1,
        pageSize: 10,
    };
    // 查询表单form
    const queryForm = reactive({ ...queryFormState });
    // 表格加载loading
    const tableLoading = ref(false);
    // 表格数据
    const tableData = ref([]);
    // 总数
    const total = ref(0);
  
    // 重置查询条件
    function resetQuery() {
        let pageSize = queryForm.pageSize;
        Object.assign(queryForm, queryFormState);
        queryForm.pageSize = pageSize;
        queryData();
    }
  
    // 搜索
    function onSearch(){
      queryForm.pageNum = 1;
      queryData();
    }
  
    // 查询数据
    async function queryData() {
        tableLoading.value = true;
        try {
            let queryResult = await lightSceneApi.queryPage(queryForm);
            console.log(queryResult,"查询结果");
            
      total.value = queryResult.data.total;
      tableData.value = queryResult.data.list;
      selected = [];
      tableData.value.forEach((item) => {
        selected.push({
          id: item.id,
          checked: false,
        });
      });
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            tableLoading.value = false;
        }
    }
  
  
    onMounted(queryData);
  
    // ---------------------------- 添加/修改 ----------------------------
    const formRef = ref();
  
    function showForm(data) {
        router.push({
        path: '/light-scene-detail',
        query: {
          type:false,
          isEdit:false,
        }
      });
    }
    function onSet(data) {
        console.log(data);
      router.push({
        path: '/light-scene-detail',
        query: {
          id: data.id,
          isDetail:true,
        }
      });
    }
  
    // ---------------------------- 单个删除 ----------------------------
    //确认删除
    function onDelete(data){
        Modal.confirm({
            title: '提示',
            content: '确定要删除选吗?',
            okText: '删除',
            okType: 'danger',
            onOk() {
                requestDelete(data);
            },
            cancelText: '取消',
            onCancel() {},
        });
    }
  
    //请求删除
    async function requestDelete(data){
        SmartLoading.show();
        try {
            let deleteForm = {
                goodsIdList: selectedRowKeyList.value,
            };
            await lightSceneApi.delete(data.id);
            message.success('删除成功');
            queryData();
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            SmartLoading.hide();
        }
    }
  
  // 选择表格行
  const selectedRowKeyList = ref([]);
  let selected = [];
  
  function onSelectChange(item) {
    const index = selected.findIndex((select) => select.id === item.id);
    if (selected[index].checked) {
        selected[index].checked = false;
        selectedRowKeyList.value = selectedRowKeyList.value.filter((id) => id !== item.id);
    } else {
        selected[index].checked = true;
        selectedRowKeyList.value.push(item.id);
    }
  }
  
  // 批量删除
  function confirmBatchDelete() {
    Modal.confirm({
      title: '提示',
      content: '确定要批量删除这些数据吗?',
      okText: '删除',
      okType: 'danger',
      onOk() {
        requestBatchDelete();
      },
      cancelText: '取消',
      onCancel() {},
    });
  }
  
  //请求批量删除
  async function requestBatchDelete() {
    console.log("调用批量删除接口啦")
    try {
      SmartLoading.show();
      await lightSceneApi.batchDelete(selectedRowKeyList.value);
      selectedRowKeyList.value = [];
      message.success('删除成功');
      queryData();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }
  
  
  async function switchChange(data) {
    if (!data.id) {
        return;
    }
    try {
        data.disabled = data.disabled === 1 ? 0 : 1;
        await lightSceneApi.update(data);
        message.success('操作成功');
        queryData();
    } catch (err) {
        smartSentry.captureError(err);
    }
  }
  const userStore = useUserStore();
onMounted(() => {
  userStore.clearKeepAliveIncludes();
});
  </script>
  <style scoped lang="less">
  :deep(.ant-card-body) {
  padding: 10px 20px;
  }
  .scroll-container {
  height: 580px; /* 设置容器的高度 */
  overflow-y: auto; /* 启用 y 轴滚动 */
  }
  
  ::-webkit-scrollbar-track {
  background: #f1f1f1;
  }
  
  ::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 8px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
  background: #555;
  }
  .span-multiline-ellipsis {
  // display: flex;
  // width: 230px;
  // margin: 5px;
  display: -webkit-box;          /* Flexbox 模式 */
  -webkit-box-orient: vertical; /* 设置盒子为垂直方向 */
  overflow: hidden;             /* 隐藏多余内容 */
  text-overflow: ellipsis;      /* 增加省略号 */
  -webkit-line-clamp: 1;        /* 限制显示2行，多出的内容隐藏 */
  max-width: 100%;              /* 设置最大宽度 */
  line-height: 1.5;             /* 设置行高（根据需要调整） */
  max-height: calc(1.5em * 2);  /* 与行高和行数匹配 */
  word-break: break-word;       /* 防止单词溢出容器 */
  font-size: 14px;              /* 设置字体大小 */
  margin-bottom: 10px;           /* 增加下边距 */
  }
  
  .detail{
  display: inline-block;
  padding: 5px 10px;
  background-color: rgba(88, 158, 255, 0.1); /* 设置背景颜色为淡蓝色 */
  border: 1px solid rgba(88, 158, 255, 0.1); /* 边框颜色 */
  border-radius: 8px; /* 圆角 */
  color: #2c77f1; /* 字体颜色 */
  font-size: 16px; /* 字体大小 */
  text-align: center; /* 文字居中 */
  font-weight: bold; /* 加粗字体 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
  }
  
  .offline-btn {
  display: inline-flex;
  align-items: center;
  padding: 5px 10px;
  background-color: #FDEDED; /* 浅红背景 */
  border: 1px solid transparent; /* 无边框 */
  border-radius: 5px; /* 圆角效果 */
  color: #F56C6C; /* 红色文字 */
  font-size: 14px; /* 字体大小 */
  cursor: pointer;
  }
  
  .offline-btn .icon {
  display: inline-block;
  width: 18px;
  height: 18px;
  margin-right: 5px; /* 图标和文字间距 */
  background-color: #F56C6C; /* 红色圆圈背景 */
  color: #ffffff; /* 图标颜色 */
  font-size: 12px; /* 图标大小 */
  line-height: 18px; /* 圆形内居中对齐 */
  text-align: center;
  border-radius: 50%; /* 圆形样式 */
  }
  
  
  /* 自定义标签容器 */
  .label-container {
  position: relative;
  display: inline-block;
  padding-left: 18px; /* 为图标留出空间 */
  }
  
  /* 提示图标定位 */
  .label-tooltip {
  position: absolute;
  left: 0;
  top: 0;
  }
  
  /* 保持与其他表单项对齐 */
  .custom-label-form-item :deep(.ant-form-item-label) {
  // padding-bottom: 4px;
  line-height: 2.5;
  }
  
  /* 图标样式调整 */
  .label-tooltip .anticon {
  font-size: 14px;
  color: #1890ff;
  cursor: help;
  
  }
  
  /* 输入框组宽度适配 */
  .custom-label-form-item :deep(.ant-input-group) {
  width: 100%;
  
  }
  </style>
  