<!--
    代码编辑器
    <AUTHOR>
    @version 1.0.0
    @date 2025-03-26
    @description 代码编辑器
-->
<template>
  <div class="code-editor">
    <textarea ref="textarea" v-show="false"></textarea>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import CodeMirror from 'codemirror';
import 'codemirror/lib/codemirror.css';
// 主题
import 'codemirror/theme/idea.css';
import 'codemirror/theme/nord.css';
import 'codemirror/theme/xq-light.css';
import 'codemirror/mode/clike/clike';
import 'codemirror/mode/javascript/javascript';
import 'codemirror/addon/display/autorefresh';
// 搜索
import 'codemirror/addon/scroll/annotatescrollbar.js';
import 'codemirror/addon/search/matchesonscrollbar.js';
import 'codemirror/addon/search/match-highlighter.js';
import 'codemirror/addon/search/jump-to-line.js';
import 'codemirror/addon/dialog/dialog.js';
import 'codemirror/addon/dialog/dialog.css';
import 'codemirror/addon/search/searchcursor.js';
import 'codemirror/addon/search/search.js';
// 折叠
import 'codemirror/addon/fold/foldgutter.css';
import 'codemirror/addon/fold/foldcode';
import 'codemirror/addon/fold/foldgutter';
import 'codemirror/addon/fold/brace-fold';
import 'codemirror/addon/fold/comment-fold';
// 格式化
import formatter from '/@/utils/formatter';
import { validatejson, validatenull } from '/@/utils/validate';
import { message } from 'ant-design-vue';

const props = defineProps({
  value: {
    type: String,
    required: true,
    default: '',
  },
  height: {
    type: String,
    required: true,
    default: '450px',
  },
  mode: {
    type: String,
    default: 'javascript',
  },
  theme: {
    type: String,
    default: 'idea',
  },
  readonly: {
    type: Boolean,
    default: false,
  },
  json: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:value']);
const textarea = ref(null);
let editor = null;

onMounted(() => {
  editor = CodeMirror.fromTextArea(textarea.value, {
    mode: props.mode,
    theme: props.theme,
    readOnly: props.readonly,
    autoRefresh: true,
    lineNumbers: true,
    lineWrapping: true,
    tabSize: 2,
    foldGutter: true,
    gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter', 'CodeMirror-lint-markers'],
    extraKeys: {
      'Ctrl-F': 'findPersistent',
      'Cmd-F': 'findPersistent',
      'Ctrl-R': 'replace',
      'Cmd-R': 'replace',
    },
  });

  // 设置高度
  editor.setSize('auto', props.height);

  // 设置文本
  const editorValue = props.json ? formatter.prettyCode(props.value) : props.value;
  editor.setValue(editorValue);

  editor.on('change', () => {
    emit('update:value', editor.getValue());
  });
});

// 监听属性变化
watch(() => props.value, (newVal) => {
  if (editor && newVal !== editor.getValue()) {
    const editorValue = props.json ? formatter.prettyCode(newVal) : newVal;
    editor.setValue(editorValue);
  }
});

watch(() => props.height, (newHeight) => {
  if (editor) {
    editor.setSize('auto', newHeight);
  }
});

// 格式化方法
const prettyCode = () => {
  if (props.json && editor) {
    const val = editor.getValue();
    if (validatenull(val)) {
      message.warning('请先填写数据');
      return;
    }
    if (!validatejson(val)) {
      message.warning('数据 JSON 格式错误');
      return;
    }
    editor.setValue(formatter.prettyCode(val));
  }
};

// 暴露方法供外部使用
defineExpose({
  prettyCode,
  editor: editor
});
</script>

<style scoped>
.code-editor {
  line-height: 1.2 !important;
  width: calc(100% - 4px);
  height: 100%;
  border: 1px solid #ccc;
}

:deep(.CodeMirror) {
  height: 100%;
  font-size: 14px;
}
</style>