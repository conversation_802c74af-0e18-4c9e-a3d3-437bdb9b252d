<template>
  <a-row>
    <a-col :span="4">
        <TreeTimeSelect @update:timeValue="handleTimeValue" />
    </a-col>
    <a-col :span="20">
      <a-form class="smart-query-form">
        <a-row class="smart-query-form-row">
          <!-- 设备搜索表单 -->
          <template v-if="activeKey === 'devices'">
            <a-form-item label="设备名称" class="smart-query-form-item">
              <a-input style="width: 200px" placeholder="请输入设备名称" v-model:value="queryDeviceForm.deviceName" />
            </a-form-item>
            <a-form-item class="smart-query-form-item">
              <a-button type="primary" @click="onDeviceSearch">
                <template #icon>
                  <SearchOutlined />
                </template>
                查询
              </a-button>
              <a-button class="smart-margin-left10" @click="resetDeviceQuery">
                <template #icon>
                  <ReloadOutlined />
                </template>
                重置
              </a-button>
            </a-form-item>
          </template>

          <!-- 区域搜索表单 -->
          <template v-else-if="activeKey === 'regions'">
            <a-form-item label="区域名称" class="smart-query-form-item">
              <a-input style="width: 200px" placeholder="请输入区域名称" v-model:value="queryRegionForm.name" />
            </a-form-item>
            <a-form-item class="smart-query-form-item">
              <a-button type="primary" @click="onRegionSearch">
                <template #icon>
                  <SearchOutlined />
                </template>
                查询
              </a-button>
              <a-button class="smart-margin-left10" @click="resetRegionQuery">
                <template #icon>
                  <ReloadOutlined />
                </template>
                重置
              </a-button>
            </a-form-item>
          </template>
        </a-row>
      </a-form>
      <a-card size="small" :bordered="false" :hoverable="true">
        <template #extra>
          <div class="smart-table-setting-block">
            <TableOperator v-model="columns" :refresh="queryDeviceData" />
          </div>
        </template>
          <a-tabs v-model:activeKey="activeKey">
            <a-tab-pane key="devices" tab="设备报告">
              <a-list :grid="{ gutter: 10, xs: 1, sm: 1, md: 2, lg: 2, xl: 2, xxl: 3 }" :data-source="deviceData" style="height: 80vh;">
                <template #renderItem="{ item, index }">
                  <a-list-item>
                    <a-card hoverable size="default" style="background: linear-gradient(rgba(88, 158, 255, 0.1), white)" @click="showDetail(item)">
                      <template #title>
                        <a class="detail">{{ item.dataExtra?.deviceName  }}</a>
                      </template>
                      <template #extra>
                        <div style="width: inherit; position: absolute; right: 30px; top: 15px">No1</div>
                        <a-checkbox style="position: absolute; right: 5px; top: 3px"></a-checkbox>
                      </template>
                      <div style="height: 100%; width: 100%; display: flex">
                        <div style="flex: 1; display: flex; flex-direction: column; z-index: 2">
                          <span class="span-multiline-ellipsis">备注名称：{{ item.dataExtra?.deviceNoteName }}</span>
                          <span class="span-multiline-ellipsis">产品名称：{{ item.dataExtra?.productName }}</span>
                          <span class="span-multiline-ellipsis">设备类型：{{ getDeviceTypeDesc(item.dataExtra?.deviceType) }}</span>
                          <span class="span-multiline-ellipsis">设备状态：{{ getDeviceStatusEnumDesc(item.dataExtra?.deviceStatus).desc }}</span>
                          <span class="span-multiline-ellipsis">生成时间：{{ dayjs(item.reportTime).format( 'YYYY年MM月') }}</span>
                        </div>
                        <div style="flex: 1; display: flex; justify-content: center; align-items: center; z-index: 1">
                          <img src="/@/assets/images/product/icon2.svg" alt="" style="max-width: 150px; max-height: 150px; object-fit: contain" />
                        </div>
                      </div>
                    </a-card>
                  </a-list-item>
                </template>
              </a-list>
              <div class="smart-query-table-page">
                <a-pagination
                  showSizeChanger
                  showQuickJumper
                  show-less-items
                  :pageSizeOptions="PAGE_SIZE_OPTIONS"
                  :defaultPageSize="queryDeviceForm.pageSize"
                  v-model:current="queryDeviceForm.pageNum"
                  v-model:pageSize="queryDeviceForm.pageSize"
                  :total="deviceTotal"
                  @change="queryDeviceData"
                  :show-total="(total) => `共${total}条`"
                />
              </div>
            </a-tab-pane>
            <a-tab-pane key="regions" tab="区域报告">
                <template #extra>
                  <div class="smart-table-setting-block">
                    <TableOperator v-model="columns" :refresh="queryRegionData" />
                  </div>
                </template>

                <a-list :grid="{ gutter: 10, xs: 1, sm: 1, md: 2, lg: 2, xl: 2, xxl: 3 }" :data-source="regionData" style="height: 80vh;">
                  <template #renderItem="{ item, index }">
                    <a-list-item>
                      <a-card hoverable size="default" style="background: linear-gradient(rgba(88, 158, 255, 0.1), white)" @click="showDetail(item)">
                        <template #title>
                          <a class="detail">{{ item.dataExtra?.name }}</a>
                        </template>
                        <template #extra>
                          <div style="width: inherit; position: absolute; right: 30px; top: 15px">No{{ index + 1 }}</div>
                          <a-checkbox style="position: absolute; right: 5px; top: 3px" @click.stop></a-checkbox>
                        </template>
                        <div style="height: 100%; width: 100%; display: flex">
                          <div style="flex: 1; display: flex; flex-direction: column; z-index: 2">
                            <span class="span-multiline-ellipsis">地址：{{ item.dataExtra?.address }}</span>
                            <span class="span-multiline-ellipsis">面积大小：{{ item.dataExtra?.size }}</span>
                            <span class="span-multiline-ellipsis">联系人：{{ item.dataExtra?.contact }}</span>
                            <span class="span-multiline-ellipsis">电话：{{ item.dataExtra?.phone }}</span>
                            <span class="span-multiline-ellipsis">创建时间：{{ dayjs(item.reportTime).format( 'YYYY年MM月') }}</span>
                          </div>
                          <div style="flex: 1; display: flex; justify-content: center; align-items: center; z-index: 1">
                            <img src="/@/assets/images/product/icon2.svg" alt="" style="max-width: 150px; max-height: 150px; object-fit: contain" />
                          </div>
                        </div>
                      </a-card>
                    </a-list-item>
                  </template>
                </a-list>

                <div class="smart-query-table-page">
                  <a-pagination
                    showSizeChanger
                    showQuickJumper
                    show-less-items
                    :pageSizeOptions="PAGE_SIZE_OPTIONS"
                    :defaultPageSize="queryRegionForm.pageSize"
                    v-model:current="queryRegionForm.pageNum"
                    v-model:pageSize="queryRegionForm.pageSize"
                    :total="regionTotal"
                    @change="queryRegionData"
                    :show-total="(total) => `共${total}条`"
                  />
                </div>
            </a-tab-pane>
          </a-tabs>
      </a-card>
    </a-col>
  </a-row>
</template>

<script setup>
  import TreeTimeSelect from '../components/treetime/index.vue';
  import { ref, reactive, watch } from 'vue';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import TableOperator from '/@/components/support/table-operator/index.vue';
  import { useRouter} from 'vue-router';
  import {getDeviceTypeDesc,getDeviceStatusEnumDesc} from '/@/constants/business/iot/device/iot-device-const.js';
  import {smartSentry} from "/@/lib/smart-sentry.js";
  import { energyApi } from '/@/api/business/operate/energy/energy-api';
  import dayjs from 'dayjs';

  const initLoading = ref(false);
  const loading = ref(false);
  const activeKey = ref('devices');
  const columns = ref([]);
  const selectedTime = ref('');
  //---------------------------------路由跳转---------------------------------
  const router = useRouter();
  function showDetail(data) {
      router.push({
        path: '/iot/operate/energy/detail',
        query: {
          id: data.id,
        }
    })
  }
// ------------------------- 处理时间 ----------------------------------

  // 处理时间选择
  function handleTimeValue(value) {
    selectedTime.value = value;
    const [startDate, endDate] = handleMonthRange(value);
    queryDeviceForm.reportTimeBegin = startDate;
    queryDeviceForm.reportTimeEnd = endDate;
    queryRegionForm.reportTimeBegin = startDate;
    queryRegionForm.reportTimeEnd = endDate;
  }
  // 处理月份范围
  function handleMonthRange(monthStr) {
    const date = dayjs(monthStr);
    const startDate = date.startOf('month').format('YYYY-MM-DD');
    const endDate = date.endOf('month').format('YYYY-MM-DD');
    return [startDate, endDate];
  }
// ---------------------------- 查询设备信息 ----------------------------
  const queryDeviceFormState = {
    deviceName: undefined, //设备名称
    uniqueNo: undefined,//唯一编号
    regionId: undefined, //区域ID
    productKey:undefined,//产品id
    productName:undefined,//产品名称
    pageNum: 1,
    pageSize: 10,
    searchCount:true,
    reportTimeBegin:undefined,
    reportTimeEnd:undefined,
    sortItemList: [{ isAsc: false, column: 'create_time' }],
  };
  // 查询表单form
  const queryDeviceForm = reactive({ ...queryDeviceFormState });
  // 表格数据
  const deviceData = ref([]);
  // 总数
  const deviceTotal = ref(0);
  function resetDeviceQuery() {
    let pageSize = queryDeviceForm.pageSize;
    Object.assign(queryDeviceForm, queryDeviceFormState);
    queryDeviceForm.pageSize = pageSize;
    queryDeviceData();
  }
  function onDeviceSearch() {
    queryDeviceForm.pageNum = 1;
    queryDeviceData();
  }
  // 查询数据
  async function queryDeviceData() {
    initLoading.value = true;
    loading.value = true;
    try {
      let queryResult = await energyApi.queryPageByDevice(queryDeviceForm);
      deviceTotal.value = queryResult.data.total;
      deviceData.value =  queryResult.data.list;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      initLoading.value = false;
      loading.value = false;
    }
  }
// ---------------------------- 查询区域信息 ----------------------------
  const queryRegionFormState = {
    name: undefined, //区域名称
    pageNum: 1,
    pageSize: 10,
    searchCount:true,
    reportTimeBegin:undefined,
    reportTimeEnd:undefined,
    sortItemList: [{ isAsc: false, column: 'create_time' }],
  };
  const queryRegionForm = reactive({ ...queryRegionFormState });
  // 表格数据
  const regionData = ref([]);
  const regionTotal = ref(0);
  // 重置查询条件
  function resetRegionQuery() {
    let pageSize = queryRegionForm.pageSize;
    Object.assign(queryRegionForm, queryRegionFormState);
    queryRegionForm.pageSize = pageSize;
    queryRegionData();
  }
  function onRegionSearch() {
    queryRegionForm.pageNum = 1;
    queryRegionData();
  }
  // 查询数据
  async function queryRegionData() {
    initLoading.value = true;
    loading.value = true;
    try {
      let queryResult = await energyApi.queryPageByRegion(queryRegionForm);
      regionTotal.value = queryResult.data.total;
      regionData.value = queryResult.data.list;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      initLoading.value = false;
      loading.value = false;
    }
  }
// ---------------------------- 监听标签页和时间变化 ----------------------------
  watch([activeKey, selectedTime], ([newActiveKey, newTimeValue]) => {
    if (newTimeValue) {
      if (newActiveKey === 'devices') {
        queryDeviceData();
      } else if (newActiveKey === 'regions') {
        queryRegionData();
      }
    }
  });
</script>

  <style lang="less" scoped>
    :deep(.ant-card-body) {
      padding: 10px 20px;
    }

    .span-multiline-ellipsis {
      display: -webkit-box; /* Flexbox 模式 */
      -webkit-box-orient: vertical; /* 设置盒子为垂直方向 */
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 1; /* 限制显示1行，多出的内容隐藏 */
      max-width: 100%;
      line-height: 1.5;
      max-height: calc(1.5em * 2); /* 与行高和行数匹配 */
      word-break: break-word;
      font-size: 14px;
      margin-bottom: 10px;
    }
  </style>
