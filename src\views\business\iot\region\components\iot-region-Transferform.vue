<template>
  <a-modal
    v-model:open="visible"
    title=""
    :width="modalWidth"
    :maskClosable="false"
    wrap-class-name="fullscreen-modal"
  >
    <div style="font-size: 25px; font-weight: 700;">[{{ regionName }}]-区域下的设备管理</div>
    <!-- 模态框内容保持不变 -->
    <div class="modal-header-extra">
      <a-space>
        <span>此区域内所分配设备: {{ leftData.length }}</span>
        <span>此区域内所未分配设备: {{ rightData.length }}</span>
      </a-space>
    </div>
    
    <a-transfer
      v-model:target-keys="targetKeys"
      :data-source="formattedAllDevices"
      :titles="['--此区域内所分配设备', '--此区域内所未分配设备']"
      :row-key="record => record.key"
      @change="handleTransferChange"
      :operations="['将设备解绑', '将勾选设备划分到该区域里']"
      :list-style="{
        width: '48%',
        height: transferHeight
      }"
      :selected-keys="selectedKeys"
      @selectChange="handleSelectChange"
    >
      <template #children="{ direction, selectedKeys: currentSelectedKeys, onItemSelect, onItemSelectAll }">
        <a-table
          :columns="tableColumns"
          :data-source="direction === 'left' ? leftData : rightData"
          :row-selection="{
            selectedRowKeys: currentSelectedKeys,
            onChange: (selectedRowKeys, selectedRows) => {
              handleTableSelectionChange(selectedRowKeys, selectedRows, direction, onItemSelect, onItemSelectAll);
            },
            onSelectAll: (selected, selectedRows, changedRows) => {
              onItemSelectAll(selectedRows.map(r => r.key), selected);
            },
            onSelect: (record, selected) => {
              onItemSelect(record.key, selected);
            },
            type: 'checkbox',
            getCheckboxProps: record => ({ disabled: false })
          }"
          :pagination="false"
          size="small"
          bordered
          style="width: 100%"
          :scroll="{ y: tableScrollY }"
          @row-click="(record) => {
            const isSelected = currentSelectedKeys.includes(record.key);
            onItemSelect(record.key, !isSelected);
          }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'deviceStatus'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
          </template>
        </a-table>
      </template>
    </a-transfer>

    <template #footer>
      <div class="modal-footer">
        <a-button @click="toggleFullscreen">
          <template #icon>
            <FullscreenExitOutlined v-if="isFullscreen" />
            <FullscreenOutlined v-else />
          </template>
          {{ isFullscreen ? '退出全屏' : '全屏' }}
        </a-button>
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleSubmitSet" :loading="submitting">保存</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { FullscreenOutlined, FullscreenExitOutlined } from '@ant-design/icons-vue';
import { iotRegionApi } from '/@/api/business/iot/region/iot-region-api.js';

const visible = ref(false);
const targetKeys = ref([]);
const selectedKeys = ref([]);
const allDevices = ref([]);
const selectedDevices = ref([]);
const regionId = ref('');
const regionName = ref('');
const submitting = ref(false);
const a = ref([]);

// 全屏相关状态
const isFullscreen = ref(true);
const modalWidth = computed(() => isFullscreen.value ? '100%' : '1200px');
const transferHeight = computed(() => isFullscreen.value ? 'calc(100vh - 200px)' : '500px');
const tableScrollY = computed(() => isFullscreen.value ? 'calc(100vh - 300px)' : '450px');

// 设备状态映射
const statusMap = {
  0: { text: '离线', color: 'red' },
  1: { text: '在线', color: 'green' },
  2: { text: '未激活', color: 'orange' }
};

const getStatusColor = (status) => statusMap[status]?.color || 'default';
const getStatusText = (status) => statusMap[status]?.text || '未知';

// 安全格式化设备数据
const safeFormatDevice = (item) => {
  if (!item) return null;
  
  return {
    key: item?.id?.toString() || Math.random().toString(36).substr(2, 9),
    title: item.deviceName || '未知设备',
    deviceName: item.deviceName || '未知设备',
    productName: item.productName || '',
    categoryName: item.categoryName || '',
    status: item.deviceStatus ?? 0,
    description: item.description || '',
    deviceSecret: item.deviceSecret || '',
    deviceType: item.deviceType || '',
    lastOnlineTime: item.lastOnlineTime || '',
    createTime: item.createTime || ''
  };
};

// 格式化所有设备数据
const formattedAllDevices = computed(() => {
  try {
    return [...allDevices.value, ...selectedDevices.value]
      .map(item => safeFormatDevice(item))
      .filter(item => item && item.key);
  } catch (error) {
    console.error('格式化设备数据出错:', error);
    return [];
  }
});

const tableColumns = [
  {
    title: '设备名称',
    dataIndex: 'deviceName',
    key: 'deviceName',
    ellipsis: true,
    width: '20%'
  },
  {
    title: '产品名称',
    dataIndex: 'productName',
    key: 'productName',
    ellipsis: true,
    width: '20%'
  },
  {
    title: '所属品类',
    dataIndex: 'categoryName',
    key: 'categoryName',
    ellipsis: true,
    width: '15%'
  },
  {
    title: '设备状态',
    dataIndex: 'status',
    key: 'deviceStatus',
    width: '15%'
  },
  {
    title: '设备描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true,
    width: '15%'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    ellipsis: true,
    width: '15%'
  }
];

// 计算选择数量
const leftData = computed(() => {
  try {
    return formattedAllDevices.value.filter(item => 
      item && item.key && !targetKeys.value.includes(item.key)
    );
  } catch (error) {
    console.error('计算左侧数据出错:', error);
    return [];
  }
});

const rightData = computed(() => {
  try {
    return formattedAllDevices.value.filter(item => 
      item && item.key && targetKeys.value.includes(item.key)
    );
  } catch (error) {
    console.error('计算右侧数据出错:', error);
    return [];
  }
});

const handleSelectChange = (sourceSelectedKeys, targetSelectedKeys) => {
  selectedKeys.value = [...sourceSelectedKeys, ...targetSelectedKeys];
};

// 处理表格选择变化 - 修复后的版本
const handleTableSelectionChange = (selectedRowKeys, selectedRows, direction, onItemSelect, onItemSelectAll) => {
  const allData = direction === 'left' ? leftData.value : rightData.value;
  
  // 如果取消选择所有行
  if (selectedRowKeys.length === 0) {
    onItemSelectAll([], false);
    return;
  }
  
  // 如果全选
  if (selectedRowKeys.length === allData.length) {
    onItemSelectAll(allData.map(r => r.key), true);
    return;
  }
  
  // 处理部分选择/取消选择
  allData.forEach(row => {
    const shouldSelect = selectedRowKeys.includes(row.key);
    const isSelected = selectedKeys.value.includes(row.key);
    
    if (shouldSelect !== isSelected) {
      onItemSelect(row.key, shouldSelect);
    }
  });
};

// 处理transfer变化
const handleTransferChange = (nextTargetKeys, direction, moveKeys) => {
  if (direction === 'right') {
    // 如果是从左到右移动，则从a.value中移除moveKeys
    a.value = a.value.filter(key => !moveKeys.includes(key));
  } else if (direction === 'left') {
    // 如果是从右到左移动，则将moveKeys添加到a.value
    a.value = [...a.value, ...moveKeys];
  }
  handleSubmitSet();
};

const handleCancel = () => {
  visible.value = false;
};

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
};

// 安全获取设备数据
const fetchAllDevices = async () => {
  try {
    const params = {
      pageNum: 1,
      pageSize: 100,
      regionId: regionId.value,
      sortItemList: [
        {
          "isAsc": true,
          "column": "createTime"
        }
      ],
    };
    const res = await iotRegionApi.queryDeviceByArea(params);
    allDevices.value = Array.isArray(res?.data) ? res.data : [];
    a.value = allDevices.value
      .map(item => item?.id?.toString())
      .filter(key => key && typeof key === 'string');
  } catch (error) {
    console.error('获取设备失败:', error);
    allDevices.value = [];
  }
};

// 安全获取未分配设备数据
const fetchSelectedDevices = async () => {
  try {
    const params = {
      pageNum: 1,
      pageSize: 100,
      regionId: regionId.value,
      sortItemList: [
        {
          "isAsc": true,
          "column": "createTime"
        }
      ],
    };
    const res = await iotRegionApi.queryUnSetRegionDevicePage(params);
    selectedDevices.value = Array.isArray(res?.data) ? res.data : [];
    targetKeys.value = selectedDevices.value
      .map(item => item?.id?.toString())
      .filter(key => key && typeof key === 'string');
  } catch (error) {
    console.error('获取未分配设备失败:', error);
    selectedDevices.value = [];
    targetKeys.value = [];
  }
};

//设置
const handleSubmitSet = async () => {
  submitting.value = true;
  try {
    const params = {
      regionId: regionId.value,
      regionName: regionName.value,
      deviceIds: a.value
    };
    await iotRegionApi.setRegionDevice(params);
    console.log('设置成功:', params);
    message.success('保存成功');
    fetchAllDevices();
    fetchSelectedDevices();
  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败');
  } finally {
    submitting.value = false;
  }
};

// 修改后的Form方法，接收参数
const Form = (item) => {
  if (item) {
    regionId.value = item.id;
    regionName.value = item.name;
  }
  visible.value = true;
  fetchAllDevices();
  fetchSelectedDevices();
};

// 暴露方法给父组件
defineExpose({ Form });
</script>

<style scoped>
.modal-header-extra {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-end;
}

.ant-transfer {
  display: flex;
  justify-content: space-between;
}

.ant-table-row {
  cursor: pointer;
}

.ant-table-row:hover {
  background-color: #f5f5f5;
}

:deep(.ant-transfer-list) {
  flex: 1;
  margin: 0 8px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 16px 0 0 0;
  border-top: 1px solid #f0f0f0;
}

:deep(.ant-table-body) {
  overflow-y: auto !important;
}
</style>

<style>
.fullscreen-modal .ant-modal {
  top: 0;
  padding-bottom: 0;
  margin: 0;
  max-width: 100%;
  height: 100vh;
}

.fullscreen-modal .ant-modal-content {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.fullscreen-modal .ant-modal-body {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.fullscreen-modal .ant-transfer-list-body {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.fullscreen-modal .ant-table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.fullscreen-modal .ant-table {
  flex: 1;
}

.fullscreen-modal .ant-table-body {
  overflow-y: auto !important;
  flex: 1;
}
</style>