<template>

    <a-form class="smart-query-form" style="margin-top: 10px;">
      <a-row class="smart-query-form-row">
        <a-form-item label="模块" class="smart-query-form-item">
          <a-select
            v-model:value="selectedBlockId"
            style="width: 200px"
            placeholder="请选择模块"
          >
            <a-select-option
              v-for="block in blocks"
              :key="block.id"
              :value="block.id"
            >
              {{ block.blockName }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="属性名称" class="smart-query-form-item">
          <a-input style="width: 200px" placeholder="请输入属性名称" v-model:value="searchName" />
        </a-form-item>
        <a-form-item class="smart-query-form-item">
          <a-button type="primary" @click="onSearch">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
          <a-button class="smart-margin-left10" @click="resetQuery">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
        </a-form-item>
      </a-row>
    </a-form>

  <a-card style="margin-top: 16px">
    <a-list :grid="{ gutter: 10, xs: 1, sm: 1, md: 2, lg: 2, xl: 2, xxl: 3 }" :data-source="displayProperties">
      <template #renderItem="{ item }">
        <a-list-item>
          <a-card hoverable size="default" style="background: linear-gradient(rgba(88, 158, 255, 0.1), white)">
            <template #title>
              <a class="detail">{{ item.name }}</a>
            </template>
            <div style="height: 100%; width: 100%; display: flex">
              <div style="flex: 1; display: flex; flex-direction: column; z-index: 2">
                <span class="span-multiline-ellipsis">属性标识符：{{ item.identifier }}</span>
                <span class="span-multiline-ellipsis">数据类型：{{ item.specFunction.fieldType }}</span>
                <span class="span-multiline-ellipsis">数据定义：取值范围: {{ item.specFunction.specMin }} ~ {{ item.specFunction.specMax }}</span>
                <span class="span-multiline-ellipsis">属性最新值：{{ item.specFunction.specUnit }}</span>
              </div>
            </div>
          </a-card>
        </a-list-item>
      </template>
    </a-list>
  </a-card>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { iotDeviceApi } from '/@/api/business/iot/device/iot-device-api.js';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons-vue';
import { smartSentry } from '/@/lib/smart-sentry';

// 接收父组件传递的路由数据对象
const props = defineProps({
  routeData: Object
});

const blocks = ref([]);
const properties = ref([]);
const selectedBlockId = ref(null);
const searchName = ref('');

const displayProperties = ref([]);

// 根据选择的模块做筛选
const filteredProperties = computed(() => {
  return properties.value.filter(item => !selectedBlockId.value || item.blockId === selectedBlockId.value);
});

// 获取TSL物模型数据
const getTslData = async () => {
  try {
    const response = await iotDeviceApi.getTslDevice({
      productKey: props.routeData.key,
      deviceName: props.routeData.name
    });
    blocks.value = response.data.blocks;
    properties.value = response.data.properties;
    if (blocks.value.length > 0) {
      selectedBlockId.value = blocks.value[0].id;
    }
    displayProperties.value = filteredProperties.value;
  } catch (error) {
    smartSentry.captureError(error);
  }
};

// 搜索
const onSearch = () => {
  displayProperties.value = filteredProperties.value.filter(item => 
    !searchName.value || item.name.toLowerCase().includes(searchName.value.toLowerCase())
  );
};

// 重置
const resetQuery = () => {
  searchName.value = '';
  selectedBlockId.value = blocks.value[0]?.id;
  displayProperties.value = filteredProperties.value;
};

onMounted(() => {
  getTslData();
});
</script>