<!--
  * 配置示例表
  *
  * @Author:    潘茜茜
  * @Date:      2025-03-27 21:21:23
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
  <a-drawer
      :title="form.id ? '编辑' : '添加'"
      :width="800"
      v-model:open="visibleFlag"
      placement="right"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
  <a-form  ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }" >
          <!-- 产品基本信息 -->
          <a-form-item label="标题"  name="label">
            <a-input style="width: 100%" v-model:value="form.label" placeholder="请输入标题" />
          </a-form-item>
          <a-form-item label="图标"  name="logo">
            <Upload 
            accept=".jpg,.png" 
            :maxUploadSize="1" 
            buttonText="点击上传图片" 
            :defaultFileList="form.logo ?[{fileUrl:form.logo}] : []"
            listType="picture-card" 
            @change="changeAttachment" 
            :folder="FILE_FOLDER_TYPE_ENUM.FEEDBACK.value" />
            <div style="color:gray">只能上传jpg/png文件，且不能超过500kb</div>
          </a-form-item>
          <a-form-item label="排序号"  name="sort">
            <a-input-number id="inputNumber" v-model:value="form.sort" placeholder="请输入排序号"/>
          </a-form-item>
          <a-form-item label="说明"  name="info">
            <SmartWangeditor ref="contentRef" v-model="form.info" :height="300" />
          </a-form-item>
          
        </a-form>
    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>


<script setup>
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { reactive, ref, nextTick,watch} from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { iotEdgeDemoApi } from '/@/api/business/iot/edge/iot-edge-demo-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  import SmartWangeditor from '/@/components/framework/wangeditor/index.vue';
  import Upload from '/@/components/support/file-upload/index.vue';
  import { FILE_FOLDER_TYPE_ENUM } from '/@/constants/support/file-const';
  import DictSelect from '/@/components/support/dict-select/index.vue';// 字典下拉框
  import { CONNECTION_MODE_ENUM } from '/@/constants/business/iot/product/iot-product-const.js';

  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show(rowData) {
    console.log(rowData);
    
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
    }
    visibleFlag.value = true;
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
    id:undefined,
    source:"demo",
    type:"tab",
    //标题
    label: undefined, 
    //图标
    logo: undefined,
    //排序号
    sort: undefined,
    //说明
    info: undefined,
  };

  let form = reactive({ ...formDefault });

  const rules = {
    label: [{ required: true, message: '标题 必填' }],
    sort: [{ required: true, message: '排序号 必填' }],
  };

  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      if (form.id) {
        console.log('end',form);
        await iotEdgeDemoApi.update(form);
      } else {
        form.id=undefined;
        await iotEdgeDemoApi.save(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  // 文件上传
  function changeAttachment(fileList) {
    form.logo = fileList[0]?.fileUrl || "";
  }
defineExpose({
  show,
});
</script>

<style scoped>
.product-manage {
  max-width: 1200px;
  margin: 0 auto;
}
.upload-tip {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  margin-top: 8px;
}
.mb-4 {
  margin-bottom: 16px;
}
.avatar-uploader > .ant-upload {
  width: 128px;
  height: 128px;
}
.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}
</style>
