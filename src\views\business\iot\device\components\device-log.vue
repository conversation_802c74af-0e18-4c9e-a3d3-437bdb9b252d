<template>

  <a-form class="smart-query-form" style="margin-top: 10px;">
    <a-row class="smart-query-form-row">
      <a-form-item label="标题" class="smart-query-form-item">
        <a-input style="width: 200px" v-model:value="queryForm.topic" placeholder="请输入topic查询" />
      </a-form-item>
      <a-form-item label="时间范围" class="smart-query-form-item">
        <a-range-picker :presets="defaultTimeRanges" style="width: 240px" v-model:value="reportTime"
          @change="onChangeDate" />
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="queryData">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="handleClear" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>

  <a-card size="small" :bordered="false">
    <a-table size="small" :dataSource="logData" :columns="columns" bordered rowKey="id" :pagination="false"
      :scroll="{ y: 600 }">
    </a-table>
    <div class="smart-query-table-page">
      <a-pagination showSizeChanger showQuickJumper show-less-items :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.size" v-model:current="queryForm.current" v-model:pageSize="queryForm.size"
        :total="total" @change="queryData" @showSizeChange="queryData" :show-total="(total) => `共${total}条`" />
    </div>
  </a-card>
</template>

<script setup>
import { defaultTimeRanges } from '/@/lib/default-time-ranges.js';
import { ref, reactive, onMounted, watch } from 'vue';
import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
import { iotDeviceApi } from '/@/api/business/iot/device/iot-device-api.js';
import { smartSentry } from '/@/lib/smart-sentry';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons-vue';
import { SmartLoading } from '/@/components/framework/smart-loading';

// Props 定义
const props = defineProps({
  deviceId: {
    type: String,
    required: true
  }
});

// 响应式数据
const total = ref(0);
const logData = ref([]);
const reportTime = ref([]);

// 查询表单状态
const queryFormState = {
  current: 1,
  size: 10,
  topic: undefined,
  deviceId: props.deviceId,
  startTime: undefined,
  endTime: undefined
};

const queryForm = reactive({
  ...queryFormState
});

// 表格配置
const columns = [
  {
    title: '#',
    dataIndex: 'seq',
    width: 50
  },
  {
    title: '更新时间',
    dataIndex: 'time',
    width: 150,
  },
  {
    title: 'topic',
    dataIndex: 'topic',
    width: 200
  },
  {
    title: '消息内容',
    dataIndex: 'message',
    width: 300
  }
];

// 日期变化处理
function onChangeDate(dates, dateString) {
  queryForm.startTime = dates[0];
  queryForm.endTime = dates[1];
}

// 查询数据
async function queryData() {
  SmartLoading.show();
  try {
    const res = await iotDeviceApi.getCloudLog(queryForm);
    logData.value = res.data.records.map((item, index) => ({
      ...item,
      seq: index + 1
    }));
    total.value = res.data.total;
  } catch (error) {
    smartSentry.captureError(error);
  } finally {
    SmartLoading.hide();
  }
}

// 清空查询条件
const handleClear = () => {
  Object.assign(queryForm, queryFormState);
  reportTime.value = [];
  queryData();
};

// 监听 deviceId 变化
watch(() => props.deviceId, (newVal) => {
  if (newVal) {
    queryForm.deviceId = newVal;
    queryData();
  }
});

onMounted(() => {
  queryData();
});
</script>

