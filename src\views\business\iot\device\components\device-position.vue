<!--
  * 设备位置
  *
  * @Author:    骆伟林
  * @Date:      2025-04-24 17:51:27
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
  <a-row :gutter="8" style="margin-bottom: 10px">
    <div style="display: flex; align-items: center">
      搜索地址：
      <table style="margin-right: 10px">
        <tr>
          <td>
            <a-input id="tipinput" v-model:value="address" placeholder="请输入地址" />
          </td>
        </tr>
      </table>
    </div>
    <a-button type="primary" style="margin: 0 10px" @click="drawMarker">开始标记位置</a-button>
    <a-button type="primary" style="margin-right: 10px" @click="clearDrawings">清空标记位置</a-button>
    <a-button type="primary" @click="confirmRegion" :disabled="!isMarkerValid" >确定位置</a-button>
  </a-row>
  <div>
    <div id="container" ref="mapContainer"></div>
    <a-button size="small" @click="filter" class="controlShow-button">
      <UnorderedListOutlined />{{ showFilter ? '隐藏区域' : '显示区域' }}
    </a-button>
  </div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted, computed } from 'vue';
  import AMapLoader from '@amap/amap-jsapi-loader';
  import { message } from 'ant-design-vue';
  import { iotDeviceApi } from '/@/api/business/iot/device/iot-device-api.js';

  const emit = defineEmits(['getArea']);

  // 定义 props
  const props = defineProps({
    address: {
      type: String,
      default: '',
    },
    longitude: {
      type: Number,
      default: undefined,
    },
    latitude: {
      type: Number,
      default: undefined,
    },
    deviceId: {
      type: String,
      required: true,
    },
    latAndLon:{
      type: Array,
      default: () => ([undefined, undefined]),
    }
  });

  const address = ref('');
  let map = null;
  let mouseTool = null;
  let polygon = null; // 用于存储绘制的多边形
  let currentOverlay = null;
  let AMapInstance = null;
  let placeSearch = null; // 地点搜索实例
  let autoComplete = null; // 自动完成实例
  let longitude = null;
  let latitude = null;
  let marker = null; // 用于存储点标记

  // 初始化地图
  let initMap = () => {
    const container = document.getElementById('container');
    if (!container) {
      console.error('Map container div not exist');
      return;
    }
    window._AMapSecurityConfig = {
      securityJsCode: 'fd83369091f64a0f25572251e0c9eae5',
    };

    AMapLoader.load({
      key: '1778f4eaad1a5a43d9b04ef0c9690b3f', // 高德地图 Key
      version: '2.0',
      plugins: [
        'AMap.MouseTool',
        'AMap.ToolBar',
        'AMap.Scale',
        'AMap.HawkEye',
        'AMap.ControlBar',
        'AMap.Geometry',
        'AMap.AutoComplete',
        'AMap.PlaceSearch',
        'AMap.GeometryUtil'
        
      ],
    })
      .then((AMap) => {
        AMapInstance = AMap;
        map = new AMap.Map('container', {
          center: [116.397428, 39.90923], // 地图中心点
          zoom: 14,
          viewMode: '2D',
          pitch: 30,
        });
        // 初始化鼠标工具
        mouseTool = new AMap.MouseTool(map);
        // 监听绘制完成事件
        mouseTool.on('draw', (event) => {
          if (event.obj instanceof AMap.Polygon) {
            polygon = event.obj;
            currentOverlay = event.obj;
          } else if (event.obj instanceof AMap.Marker) {
            if (marker) {
              map.remove(marker);
            }
            marker = event.obj;
            currentOverlay = event.obj;
          }
        });

        // 添加比例尺控件
        const scale = new AMap.Scale();
        map.addControl(scale);

        // 添加工具条控件
        const toolBar = new AMap.ToolBar({
          position: {
            top: '110px',
            right: '40px',
          },
        });
        map.addControl(toolBar);

        // 初始化自动完成服务
        autoComplete = new AMapInstance.AutoComplete({
          input: 'tipinput',
        });
        // 监听选择事件
        autoComplete.on('select', select);
        // 初始化地点搜索
        placeSearch = new AMapInstance.PlaceSearch({
          map: map,
        });
        if(props.longitude&&props.latitude){
          // 设置地图中心点
          map.setCenter([props.longitude, props.latitude]);
          map.setZoom(15);

        // 回显图标
        if (props.latitude !== undefined && props.longitude !== undefined) {
          addMarker(props.longitude, props.latitude);
        }
        }
         // 回显多边形
      if (parsedLatAndLon.value.length > 0) {
        drawExistingPolygon(parsedLatAndLon.value);
      }
      })
      .catch((e) => {
        console.error('地图加载失败:', e);
      });
  };
  // 处理选择
  const select = (e) => {
    if (e.poi && e.poi.location) {
      const { lng, lat } = e.poi.location;
      const { adcode, name } = e.poi;
      console.log(name);
      address.value = name;
      // 跳转到选择的位置
      map.setCenter([lng, lat]);
      map.setZoom(15);
      // 在地图上显示标记
      placeSearch.setCity(adcode);
      placeSearch.search(name);
      longitude = lng;
      latitude = lat;
    }
  };
  const parsedLatAndLon = computed(() => {
  try {
    const parsed = JSON.parse(props.latAndLon.replace(/\(/g, '[').replace(/\)/g, ']'));
    return parsed.map(([lat, lng]) => [lng, lat]);
  } catch (e) {
    console.error('解析 latAndLon 失败:', e);
    return [];
  }
});
  function drawExistingPolygon(path) {
    if (map && path.length > 0) {
      polygon = new AMapInstance.Polygon({
        path: path,
        strokeColor: '#FF33FF',
        strokeOpacity: 1,
        strokeWeight: 6,
        strokeOpacity: 0.2,
        fillColor: '#1791fc',
        fillOpacity: 0.4,
        strokeStyle: 'solid',
      });
      map.add(polygon);
      currentOverlay = polygon;
      map.setFitView([polygon]); // 调整视图以适应多边形
      console.log(1);
      
    }
  }

  // 清空绘制图像
  function clearDrawings() {
    if (marker) {
      map.remove(marker);
      marker = null;
      currentOverlay = null;
    }
    isMarkerValid.value = false;
  }

  // 确定区域
  async function confirmRegion() {
    if (marker) {
      try{
        const position = marker.getPosition();
      iotDeviceApi.setCoordinate({
        id: props.deviceId,
        longitude: position.getLng(),
        latitude: position.getLat(),
      });
      message.success('设置成功');
      }
      catch(e){
        console.log(e);
        message.info(e);
      }
     
      
    } else {
      message.warning('请先绘制覆盖物');
    }
  }

  const regionList = ref([]);

  async function regionSearch() {
    const res = await iotDeviceApi.queryLightInformation({});
    regionList.value = res.data.regionList;
  }

  const isMarkerValid = ref(true);
  function drawMarker() {
  if (marker) {
    map.remove(marker);
    marker = null;
  }

  // 开启鼠标工具绘制点标记
  mouseTool.marker({
    cursor: 'crosshair',
    draggable: true,
  });

  // 监听地图点击事件
  map.off('click'); // 避免重复绑定
  map.on('click', (e) => {
    const { lng, lat } = e.lnglat;
    const point = [lng, lat];
    const path = polygon.getPath();
    const isInside = AMapInstance.GeometryUtil.isPointInRing(point, path);
    if (isInside) {
      if (marker) {
        map.remove(marker);
      }
      marker = new AMapInstance.Marker({
        position: new AMapInstance.LngLat(lng, lat),
        title: '设备位置',
        draggable: false,
      });
      map.add(marker);
      isMarkerValid.value = true;
    } else {
      message.warning('请在区域内设置位置');
      isMarkerValid.value = false;
    }
  });
}

  const showFilter = ref(true);
  const filter = () => {
    showFilter.value = !showFilter.value;
    if (showFilter.value) {
      showPolygon();
    } else {
      hidePolygon();
    }
  };

  // 显示区域多边形
  const showPolygon = () => {
    if (props.latAndLon) {
      const parsedLatAndLon = computed(() => {
        try {
          const parsed = JSON.parse(props.latAndLon.replace(/\(/g, '[').replace(/\)/g, ']'));
          return parsed.map(([lat, lng]) => [lng, lat]);
        } catch (e) {
          console.error('解析 latAndLon 失败:', e);
          return [];
        }
      });
      drawExistingPolygon(parsedLatAndLon.value);
    }
  };

  // 隐藏区域多边形
  const hidePolygon = () => {
    if (polygon) {
      map.remove(polygon);
      polygon = null;
    }
  };

  // 添加点标记
  function addMarker(lng, lat) {
    if (marker) {
      map.remove(marker);
    }
    marker = new AMapInstance.Marker({
      position: new AMapInstance.LngLat(lng, lat),
      title: '设备位置',
      draggable: false,
    });
    map.add(marker);
    map.setCenter([lng, lat]);
    map.setZoom(15);
  }
  // 组件挂载时初始化地图
  onMounted(async () => {
    regionSearch();
    address.value = props.address;
     initMap();
  });

  // 组件卸载时销毁地图
  onUnmounted(() => {
    if (map) {
      map.destroy();
      map = null;
    }
  });
</script>

<style scoped>
  #container {
    width: 100%;
    height: 70vh;
  }
  .input-card {
    margin-top: 10px;
  }
  .controlShow-button {
    position: absolute;
    top: 10%;
    right: 5%;
    z-index: 5;
  }
</style>