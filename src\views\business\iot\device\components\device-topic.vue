<template>
  <a-card>
    <a-table :columns="columns" :data-source="tableData" :pagination="false" bordered :row-key="(record) => record.id">
    </a-table>
  </a-card>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { iotDeviceApi } from '/@/api/business/iot/device/iot-device-api.js';
import { smartSentry } from '/@/lib/smart-sentry';

// 接收父组件传递的路由数据对象
const props = defineProps({
  routeData: Object
});

const tableData = ref([]);

// 表格列定义
const columns = [
    {
        title: '#',
        dataIndex: 'seq',
        width: 80,
    },
    {
        title: '设备的Topic',
        dataIndex: 'topic',
        width: 1000,
    },
    {
        title: 'Topic描述',
        dataIndex: 'topicDesc',
    }
];

// 获取数据
const getTopicList = async () => {
    try {
        const response = await iotDeviceApi.getTopicList({
            productKey: props.routeData.key,
            deviceName: props.routeData.name
        });
        tableData.value = response.data.map((item, index) => ({
            ...item,
            seq: index + 1
        }));
    } catch (error) {
      smartSentry.captureError(error);
    }
};

onMounted(() => {
    getTopicList();
});
</script>