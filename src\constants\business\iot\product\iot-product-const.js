/**
 * 产品信息表 枚举
 *
 * @Author:    李帅兵
 * @Date:      2025-03-21 21:15:14
 * @Copyright  中山睿数信息技术有限公司 2025
 */

// 网关协议
export const GATEWAY_PROTOCOL_ENUM = {
  CUSTOM: {
    value: 'custom',
    desc: '自定义'
  },
  MQTT: {
    value: 'mqtt',
    desc: 'MQTT'
  },
  MODBUS: {
    value: 'Modbus',
    desc: 'Modbus'
  },
  OPCUA: {
    value: 'OpcUA',
    desc: 'OpcUA'
  },
  BLE: {
    value: 'BLE',
    desc: 'BLE'
  },
  ZIGBEE: {
    value: 'ZigBee',
    desc: 'ZigBee'
  }
};

//数据状态
export const DATA_STATUS_ENUM = {
  DEVELOPING: {
    value: 0,
    desc: '开发中'
  },
  ONLINE: {
    value: 1,
    desc: '已上线'
  }
};


//数据格式
export const DATA_FORMAT_ENUM = {
  ALINK: {
    value: 'alink',
    desc: '阿里ALink'
  },
  CUSTOM: {
    value: 'custom',
    desc: '自定义'
  }
};


//设备类型
export const DEVICE_TYPE_ENUM = {
  DIRECT: {
    value: 'direct_connect',
    desc: '直连设备'
  },
  GATEWAY: {
    value: 'gateway',
    desc: '网关设备'
  },
  CHILD: {
    value: 'gateway_child',
    desc: '网关子设备'
  }
};


//联网方式
export const CONNECTION_MODE_ENUM = {
  WIFI: {
    value: 'wifi',
    desc: 'WiFi'
  },
  CELLULAR: {
    value: 'cellular_network',
    desc: '蜂窝(2G/3G/4G/5G)'
  },
  ETHERNET: {
    value: 'ethernet',
    desc: '以太网'
  },
  LORA: {
    value: 'lora',
    desc: 'LoRaWAN'
  },
  OTHER: {
    value: 'other',
    desc: '其他'
  }
};

// 功能类型
export const FUNCTION_TYPE_ENUM = {
  PROPERTY: {
    value: 1,
    desc: 'property',
    color: '#108ee9'
  },
  COMMAND: {
    value: 2,
    desc: 'command',
    color: '#87d068'
  },
  EVENT: {
    value: 3,
    desc: 'event',
    color: '#2db7f5'
  }
};

// 字段类型
export const FIELD_TYPE_ENUM = [
  { type: 'int32', name: '整数型' },
  { type: 'float', name: '单精度浮点型' },
  { type: 'double', name: '双精度浮点型' },
  { type: 'text', name: '字符串' },
  { type: 'enum', name: '枚举型' },
  { type: 'bool', name: '布尔型' },
  { type: 'date', name: '时间型' },
  { type: 'array', name: '数组' },
  { type: 'struct', name: '结构体' }
];

// 事件类型
export const EVENT_TYPE_ENUM = {
  INFO: {
    value: 'info',
    desc: '信息'
  },
  ALERT: {
    value: 'alert',
    desc: '告警'
  },
  ERROR: {
    value: 'error',
    desc: '故障'
  }
};

// 调用方式
export const CALL_TYPE_ENUM = {
  ASYNC: {
    value: 'async',
    desc: '异步'
  },
  SYNC: {
    value: 'sync',
    desc: '同步'
  }
};

/**
 * 获取功能类型名称
 * @param {number} type 功能类型值
 * @returns {string} 功能类型名称
 */
export const getFunctionTypeName = (type) => {
  for (const key in FUNCTION_TYPE_ENUM) {
    if (FUNCTION_TYPE_ENUM[key].value === type) {
      return FUNCTION_TYPE_ENUM[key].desc;
    }
  }
  return '';
};

/**
 * 获取功能类型颜色
 * @param {number} type 功能类型值
 * @returns {string} 功能类型对应的颜色
 */
export const getFunctionTypeColor = (type) => {
  for (const key in FUNCTION_TYPE_ENUM) {
    if (FUNCTION_TYPE_ENUM[key].value === type) {
      return FUNCTION_TYPE_ENUM[key].color;
    }
  }
  return '';
};

/**
 * 获取数据类型描述
 * @param {string} fieldType 字段类型
 * @returns {string} 字段类型描述
 */
export const getDataTypeDesc = (fieldType) => {
  const item = FIELD_TYPE_ENUM.find(item => item.type === fieldType);
  return item ? item.name : fieldType;
};

/**
 * 获取数据定义描述
 * @param {Object} specFunction 功能规格对象
 * @returns {string} 数据定义描述
 */
export const getDataSpec = (specFunction) => {
  if (!specFunction) return '';
  
  const { fieldType, specMin, specMax, specBoolTrue, specBoolFalse } = specFunction;
  
  if (fieldType === 'bool') {
    return `布尔值: ${specBoolFalse}/${specBoolTrue}`;
  } else if (fieldType === 'int32' || fieldType === 'float' || fieldType === 'double') {
    return `取值范围: ${specMin} ~ ${specMax}`;
  } else if (fieldType === 'text') {
    return `数据长度: ${specFunction.specLength}`;
  } else if (fieldType === 'enum') {
    return `枚举值: (${specFunction.specEnum})`;
  } else if (fieldType === 'array') {
    return `${specFunction.specItemType}: 长度 ${specFunction.specSize}`;
  }
  
  return '';
};

/**
 * 获取事件类型描述
 * @param {string} eventType 事件类型值
 * @returns {string} 事件类型描述
 */
export const getEventTypeDesc = (eventType) => {
  for (const key in EVENT_TYPE_ENUM) {
    if (EVENT_TYPE_ENUM[key].value === eventType) {
      return EVENT_TYPE_ENUM[key].desc;
    }
  }
  return eventType;
};

/**
 * 获取调用方式描述
 * @param {string} callType 调用方式值
 * @returns {string} 调用方式描述
 */
export const getCallTypeDesc = (callType) => {
  for (const key in CALL_TYPE_ENUM) {
    if (CALL_TYPE_ENUM[key].value === callType) {
      return CALL_TYPE_ENUM[key].desc;
    }
  }
  return callType;
};

/**
 * 获取数据类型名称（包含类型说明）
 * @param {Object} data 功能数据对象
 * @returns {string} 格式化后的数据类型名称
 */
export const getFieldTypeName = (data) => {
  const functionType = data.functionType;
  if (functionType === 1) {
    const fieldType = data.specFunction.fieldType;
    const item = FIELD_TYPE_ENUM.find(item => item.type === fieldType);
    return item ? `${item.type}(${item.name})` : "-";
  }
  return "-";
};

/**
 * 获取数据定义名称
 * @param {Object} data 功能数据对象
 * @returns {string} 格式化后的数据定义
 */
export const getDataTypeName = (data) => {
  const functionType = data.functionType;
  if (functionType === 1) {
    return getDataSpec(data.specFunction);
  } else if (functionType === 2) {
    return `调用方式: ${getCallTypeDesc(data.callType)}`;
  } else if (functionType === 3) {
    return `事件类型: ${getEventTypeDesc(data.eventType)}`;
  }
  return '-';
};

export default {
  GATEWAY_PROTOCOL_ENUM,//网关协议
  DATA_STATUS_ENUM,//数据状态
  DATA_FORMAT_ENUM,//数据格式
  DEVICE_TYPE_ENUM,//设备类型
  CONNECTION_MODE_ENUM,//联网方式
  FUNCTION_TYPE_ENUM,//功能类型
  FIELD_TYPE_ENUM,//字段类型
  EVENT_TYPE_ENUM,//事件类型
  CALL_TYPE_ENUM,//调用方式
  getFunctionTypeName,//获取功能类型名称
  getFunctionTypeColor,//获取功能类型颜色
  getDataTypeDesc,//获取数据类型描述
  getDataSpec,//获取数据定义描述
  getEventTypeDesc,//获取事件类型描述
  getCallTypeDesc,//获取调用方式描述
  getFieldTypeName,//获取数据类型名称
  getDataTypeName//获取数据定义名称
};