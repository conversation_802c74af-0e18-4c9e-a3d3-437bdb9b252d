<template>
    <a-tabs v-model:activeKey="logActiveKey" type="card">
        <a-tab-pane key="deviceLog" tab="设备日志">
            <a-table size="small" :dataSource="sortedConnetMessageData" :columns="deviceColumns" bordered
                :pagination="false" :scroll="{ y: 600 }">
            </a-table>
        </a-tab-pane>
        <a-tab-pane key="cloudLog" tab="云端日志">
            <div style="margin-bottom: 16px">
                <a-input v-model:value="queryForm.topic" placeholder="请输入topic查询"
                    style="width: 300px; margin-right: 16px" />
                <a-range-picker :presets="defaultTimeRanges" style="width: 240px; margin-left: 6px" v-model:value="reportTime"
                    @change="onChangeDate" />
                <a-button type="primary" @click="queryData" style="margin-left: 8px">搜索</a-button>
                <a-button style="margin-left: 8px" @click="handleClear">清空</a-button>
            </div>
            <a-table size="small" :dataSource="cloudLogData" :columns="cloudColumns" bordered rowKey="id"
                :pagination="false" :scroll="{ y: 600 }">
            </a-table>
            <div class="smart-query-table-page">
                <a-pagination showSizeChanger showQuickJumper show-less-items :pageSizeOptions="PAGE_SIZE_OPTIONS"
                    :defaultPageSize="queryForm.size" v-model:current="queryForm.current"
                    v-model:pageSize="queryForm.size" :total="total" @change="queryData" @showSizeChange="queryData"
                    :show-total="(total) => `共${total}条`" />
            </div>
        </a-tab-pane>
    </a-tabs>
</template>

<script setup>
// 导入依赖
import { defaultTimeRanges } from '/@/lib/default-time-ranges.js';
import { ref, reactive, computed, onMounted } from 'vue';
import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
import { iotDeviceApi } from '/@/api/business/iot/device/iot-device-api.js';
import { smartSentry } from '/@/lib/smart-sentry';

// Props 定义
const props = defineProps({
    connetMessageData: {
        type: Array,
        default: () => []
    },
    deviceId: {
        type: String,
        default: ''
    }
});

// 响应式数据
const logActiveKey = ref('deviceLog');
const total = ref(0);
const cloudLogData = ref([]); // 云端日志数据
const reportTime = ref([]);

// 查询表单状态
const queryFormState = reactive({
    current: 1,
    size: 10,
    topic: undefined,
    deviceId: props.deviceId,
    startTime: undefined,
    endTime: undefined
});

const queryForm = reactive({
    ...queryFormState
});

// 表格配置
const deviceColumns = [
    {
        title: '#',
        dataIndex: 'seq',
        width: 50,
    },
    {
        title: '更新时间',
        dataIndex: 'updateTime',
        width: 150,
    },
    {
        title: '类型',
        dataIndex: 'type',
        width: 120
    },
    {
        title: '消息内容',
        dataIndex: 'message',
        width: 300
    }
];

const cloudColumns = [
    {
        title: '#',
        dataIndex: 'seq',
        width: 50
    },
    {
        title: '更新时间',
        dataIndex: 'time',
        width: 150,
    },
    {
        title: 'topic',
        dataIndex: 'topic',
        width: 200
    },
    {
        title: '消息内容',
        dataIndex: 'message',
        width: 300,
       
    }
];
function onChangeDate(dates,dateString) {
    queryForm.startTime = dates[0];
    queryForm.endTime = dates[1];
}

// 计算属性
const sortedConnetMessageData = computed(() => {
    if (!props.connetMessageData || props.connetMessageData.length === 0) {
        return [];
    }

    // 先对数据进行时间排序（新的在前面）
    const sorted = [...props.connetMessageData].sort((a, b) => {
        const aTime = new Date(a.updateTime).getTime();
        const bTime = new Date(b.updateTime).getTime();
        return bTime - aTime; // 降序排列，新的时间在前
    });

    // 重新分配序号（新数据序号小）
    return sorted.map((item, index) => ({
        ...item,
        seq: index + 1
    }));
});

// 方法定义
async function queryData() {
    try {
        const res = await iotDeviceApi.getCloudLog(queryForm);
        cloudLogData.value = res.data.records.map((item, index) => ({
            ...item,
            seq: index + 1
        }));
        total.value = res.data.total;
    } catch (error) {
        smartSentry.captureError(error);
    }
}

const handleClear = () => {
    Object.assign(queryForm, queryFormState);
    reportTime.value = [];
    queryData();
};

onMounted(() => {
    queryData();
});
</script>