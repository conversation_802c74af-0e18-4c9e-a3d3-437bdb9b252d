<!--
  * 素材管理
  *
  * @Author:    谢志豪
  * @Date:      2025-04-05 15:10:25
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
  <a-modal :title="form.id ? '编辑' : '添加'" :width="1000" :open="visibleFlag" @cancel="onClose" :maskClosable="false"
    :destroyOnClose="true">
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }">
      <a-row>
        <a-col :span="12">
          <a-form-item label="素材名称" name="name">
            <a-input style="width: 100%" v-model:value="form.name" placeholder="素材名称" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="素材类型" name="type">
            <DictSelect width="100%" v-model:value="form.type" keyCode="ASSETS_TYPE" placeholder="素材类型" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="存储路径/URL" name="fileKey" >
            <FileUpload :defaultFileList="form.fileKey" :folder="FILE_FOLDER_TYPE_ENUM.COMMON.value"
              buttonText="上传 存储路径/URL" listType="text" @change="(e) => (form.fileKey = e)" disabled />
          </a-form-item>
        </a-col>
        <!-- <a-col :span="12">
          <a-form-item label="文件大小(字节)" name="fileSize">
            <a-input-number style="width: 100%" v-model:value="form.fileSize" placeholder="文件大小(字节)" />
          </a-form-item>
        </a-col> -->
        <a-col :span="12">
          <a-form-item label="文件格式" name="fileFormat">
            <a-input style="width: 100%" v-model:value="form.fileFormat" placeholder="文件格式" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="素材时长(秒)" name="duration">
            <a-input-number style="width: 100%" v-model:value="form.duration" placeholder="素材时长(秒)" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="审核状态" name="status">
            <a-select v-model:value="form.status" allowClear placeholder="请选择审核状态"
              :options="MEDIA_STATUS_ENUM.getOptions()">
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="审核意见" name="reviewComment">
            <a-textarea style="width: 100%" v-model:value="form.reviewComment" placeholder="审核意见" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="上传人" name="createUserId">
            <a-input-number style="width: 100%" v-model:value="form.createUserId" placeholder="上传人" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { reactive, ref, nextTick,watch} from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { mediaMaterialApi } from '/@/api/business/media/material/media-material-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  import FileUpload from '/@/components/support/file-upload/index.vue';
  import DictSelect from '/@/components/support/dict-select/index.vue';
  import { FILE_FOLDER_TYPE_ENUM } from '/@/constants/support/file-const';
import { MEDIA_STATUS_ENUM } from '/@/constants/business/media/material/media-material-const'

  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

function showForm(videoData, totalDuration) {
    form.fileKey.push(videoData);
    form.duration = totalDuration;
  form.fileFormat = videoData.fileType;
    visibleFlag.value = true;
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
    name: undefined, //素材名称
    type: 'VIDEO', //素材类型
    fileKey: [], //存储路径/URL
    fileSize: undefined, //文件大小(字节)
    fileFormat: undefined, //文件格式
    resolution: undefined, //分辨率(宽x高)
    duration: undefined, //素材时长(秒)
    status: undefined, //审核状态
    reviewComment: undefined, //审核意见
    createUserId: undefined, //上传人
  };

  let form = reactive({ ...formDefault });

  const rules = {
    name: [{ required: true, message: '素材名称 必填' }],
    type: [{ required: true, message: '素材类型 必填' }],
    fileKey: [{ required: true, message: '存储路径/URL 必填' }],
    status: [{ required: true, message: '审核状态 必填' }],
    createUserId: [{ required: true, message: '上传人 必填' }],
  };

  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      if (form.materialId) {
        await mediaMaterialApi.update(form);
      } else {
        await mediaMaterialApi.add(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  defineExpose({
    showForm,
  });
</script>
