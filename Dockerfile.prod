## 设置基础镜像
#FROM nginx
## 定义作者
#MAINTAINER cjm
#
#COPY /nginx/nginx.conf /etc/nginx/nginx.conf
#
## 将dist文件中的内容复制到 /usr/share/nginx/html/ 这个目录下面
#COPY dist/  /usr/share/nginx/html/
#----------------------------------------------

#
FROM node:alpine as builder

WORKDIR /app

# 确保路径正确
COPY package.json .

RUN npm install --registry=https://mirrors.cloud.tencent.com/npm --legacy-peer-deps

# 复制项目文件
COPY . .

#选择构建环境
RUN npm run build:prod


# 打包项目

FROM nginx:1.15.2-alpine

# Nginx config
COPY nginx/nginx.conf /etc/nginx/nginx.conf
COPY --from=builder /app/dist /usr/share/nginx/html/

EXPOSE 80


#CMD ["nginx", "-g", "daemon off;"]
