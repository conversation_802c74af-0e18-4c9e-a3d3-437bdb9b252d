/**
 * 设备表 枚举
 *
 * @Author:    panxz
 * @Date:      2025-05-06 22:21:07
 * @Copyright  中山睿数信息技术有限公司 2025
 */

export const MEDIA_TYPE_ENUM = {
    VIDEO: { value: 'VIDEO', desc: '视频', label: '视频' },
    IMAGE: { value: 'IMAGE', desc: '图片', label: '图片' },

    // 获取所有选项
    getOptions() {
        return Object.values(this).filter(item => typeof item === 'object');
    },

    // 根据值获取描述
    getDesc(value) {
        const type = Object.values(this).find(item => typeof item === 'object' && item.value === value);
        return type?.desc || '';
    }
};

export const MEDIA_STATUS_ENUM = {
    PENDING: { value: 'PENDING', desc: '未提交', label: '未提交' },
    APPROVED: { value: 'APPROVED', desc: '审核通过', label: '审核通过' },
    REJECTED: { value: 'REJECTED', desc: '审核不通过', label: '审核不通过' },
    SUBMITTED: { value: 'SUBMITTED', desc: '待审核', label: '待审核' },
    ISSUED: { value: 'ISSUED', desc: '已下达', label: '已下达' },
    COMPLETED: { value: 'COMPLETED', desc: '已结束', label: '已结束' },
    // 获取所有选项
    getOptions() {
        return Object.values(this).filter(item => typeof item === 'object');
    },

    // 根据值获取描述
    getDesc(value) {
        const status = Object.values(this).find(item => typeof item === 'object' && item.value === value);
        return status?.desc || '';
    }
};