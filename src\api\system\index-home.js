import { getRequest , postRequest} from '/@/lib/axios';

export const indexHomeApi = {
    /**
     * 分页查询区域报警信息 <AUTHOR>
     */
    queryRegionWarnPage: (params) => {
      return postRequest('/maintainFault/queryRegionWarnPage', params);
    },
    /**
   * 首页顶部数据查询
   */
  homeTopData: (params) => {
    return postRequest('/home/<USER>/state',params);
  },
    /**
   * 数据视窗 <AUTHOR>
   */
    queryDeviceOnlineData: () => {
      return getRequest('/iotDevice/queryDeviceOnlineData');
   },
  
  };