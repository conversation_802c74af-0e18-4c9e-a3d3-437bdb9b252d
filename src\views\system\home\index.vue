<template>
  <!--  顶部用户信息-->
  <a-row>
    <HomeHeader />
  </a-row>

  <div style="display: flex;">
    <div class="left-container">
      <a-card title="警告卡片">
        <div class="order-container">
          <a-list
            class="order-list"
            :grid="{ gutter: 16, xs: 1, sm: 2, md: 2, lg: 2, xl: 3 ,xxl: 3 }"
            :data-source="orderData"
            :loading="loading"
            :pagination="{
              ...pagination,
              style: { marginTop: '10px' },
            }"
          >
            <template #renderItem="{ item, index }">
              <a-list-item>
                <a-card
                  hoverable
                  :class="['order-card', getStatusClass(item.status)]"
                  @click="handleCardClick(item)"
                >
                  <template #title>
                    <a class="order-title">工单编号: {{ item.orderNumber }}</a>
                  </template>
                  <template #extra>
                    <span class="order-number">No.{{ index + 1 }}</span>
                  </template>
                  
                  <div class="order-content">
                    <div class="order-details">
                      <span class="detail-item">负责人: {{ item.maintainer }}</span>
                      <span class="detail-item">处理进度: {{ item.progress}}%</span>
                      <span class="detail-item">派单时间: {{ item.createTime }}</span>
                      <span class="detail-item">所在区域: {{ item.regionName }}</span>
                      <span class="detail-item">涉及设备: {{ item.deviceName }}</span>
                    </div>
                    
                    <a-steps
                      :current="getCurrentStep(item)"
                      size="small"
                      direction="horizontal"
                      class="order-steps"
                      style="margin-top: 16px; transform: scale(0.7); transform-origin: left center;  width: 130%;"
                    >
                      <a-step 
                        v-for="(status, key) in filteredWorkOrderStatus"
                        :key="key"
                        :title="status.desc"
                      />
                    </a-steps>
                  </div>
                </a-card>
              </a-list-item>
            </template>
          </a-list>
        </div>
      </a-card>
    </div>
    <div class="right-container">
      <a-card title="快捷导航" class="nav-card">
        <!-- <template v-slot:extra>
          <a href="#" target="_blank">设置</a>
        </template> -->
        <a-row :gutter="[6, 6]" class="grid-row">
          <a-col v-for="item in navItems" :key="item.key" :span="8" :xs="24" :sm="4" :md="4" :lg="4" :xl="4" :xxl="8" class="grid-col">
            <a-card hoverable class="grid-item" @click="handleClick(item.key)">
              <div class="grid-content">
                <component :is="item.icon" class="nav-icon" />
                <span class="nav-title">{{ item.title }}</span>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </a-card>
      <a-card title="公告" class="notice-card">
        <!-- <template v-slot:extra>
          <a href="#" target="_blank">更多数据</a>
        </template> -->
        
        <a-carousel 
          vertical
          :dots="false"
          :autoplay="true"
          :autoplay-speed="1500"
          :slides-toShow="4"
          class="vertical-carousel"
          style="margin-top: 4px;"
        >
          <div v-for="(item, index) in visibleNotices" :key="index">
            <a-list-item>
              <a-list-item-meta>
                <template #title>
                  <span class="notice-title">针对[{{item.regionName}}]区域，{{ item.maintainer }}<span style="color: #41b883;">{{findDescByValue(item.status, WORK_ORDER_STATUS_ENUM)}}</span>,其工单编号为[{{ item.orderNumber }}]</span>
                </template>
                <template #description>
                  <div class="notice-meta">
                    <span class="notice-source">{{ item.orderNumber }}</span>
                    <span class="notice-time">{{ item.createTime }}</span>
                  </div>
                </template>
              </a-list-item-meta>
            </a-list-item>
          </div>
        </a-carousel>
      </a-card>
    </div>
  </div>
  
  <!-- 模态框 -->
  <a-modal
    v-model:visible="modalVisible"
    title=""
    :footer="null"
    width="800px"
    @cancel="handleModalCancel"
    @ok="handleModalOk"
  >
    <a-card v-if="selectedOrder" :title="`工单编号: ${selectedOrder.orderNumber}`">
      <a-form ref="formRef" :model="form" :label-col="{ span: 5 }">
        <a-form-item label="维修单">
          <a-input
            v-model:value="form.faultId"
            placeholder="选择设备"
            disabled
          />
        </a-form-item>
        <a-form-item label="工单编号">
          <a-input
            style="width: 100%" 
            v-model:value="form.orderNumber" 
            placeholder="请输入工单编号"
            disabled
          />
        </a-form-item>
        <a-form-item label="维修人">
          <a-select
            v-model:value="form.maintainerId"
            placeholder="选择维修人"
            :options="maintainerOptions"
            :allowClear="true"
            @change="handleMaintainerChange"
          />
        </a-form-item>
        <a-form-item label="维修情况">
          <a-textarea 
            style="width: 100%" 
            v-model:value="form.faultRemark" 
            placeholder="请输入维修情况说明"
            :rows="4"
          />
        </a-form-item>
        <a-form-item label="维修情况照片">
          <UploadImg
            :default-file-list="form.situationImg"
            @change="(fileList) => form.situationImg = fileList"
          />
        </a-form-item>

      </a-form>
      
      <a-steps
        :current="getCurrentStep(selectedOrder)"
        size="small"
        direction="horizontal"
        class="modal-steps"
        style="margin-top: 16px;"
      >
        <a-step
          v-for="(status, key) in filteredWorkOrderStatus"
          :key="key"
          :title="status.desc"
          @click="handleStepClick(status)"
        />
      </a-steps>
    
    </a-card>
    <a-space>
      <a-button type="primary" @click="handleSubmit">保存</a-button>
      <a-button @click="modalVisible = false">取消</a-button>
    </a-space>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue';
import UploadImg from '/@/components/support/file-upload/index.vue';
import { WORK_ORDER_STATUS_ENUM } from '/@/constants/work-order/maintain-order-const';
import { useRouter } from 'vue-router';
import { computed, ref, watch } from 'vue';
import HomeHeader from './home-header.vue';
import { homeApi } from '/@/api/system/home-api.js';
import { employeeApi } from '/@/api/system/employee-api';
import { maintainOrderApi } from '/@/api/business/work-order/maintain-order-api.js';
import { 
  HomeOutlined,
  ClusterOutlined,
  EnvironmentOutlined,
  BulbOutlined,
  DashboardOutlined,
  FileTextOutlined,
  ShareAltOutlined,
  HistoryOutlined,
  BarChartOutlined
} from '@ant-design/icons-vue';

const router = useRouter();
const formRef = ref();
const modalVisible = ref(false);
const selectedOrder = ref(null);
const form = ref({
  faultId: undefined,
  orderNumber: undefined,
  maintainer: undefined,
  maintainerId: undefined,
  faultRemark: '',
  situationImg: []
});

// 分页参数
const paginationParams = ref({
  pageNum: 1,
  pageSize: 9
});

// 维修人选项
const maintainerOptions = ref([]);
// 获取维修人下拉框列表
async function queryMaintainer(){
  try {
    const res = await employeeApi.queryAll({
      disabledFlag: false,
    });
    maintainerOptions.value = res.data.map((e) => {
      return {
        label: e.actualName,
        value: e.employeeId,
      }
    })
  } catch (err) {
    message.error(err);
  }
}
queryMaintainer();

// 获取选择的员工
function handleMaintainerChange(value) {
  const selectedMaintainer = maintainerOptions.value.find(option => option.value === value);
  if (selectedMaintainer) {
    form.value.maintainer = selectedMaintainer.label;
  }
}

// 获取工单详情
const getProductDetail = async () => {
  try {
    loading.value = true;
    const response = await homeApi.homeWorkOrder(paginationParams.value);
    orderData.value = response.data.list; // 确保返回的 list 是一个数组
    pagination.value.total = response.data.total; // 确保 total 是一个有效的数字
    loading.value = false;
  } catch (error) {
    loading.value = false;
    message.error('获取详情失败，请稍后重试');
  }
};


// 分页变化处理
const handleTableChange = (pag) => {
  paginationParams.value.pageNum = pag;
  getProductDetail();
};

const orderData = ref([]);
const loading = ref(false);
const showProgress = ref(true);

// 分页配置
const pagination = computed(() => ({
  current: paginationParams.value.pageNum,
  pageSize: paginationParams.value.pageSize,
  total: 0, // 初始为0，从接口获取后更新
  showSizeChanger: false,
  showQuickJumper: true,
  showTotal: total => `共 ${total} 条工单`,
  onChange: handleTableChange,
}));

// 过滤掉未查阅状态的步骤
const filteredWorkOrderStatus = computed(() => {
  return Object.entries(WORK_ORDER_STATUS_ENUM)
    .filter(([key, value]) => value.value !== 0)
    .reduce((obj, [key, value]) => {
      obj[key] = value;
      return obj;
    }, {});
});

// 根据状态获取卡片样式类
const getStatusClass = (status) => {
  if (status === 5) {
    return 'status-completed'; // 已完成 - 绿色
  } else if (status === 0 || status === 1) {
    return 'status-pending'; // 待处理 - 灰色
  } else {
    return 'status-processing'; // 处理中 - 蓝色
  }
};

// 获取步骤条状态
const getCurrentStep = (item) => {
  const statusToProgressMap = {0: 0, 1: 0, 2: 1, 3: 2, 4: 3, 5: 4};
  item.progress = statusToProgressMap[item.status] * 25 || 0;
  return statusToProgressMap[item.status] || 0;
};

// 处理卡片点击
const handleCardClick = async (item) => {
  selectedOrder.value = item;
  
  // 填充表单数据
  form.value = {
    faultId: item.faultId,
    orderNumber: item.orderNumber,
    maintainer: item.maintainer,
    maintainerId: item.maintainerId,
    faultRemark: item.faultRemark || '',
    situationImg: item.situationImg || []
  };
  modalVisible.value = true;
};

// 处理模态框取消
const handleModalCancel = () => {
  formRef.value?.resetFields();
};

// 处理模态框确定
const handleModalOk = () => {
  handleSubmit();
};

// 处理表单提交
const handleSubmit = async () => {
  try {
    const params = {
      id: selectedOrder.value.id,
      ...form.value
    };
    await maintainOrderApi.update(params);
    message.success('工单更新成功');
    getProductDetail();
    modalVisible.value = false;
  } catch (error) {
    message.error('工单更新失败');
  }
};

// 处理步骤条点击事件
const handleStepClick = (status) => {
  console.log('当前工单 ID:', selectedOrder.value.id);
  console.log('当前状态:', selectedOrder.value.status);
  console.log('目标状态:', status.value);
  // 状态1 → 状态2：选择维修人
  if (selectedOrder.value.status === 1 && status.value === 2) {
    if (!selectedOrder.value.maintainerId) {
      message.error('请先选择维修人并保存后再操作');
      return;
    }
    updateOrderStatus(selectedOrder.value.id, 2);
    modalVisible.value = false;
  }
  // 状态2 → 状态3：直接更新
  else if (selectedOrder.value.status === 2 && status.value === 3) {
    updateOrderStatus(selectedOrder.value.id, 3);
    modalVisible.value = false;
  }
  // 状态3 → 状态4：检查维修情况和照片
  else if (selectedOrder.value.status === 3 && status.value === 4) {
    if (!selectedOrder.value.faultRemark || !selectedOrder.value.situationImg?.length) {
      message.error('请先填写维修情况并上传照片后再操作');
      return;
    }
    updateOrderStatus(selectedOrder.value.id, 4);
    modalVisible.value = false;
  }
   // 状态4 → 状态5：直接更新
   else if (selectedOrder.value.status === 4 && status.value === 5) {
    updateOrderStatus(selectedOrder.value.id, 5);
    modalVisible.value = false;
  }
  // // 其他不允许的状态跳转
  // else {
  //   message.error('当前是未查询状态，请选择维修人，并查询信息后再进行派工');
  // }
};

// 更新步骤条状态
const updateOrderStatus = async (id, status) => {
  try {
    await maintainOrderApi.updateStatus({ id, status });
    message.success('状态更新成功');
    getProductDetail();
  } catch (error) {
    message.error('状态更新失败');
  }
};

// 通知数据
const notices = ref([]);
const visibleNotices = computed(() => {
  const count = Math.max(5, notices.value.length);
  return Array.from({ length: count }, (_, i) => 
    notices.value[i % notices.value.length] || {}
  );
});

const getNoticeData = async () => {
  try {
    const response = await homeApi.queryWorkOrderLog();
    notices.value = response.data;
  } catch (error) {
    message.error('获取通知失败');
  }
};

// 返回键值
function findDescByValue(value, enumObject) {
  for (const key in enumObject) {
    if (enumObject[key].value === value) {
      return enumObject[key].desc;
    }
  }
  return null;
}

// 导航项数据
const navItems = ref([
  { key: '/home', title: '首页', icon: HomeOutlined },
  { key: '/media/statistics', title: '数据分析', icon: BarChartOutlined },
  { key: '/edge/gateway', title: '网络设备', icon: ClusterOutlined },
  { key: '/controlMap', title: '区域地图', icon: EnvironmentOutlined },
  { key: '/light-scene-list', title: '路灯详情', icon: BulbOutlined },
  { key: '/Operation/energy', title: '设备能耗', icon: DashboardOutlined },
  { key: '/Operation/env', title: '文件审核', icon: FileTextOutlined },
  { key: '/maintain/working-calendar', title: '数据共享', icon: ShareAltOutlined },
  { key: '/iot/deviceLog', title: '设备日志', icon: HistoryOutlined },
]);

const handleClick = (key) => {
  router.push({ path: key });
};

// 初始化数据
getProductDetail();
getNoticeData();
</script>
<style lang="less" scoped>
.left-container {
  flex: 2.3;
  margin-top: 10px;
  margin-right: 20px;
}

.right-container {
  flex: 1;
  margin-top: 10px;
}

.nav-card {
  border-radius: 8px;
  padding-bottom: 15%;
}

.notice-card {
  margin-top: 10px;
}

.nav-menu {
  border-right: none;
}

.notice-title {
  font-weight: 700;
  color: rgba(0, 0, 0, 0.85);
}

.notice-meta {
  display: flex;
  justify-content: space-between;
}

.notice-source {
  color: #888;
}

.notice-time {
  color: #999;
}

:deep(.ant-list-item) {
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 0;
}

.grid-item {
  height: 100%;
  border-radius: 6px;
  transition: all 0.3s;
  text-align: center;
  cursor: pointer;
}

.grid-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-4px);
  background-color: #edf2fa;
}

.grid-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.nav-icon {
  font-size: 24px;
  margin-bottom: 12px;
  color: #1890ff;
}

.nav-title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
}

.order-container {
  height: 100%;
}

.order-card {
  height: 100%;
  transition: all 0.3s;
  position: relative;
}

.order-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.order-title {
  color: #1890ff;
}

.order-number {
  position: absolute;
  right: 40px;
  top: 16px;
  color: #888;
}

.order-content {
  display: flex;
  flex-direction: column;
  height: calc(100% - 50px);
}

.order-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.detail-item {
  margin-bottom: 8px;
  color: #666;
  font-size: 13px;
}

.order-steps {
  width: 100%;
  margin-top: 8px;
}

/* 状态背景色 */
.order-card.status-pending {
  background: linear-gradient(rgba(150, 150, 150, 0.1), white);
  // border-left: 4px solid #ccc;
}

.order-card.status-processing {
  background: linear-gradient(rgba(24, 144, 255, 0.1), white);
  // border-left: 4px solid #1890ff;
}

.order-card.status-completed {
  background: linear-gradient(rgba(82, 196, 26, 0.1), white);
  // border-left: 4px solid #52c41a;
}

/* 滚动条样式 */
.order-list::-webkit-scrollbar {
  width: 6px;
}

.order-list::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.order-list::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.05);
}

.modal-content {
  padding: 16px;
}

.modal-steps {
  margin-top: 16px;
}
</style>