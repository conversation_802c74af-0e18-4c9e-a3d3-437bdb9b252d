variables: #变量
  #网关
  MES_FRONT_CONTAINER: 'wiselume-web-cicd'
  MES_FRONT_IMAGE: 'wiselume-web-cicd'
  MES_FRONT_PORT: 7777

stages:
  - deploy
# 设置缓存

cache:
  paths:
    - node_modules/
    - dist/

deploy-test:
  stage: deploy
  image: docker:27.4.0 #目的是为了在docker环境下的runner能执行docker命令
  script:
    #服务
    - docker ps -a|grep $MES_FRONT_CONTAINER &&  docker stop $MES_FRONT_CONTAINER && docker rm $MES_FRONT_CONTAINER || echo "not exist"
    - docker images |grep  $MES_FRONT_IMAGE && docker rmi -f $MES_FRONT_IMAGE || echo  "not exist"
    - docker build -f Dockerfile.dev -t $MES_FRONT_IMAGE .
    - docker run -d --restart=always -p $MES_FRONT_PORT:80 --log-opt max-size=50m --log-opt max-file=3 --name $MES_FRONT_CONTAINER $MES_FRONT_IMAGE
  only:
    - dev
  tags:
    - dev

deploy-prod:
  stage: deploy
  image: docker:27.4.0 #目的是为了在docker环境下的runner能执行docker命令
  script:
    #服务
    - docker ps -a|grep $MES_FRONT_CONTAINER &&  docker stop $MES_FRONT_CONTAINER && docker rm $MES_FRONT_CONTAINER || echo "not exist"
    - docker images |grep  $MES_FRONT_IMAGE && docker rmi -f $MES_FRONT_IMAGE || echo  "not exist"
    - docker build -f Dockerfile.prod -t $MES_FRONT_IMAGE .
    - docker run -d --restart=always -p $MES_FRONT_PORT:80 --log-opt max-size=50m --log-opt max-file=3 --name $MES_FRONT_CONTAINER $MES_FRONT_IMAGE
  only:
    - main
  tags:
    - prod

