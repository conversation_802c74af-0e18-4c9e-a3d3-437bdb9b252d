import func from '/@/utils/func';

/**
 * 小数点精度判断
 * @param value  要检查的数值
 * @param  precision 允许的最大小数位数
 * @returns {boolean}
 */
export const checkPrecision = (value, precision) => {
    const valueString = value.toString();
    const decimalPointIndex = valueString.indexOf('.');
    if (decimalPointIndex !== -1) {
      const decimalPartLength = valueString.length - decimalPointIndex - 1;
      if (decimalPartLength > precision) {
        return false;
      }
    }
    return true;
  };

/**
 * 基本类型数据
 * @param fieldType 数据类型
 * @param value 数据值
 * @returns {Promise<string|null>} 返回错误信息或null
 * 
 */
export const validateSpec = (fieldType, value) => {
    const numberFieldTypes = ['int32', 'float', 'double'];
    if (!numberFieldTypes.includes(fieldType)) {
        return '类型错误或者没有该类型';
      }
    value = Number(value);
    if (fieldType === 'int32' && !Number.isInteger(value)) {
        return '请输入正确的整数类型';
      }
      // 单精度浮点型
    if (fieldType === 'float' && !checkPrecision(value, 7)) {
        return '单精度数值格式错误（小数点有效位为 7 位）';
      }
      // 双精度浮点型
    if (fieldType === 'double' && !checkPrecision(value, 16)) {
        return '双精度数值格式错误（小数点有效位为 16 位）';
      }
    return null;
}
/**
 * 验证最小值
 */
export const validateSpecMin = (fieldType, specMin, specMax) => {
    specMin = func.toNumber(specMin);
    specMax = func.toNumber(specMax);
    // 基本类型验证
    const typeError = validateSpec(fieldType, specMin);
    if (typeError) {
      return typeError;
    }
    // 最小值不能大于最大值
    if (specMin > specMax) {
      return '最小值不能大于最大值';
    }
    return null;
  };
/**
 * 验证最大值
 */
export const validateSpecMax = (fieldType, specMin, specMax) => {
    specMin = func.toNumber(specMin);
    specMax = func.toNumber(specMax);
    // 基本类型验证
    const typeError = validateSpec(fieldType, specMax);
    if (typeError) {
      return typeError;
    } 
    // 最大值不能小于最小值
    if (specMax < specMin) {
      return '最大值不能小于最小值';
    }
    return null;
  };
/**
 * 验证步长
 */
export const validateSpecStep = (fieldType, specMin, specMax, specStep) => {
    specMin = func.toNumber(specMin);
    specMax = func.toNumber(specMax);
    specStep = func.toNumber(specStep);
    
    // 基本类型验证
    const typeError = validateSpec(fieldType, specStep);
    if (typeError) {
      return typeError;
    }
    
    // 步长不能大于最大值与最小值的差值
    if (specStep > specMax - specMin) {
      return '步长不能大于最大值与最小值的差值';
    }
    
    return null;
  };
  
  /**
   * 验证标识符格式
   */
  export const validateIdentifier = (value) => {
    // 正则表达式，以英文或数字开头，后续字符可以是英文、数字、下划线或短划线
    const regex = /^[a-zA-Z0-9][a-zA-Z0-9_-]{0,29}$/;
    // 验证是否是纯数字
    const isAllDigits = /^\d+$/;
    // 首先验证基本格式
    const isValidFormat = regex.test(value);
    // 确保不是纯数字
    const isNotAllDigits = !isAllDigits.test(value);
    
    if (!isValidFormat || !isNotAllDigits) {
      return '请确保您的输入以英文或数字开头，并且可包含数字、英文、下划线（_）和短划线（-），不能为纯数字，整体长度需控制在30个字符以内';
    }
    
    return null;
  };
  
  /**
   * 验证功能名称格式
   */
  export const validateName = (value) => {
    // 使用 Unicode 范围来匹配中文（\u4e00-\u9fa5）、日文字符（\u3040-\u30ff）
    const regex = /^[a-zA-Z0-9\u4e00-\u9fa5][a-zA-Z0-9\u4e00-\u9fa5\u3040-\u30ff\-_\/.]{0,29}$/;
    const isValid = regex.test(value);
    
    if (!isValid) {
      return '请确保输入以中文、英文字母或数字开头，并可包含中文、日文、英文字母、数字、短划线（-）、下划线（_）、斜杠（/）、点号（.），整体长度需控制在30个字符以内';
    }
    
    return null;
  };