/**
 * 工单 api 封装
 *
 * @Author:    yourName
 * @Date:      2025-04-11 11:45:56
 * @Copyright  bdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const maintainOrderApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/workOrder/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/workOrder/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/workOrder/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/workOrder/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
      return postRequest('/maintainOrder/batchDelete', idList);
  },
  /**
   * 修改工单状态  <AUTHOR>
   */
  updateStatus: (param) => {
      return postRequest('/workOrder/orderStatus', param);
  },
};
