<template>
  <a-card>
    <template #title>
      设备信息 => {{ deviceDetail.deviceName }}
    </template>
    <template #extra>
      <a-button @click="back">返回</a-button>
    </template>
    <a-descriptions :column="2" :bordered="false">
      <a-descriptions-item label="ProductName">
        {{ productDetail.productName }}
        <a-button type="link" size="small" @click="showProductDetail">查看</a-button>
      </a-descriptions-item>
      <a-descriptions-item label="DeviceName">
        {{ deviceDetail.deviceName }}
        <a-button type="link" size="small" @click="copyText(deviceDetail.deviceName)">复制</a-button>
      </a-descriptions-item>
      <a-descriptions-item label="ProductKey">
        {{ productDetail.productKey }}
        <a-button type="link" size="small" @click="copyText(productDetail.productKey)">复制</a-button>
      </a-descriptions-item>
      <a-descriptions-item label="DeviceSecret">
        {{ deviceDetail.deviceSecret }}
        <a-button type="link" size="small" @click="copyText(deviceDetail.deviceSecret)">复制</a-button>
      </a-descriptions-item>
    </a-descriptions>

    <a-tabs v-model:activeKey="activeKey" type="card" class="tabs-no-margin" >
      <a-tab-pane key="info" tab="设备信息">
        <DeviceInfo :productKey="productDetail.productKey" :deviceId="deviceDetail.id"
          :deviceName="deviceDetail.deviceName" />
      </a-tab-pane>
      <a-tab-pane key="topic" tab="Topic类别表">
        <DeviceTopic :routeData="routeData" />
      </a-tab-pane>
      <a-tab-pane key="tsl" tab="物理型数据">
        <DeviceModelTsl :routeData="routeData" />
      </a-tab-pane>
      <a-tab-pane key="group" tab="设备分组">
        <DeviceGroup :deviceId="deviceDetail.id" />
      </a-tab-pane>
      <a-tab-pane key="sub" tab="设备子集">
        <DeviceSubset :deviceId="deviceDetail.id" />
      </a-tab-pane>
      <a-tab-pane key="log" tab="设备日志">
        <DeviceLog :deviceId="deviceDetail.id" />
      </a-tab-pane>
      <a-tab-pane key="mqtt" tab="MQTT连接参数">
        <DeviceMqtt :routeData="routeData" />
      </a-tab-pane>
      <a-tab-pane key="location" tab="设备位置">
        <DevicePosition :deviceId="route.query.id" :latitude="latitude" :longitude="longitude" :latAndLon=" latAndLon"/>
      </a-tab-pane>
    </a-tabs>
  </a-card>
</template>

<script setup>
import { useRouter, useRoute } from 'vue-router';
import { useUserStore } from '/@/store/modules/system/user';
import { message } from 'ant-design-vue';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { ref, onMounted, watch,reactive } from 'vue';
import { iotDeviceApi } from '/@/api/business/iot/device/iot-device-api.js';
import { iotProductApi } from '/@/api/business/iot/product/iot-product-api.js';
// 组件引入
import DeviceInfo from './device-info.vue';
import DeviceTopic from './device-topic.vue';
import DeviceModelTsl from './device-modelTsl.vue';
import DeviceGroup from './device-group.vue';
import DeviceSubset from './device-subset.vue';
import DeviceLog from './device-log.vue';
import DeviceMqtt from './device-mqtt.vue';
import DevicePosition from './device-position.vue';

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();

const activeKey = ref('info');
const productDetail = ref({});
const deviceDetail = ref({});
const longitude = ref();
const latitude = ref();
const latAndLon = ref([]);

// 页面返回
const back = () => {
  router.push('/iot/device/list');
  userStore.closeTagNav(route.name, false);
};

// 跳转产品详情
const showProductDetail = () => {
  router.push({
    path: '/iot/product/detail', 
    query: {
      id: productDetail.value.id,
      name: productDetail.value.productName,
    },
  });
};

// 复制文本
const copyText = (text) => {
  navigator.clipboard.writeText(text)
    .then(() => message.success('复制成功'))
    .catch(() => message.error('复制失败'));
};

const routeState = {
  key: '',
  name: '示例设备名称',
  id: '',
}
const routeData = reactive({...routeState});
// 获取路由参数
const getRouteParams = () => {
  routeData.key = route.query.productKey;
  routeData.name = route.query.deviceName;
  routeData.id = route.query.id;
};

// 获取产品详情
const getProductDetail = async () => {
  try {
    SmartLoading.show();
    const response = await iotProductApi.getDetail({ productKey: routeData.key });
    productDetail.value = response.data;
  } catch (error) {
    message.error('获取产品详情失败');
  } finally {
    SmartLoading.hide();
  }
};

// 获取设备详情
const getDeviceDetail = async () => {
  try {
    SmartLoading.show();
    const response = await iotDeviceApi.queryDetailByName(routeData.name);
    deviceDetail.value = response.data;
    longitude.value = response.data.longitude;
    latitude.value = response.data.latitude;
    const res= await iotDeviceApi.getRegionByDevice(response.data.id);
    latAndLon.value= res.data.latAndLon;
  } catch (error) {
    message.error('获取设备详情失败');
  } finally {
    SmartLoading.hide();
  }
};

// 监听路由变化
watch(() => route.query, (newQuery) => {
  if (newQuery.id && route.path.includes('/iot/device/detail')) {
    getRouteParams();
    getProductDetail();
    getDeviceDetail();
  }
}, { immediate: true });

onMounted(() => {
  console.log(route.query);
  
});
</script>

<style lang="less" scoped>
.tabs-no-margin {
  margin: 0 !important;

  :deep(.ant-tabs-nav) {
    margin: 0;
  }
}
</style>
