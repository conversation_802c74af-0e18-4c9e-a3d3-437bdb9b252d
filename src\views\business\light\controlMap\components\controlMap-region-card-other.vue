<!--
  * 控制地图-设备卡片
  *
  * @Author:    骆伟林
  * @Date:      2025-03-26 17:51:27
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
  <a-list :data-source="processedLampList" style="height: 100%">
    <template #renderItem="{ item, index }">
      <a-list-item>
        <a-card
          hoverable
          :loading="loading"
          size="default"
          :style="
            item.onlineStatus == 1
              ? 'background: linear-gradient(rgba(88, 158, 255, 0.1), white)'
              : item.onlineStatus == 0
              ? 'background: linear-gradient(rgba(128, 128, 128, 0.1), white)'
              : 'background: linear-gradient(rgba(128, 128, 128, 0.1), white)'
          "
        >
          <template #title>
            <a class="detail">{{ item.deviceName }}</a>
          </template>
          <div style="height: 100%; width: 100%; display: flex">
            <div style="flex: 1; display: flex; flex-direction: column; z-index: 2">
              <span class="span-multiline-ellipsis">品类名：{{ item.categoryName ?? '暂无' }}</span>
              <span class="span-multiline-ellipsis">所属网关：{{ item.gatewayName || '暂无' }}</span>
              <span class="span-multiline-ellipsis"
                >设备状态：<a-tag :color="item.onlineStatus == 1 ? 'green' : item.onlineStatus == 0 ? 'orange' : 'orange'">{{
                  item.onlineStatus === 1 ? '在线' : '离线'
                }}</a-tag>
              </span>
              <span>工作时长：{{ item.workDuration ?? '暂无' }}</span>
              <span class="span-multiline-ellipsis"
                >能耗： {{ item.data?.powerConsumption?.toFixed(2) || '0.00' }} <span style="margin-left: 6px; font-size: 15px">KWH</span></span
              >
            </div>
            <div style="flex: 1; display: flex; justify-content: center; align-items: center; z-index: 1">
              <img src="/@/assets/images/product/icon2.svg" alt="" style="max-width: 130px; max-height: 130px; object-fit: contain" />
            </div>
          </div>
        </a-card>
      </a-list-item>
    </template>
  </a-list>
</template>

<script setup>
  import { computed, ref } from 'vue';
  import { router } from '/@/router';
  import { getDeviceStatusEnumDesc } from '/@/constants/business/iot/device/iot-device-const.js'; //getDeviceStatusEnumDesc
  import { DEVICE_TYPE } from '/@/constants/business/iot/device/iot-device-const.js';

  const props = defineProps({
    lampList: {
      type: Array,
      default: () => [],
    },
  });
  console.log('props.lampList', props.lampList);

  const processedLampList = computed(() => {
    return props.lampList.map((item) => ({
      ...item,
      data: {
        ...item.data,
        brightness: item.data.brightness === null ? 0 : item.data.brightness,
        power: item.data.power === null ? 0 : item.data.power,
        switchState: item.data.switchState === null ? 0 : item.data.switchState,
        voltage: item.data.voltage === null ? 0 : item.data.voltage,
        current: item.data.current === null ? '暂无' : item.data.current,
        powerConsumption: item.data.powerConsumption === null ? 0 : item.data.powerConsumption,
      },
      electric: item.electric || { consumption: 0 },
      isSwitchShow: false,
    }));
  });
  console.log('processedLampList.value', processedLampList.value);

  console.log('processedLampList', processedLampList.value);
  processedLampList.value = processedLampList.value[0];
  console.log('processedLampList.value', processedLampList.value);

  // 计算属性
  const tagColor = (item) => {
    return item.onlineStatus === 1 ? '#D8DDFB' : '#FAD8DA'; // 蓝/红
  };

  const bgColor = (item) => {
    console.log(item);

    return item.onlineStatus === 1
      ? 'linear-gradient(188.4deg, rgba(9, 46, 231, 0.08) 30%, rgba(9, 46, 231, 0) 80%)'
      : 'linear-gradient(188.4deg, rgba(229, 0, 18, 0.03) 30%, rgba(229, 0, 18, 0) 80%)';
  };

  const statusTitle = (item) => {
    return item.onlineStatus === 1 ? '在线' : '离线';
  };
  //返回键值
  function findDescByValue(value, enumObject) {
    // 遍历枚举对象的键值对
    for (const key in enumObject) {
      if (enumObject[key].value === value) {
        return enumObject[key].desc; // 找到对应的描述后返回
      }
    }
    return null; // 如果找不到对应的值，则返回 null
  }

  const pointColor = (item) => {
    console.log(item);

    return item.onlineStatus === 1 ? '#1890FF' : '#E50012'; // 蓝/红
  };

  // 截断长文本
  const truncateText = (text, maxLength) => {
    return text.length > maxLength ? `${text.slice(0, maxLength)}...` : text;
  };

  // 开关切换事件
  const handleSwitchChange = (item) => {
    console.log(`灯杆 ${item.id} 开关状态: ${item.extra.switchStatus}`);
  };

  function goLampDetail(data) {
    console.log(data);
    router.push({
      path: '/controlMap-device-detail',
      query: {
        longitude: 113.277,
        latitude: 22.5344,
        id: data.id,
        name: data.name,
      },
    });
  }
</script>

<style scoped lang="less">
  .cardContent {
    width: 100%;
    height: calc(100% - 50px);
    padding: 10px;
    overflow: hidden; // 去除外层滚动条
  }

  .ant-list-item {
    margin-bottom: 16px;
  }

  .ant-list {
    height: 100%;
    overflow-y: auto; // 仅保留 a-list 的竖向滚动条
    overflow-x: hidden;
  }

  .entire {
    position: relative;
    width: 100%;
    height: 180px;
    border-radius: 5px;
    box-shadow: 2px 2px 5px 2px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    transition: 0.2s linear;

    &:hover {
      cursor: pointer;
      transform: scale(1.01);
    }

    .card-content-bg1 {
      position: absolute;
      right: -5%;
      height: 100%;
      width: 64.65%;
      top: 0;
      background: linear-gradient(188.4deg, rgba(229, 0, 18, 0.03) 22.94%, rgba(229, 0, 18, 0) 94.62%);
      transform: skew(-15deg);
    }

    .card-workHours {
      position: absolute;
      width: 52%;
      right: -5%;
      height: 23px;
      top: 5px;
      transform: skew(45deg);

      .productStatus {
        margin-left: 6px;
        transform: skew(-45deg);
      }
    }

    .card-state {
      position: absolute;
      top: 30px;
      right: -9px;
      display: flex;
      justify-content: center;
      width: 60px;
      padding: 2px 0;
      transform: skew(45deg);

      .productStatus {
        transform: skew(-45deg);

        .ant-badge-status-dot {
          position: relative;
          top: -1px;
          display: inline-block;
          width: 6px;
          height: 6px;
          vertical-align: middle;
          border-radius: 50%;
        }

        .ant-badge-status-text {
          margin-left: 8px;
          color: #000000d9;
          font-size: 14px;
        }
      }
    }

    .card-switch {
      position: absolute;
      top: 30px;
      right: 5px;
    }

    .box1 {
      flex: 1;
      overflow: hidden;

      .number {
        width: 140px;
        height: 50px;

        .num1 {
          width: 140px;
          height: 30px;
          /* 文本居中 */
          line-height: 30px;
          /* 文本等高 */
          font-size: 12px;
          font-weight: 900;

          .span {
            display: block;
            width: 100% !important;
          }
        }

        .num2 {
          width: 140px;
          height: 20px;
          /* // text-align: center; */
          line-height: 15px;
          font-size: 14px;
        }
      }

      /* 策略 */
      .syrategy {
        height: 70px;
        display: flex;
        text-align: center;
        align-items: center;

        .text {
          float: left;
          width: 70px;

          .text1 {
            height: 40px;
            font-size: 17px;
            /* font-weight: 700; */
            line-height: 40px;
          }

          .text2 {
            height: 30px;
            transition: transform 0.5s ease;
            font-weight: 550;
          }
        }
      }

      /* 这里到kwm都是能耗的 */
      .energy {
        display: flex;
        align-items: center; /* 垂直居中 */

        .nenghao {
          height: 25px;
          line-height: 25px;
          font-size: 14px;

          .value {
            height: 30px;
            line-height: 30px;
          }
        }
      }
    }

    /* 右盒子 */
    .box2 {
      width: 120px;
      flex-shrink: 0;
      float: right;
      margin-top: -100px;
      .mainInfoRight {
        margin-top: 10px;

        .PercentageBr {
          font-size: 22px;
          margin-top: 7px;
          font-weight: 700;
        }
      }
    }
  }
</style>
