<template>
    <a-modal :open="visible" title="添加文字" @ok="handleOk" @cancel="handleCancel" width="300px">
        <a-form :model="form">
            <a-form-item label="文字内容">
                <a-input v-model:value="form.content" placeholder="请输入文字内容" />
            </a-form-item>
            
            <a-form-item label="字体大小">
                <a-input-number v-model:value="form.fontSize" :min="12" :max="100" />
            </a-form-item>

            <a-form-item label="文字颜色">
                <a-select v-model:value="form.color">
                    <a-select-option v-for="color in colorOptions" :key="color.value" :value="color.value">
                        {{ color.label }}
                    </a-select-option>
                </a-select>
            </a-form-item>
        </a-form>
    </a-modal>
</template>

<script setup>
import { ref, reactive, defineEmits } from 'vue';

const visible = ref(false);
const colorOptions = [
    { label: '红色', value: '#ff0000' },
    { label: '蓝色', value: '#0000ff' },
    { label: '绿色', value: '#00ff00' },
    { label: '黄色', value: '#ffff00' },
    { label: '黑色', value: '#000000' },
    { label: '白色', value: '#ffffff' }
];

const defaultForm = {
    content: '',
    fontSize: 80,
    color: '#ff0000',
    type:'TEXT'
};

const form = reactive({ ...defaultForm});
const emit = defineEmits(['submit']);

const handleOk = () => {
    emit('submit', { ...form });
    visible.value = false;
    Object.assign(form, defaultForm);
};

const handleCancel = () => {
    visible.value = false;
    Object.assign(form, defaultForm);
};

const showForm = () => {
    visible.value = true;
};

defineExpose({ showForm });
</script>