<!--
  * 规则引擎表
  *
  * @Author:    林丽专
  * @Date:      2025-03-27 17:17:51
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
  <a-modal :title="form.id ? '编辑' : '添加'" :width="700" :open="visibleFlag" @cancel="onClose" :maskClosable="false"
    :destroyOnClose="true">
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }">
      <a-form-item label="标题" name="label">
        <a-input style="width: 100%" v-model:value="form.label" placeholder="请输入标题" />
      </a-form-item>
      <a-form-item label="图标" name="logo">
        <Upload accept=".jpg,.png" :maxUploadSize="1" buttonText="点击上传图片"
          :defaultFileList="form.logo ? [{ fileUrl: form.logo }] : []" listType="picture-card"
          @change="changeAttachment" :folder="FILE_FOLDER_TYPE_ENUM.FEEDBACK.value" />
        <div style="color:gray">只能上传jpg/png文件，且不能超过500kb</div>
      </a-form-item>
      <a-form-item label="排序号" name="sort">
        <a-input-number style="width: 100%" v-model:value="form.sort" placeholder="排序号" />
      </a-form-item>
      <a-form-item label="JSON配置" name="flowData" v-if="form.id">
        <a-textarea v-model:value="form.flowData" placeholder="请输入品类说明" :disabled="form.id"
          :autoSize="{ minRows: 3, maxRows: 10 }" />
      </a-form-item>
      <a-form-item label="说明" name="info">
        <!-- <a-textarea style="width: 100%" v-model:value="form.categoryGuide" placeholder="请输入引导说明" /> -->
        <SmartWangeditor ref="contentRef" :modelValue="form.contentHtml" :height="300" />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
import { reactive, ref, nextTick } from 'vue';
import _ from 'lodash';
import { message } from 'ant-design-vue';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { iotRuleApi } from '/@/api/business/iot/edge/iot-rule-api';
import { smartSentry } from '/@/lib/smart-sentry';
import Upload from '/@/components/support/file-upload/index.vue';
import { FILE_FOLDER_TYPE_ENUM } from '/@/constants/support/file-const';
import DictSelect from '/@/components/support/dict-select/index.vue';// 字典下拉框
import SmartWangeditor from '/@/components/framework/wangeditor/index.vue';
// ------------------------ 事件 ------------------------

const emits = defineEmits(['reloadList']);

// ------------------------ 显示与隐藏 ------------------------
// 是否显示
const visibleFlag = ref(false);

function show(rowData) {
  console.log("rowData", rowData)
  Object.assign(form, formDefault);
  if (rowData && !_.isEmpty(rowData)) {
    Object.assign(form, rowData);
  }
  console.log(form, 'dd');

  // 使用字典时把下面这注释修改成自己的字典字段 有多个字典字段就复制多份同理修改 不然打开表单时不显示字典初始值
  // if (form.status && form.status.length > 0) {
  //   form.status = form.status.map((e) => e.valueCode);
  // }
  visibleFlag.value = true;
  nextTick(() => {
    formRef.value.clearValidate();
  });
}

function onClose() {
  Object.assign(form, formDefault);
  visibleFlag.value = false;
}

// ------------------------ 表单 ------------------------

//选择图片
function changeAttachment(fileList) {
  form.logo = fileList[0]?.fileUrl || "";
 
}
// 组件ref
const formRef = ref();

const formDefault = {
  label: undefined,    //标题
  logo: undefined,    //图标
  info: undefined,    //说明
  sort: undefined,    //排序号
  source: 'rule',     //来源
  type: 'tab',        //类型
  disabled: 1,        //禁用
  id: undefined,      //ID
  flowData: undefined,    //JSON配置
  deployTime:undefined    //部署时间
};

let form = reactive({ ...formDefault });

const rules = {
  label: [{ required: true, message: '标题 必填' }],
  sort: [{ required: true, message: '排序号 必填' }],
};

// 点击确定，验证表单
async function onSubmit() {
  try {
    await formRef.value.validateFields();
    save();
  } catch (err) {
    message.error('参数验证错误，请仔细填写表单数据!');
  }
}

// 新建、编辑API
async function save() {
  SmartLoading.show();
  try {
    if (form.id) {
      console.log("编辑")
      await iotRuleApi.update(form);
    } else {
      form.id = undefined;
      await iotRuleApi.add(form);
    }
    message.success('操作成功');
    emits('reloadList');
    onClose();
  } catch (err) {
    smartSentry.captureError(err);
  } finally {
    SmartLoading.hide();
  }
}

defineExpose({
  show,
});
</script>