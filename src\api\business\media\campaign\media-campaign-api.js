/**
 * 投放计划 api 封装
 *
 * @Author:    潘显镇
 * @Date:      2025-05-26 20:44:46
 * @Copyright  中山睿数信息技术有限公司 2025
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const mediaCampaignApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/mediaCampaign/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/mediaCampaign/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/mediaCampaign/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/mediaCampaign/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
      return postRequest('/mediaCampaign/batchDelete', idList);
  },
  /**
   * id查询
   */
  getById:(id)=> {
    return getRequest(`/mediaCampaign/getMediaCampaignById/${id}`);
  },
  // /**
  //  * 计划下达  <AUTHOR>
  //  */
  // issuePlanToDevice: (param) => {
  //   return postRequest('/mediaCampaign/issuePlanToDevice', param);
  // },
  /**
   * 根据设备查询投放计划  <AUTHOR>
   */
  getMediaCampaignByDevice: (param) => {
    return postRequest('/mediaCampaign/getMediaCampaignByDevice', param);
  },
  /**
   * 提交投放计划  <AUTHOR>
   */
  submittedCampaign: (id) => {
    return getRequest(`/mediaCampaign/submittedCampaign/${id}`);
  },
  /**
   * 审核通过投放计划  <AUTHOR>
   */
  approvedCampaign: (param) => {
    return postRequest('/mediaCampaign/approvedCampaign', param);
  },
  /**
   * 审核不通过投放计划  <AUTHOR>
   */
  rejectedCampaign: (param) => {
    return postRequest('/mediaCampaign/rejectedCampaign', param);
  },
  /**
   * 反审核投放计划  <AUTHOR>
   */
  unapprovedCampaign: (id) => {
    return getRequest(`/mediaCampaign/unapprovedCampaign/${id}`);
  },
  /**
   * 计划下达  <AUTHOR>
   */
  issueCampaign: (id) => {
    return getRequest(`/mediaCampaign/issueCampaign/${id}`);
  },
  /**
   * 取消计划下达  <AUTHOR>
   */
  unIssueCampaign: (id) => {
    return getRequest(`/mediaCampaign/cancelIssueCampaign/${id}`);
  },
  /**
   * 结束计划  <AUTHOR>
   */
  completedIssueCampaign: (id) => {
    return getRequest(`/mediaCampaign/completedIssueCampaign/${id}`);
  },
  
};
