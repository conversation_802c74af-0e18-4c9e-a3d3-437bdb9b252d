<template>
    <a-card size="small" :bordered="false" :hoverable="true">
      <!-- 信息行 -->
      <a-space style="margin-bottom: 10px">
        <a-button type="text" @click="back"> <left-outlined /> 返回 </a-button>
        <a-divider type="vertical" style="border-color: #dcdfe6" />
        <span class="title">月度报告：</span>
      </a-space>
      <EnergyInfo :Id="id" />
    </a-card>
  </template>
  
  <script setup>
    import { useRouter, useRoute } from 'vue-router';
    import { ref} from 'vue';
    import { useUserStore } from '/@/store/modules/system/user';
    import { LeftOutlined } from '@ant-design/icons-vue';
    import EnergyInfo from './components/energy-info.vue';

  
    //---------------------------------页面返回-------------------------------------
    const route = useRoute();
    const router = useRouter();
    const back = () => {
      router.push({
        path: '/Operation/energy', //返回能耗管理页
        query: {} // 使用空对象确保清除所有查询参数
      });
      
      //关闭当前标签页
      useUserStore().closeTagNav(route.name, false);;
    };
    //---------------------------------组建信息-------------------------------------
    const id = ref(route.query.id);


  </script>
  
  <style scoped>
    
  </style>