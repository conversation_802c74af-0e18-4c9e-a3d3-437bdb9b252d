<!--
  * 巡检单表单
  *
  * @Author:    潘显镇
  * @Date:      2025-04-11
-->
<template>
  <a-modal
    :title="'巡检单详情'"
    :width="1200"
    :open="visibleFlag"
    @cancel="onClose"
    :maskClosable="false"
    :destroyOnClose="true"
    :footer="null"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 6 }" disabled>
      <a-row>
        <a-col :span="12">
          <a-form-item label="设备" name="deviceId">
            <IotDeviceSelect 
              style="width: 100%"
              v-model:value="form.deviceId"
              placeholder="请选择设备"
            />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="巡检方案" name="programmeId">
           <inspectProgrammeSelect
              style="width: 100%"
              v-model:value="form.programmeId"
              placeholder="请选择巡检方案"
              @change="onProgrammeChange"
            />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="巡检时间" name="inspectionTime">
            <a-date-picker
              style="width: 100%"
              v-model:value="form.inspectionTime"
              show-time
              valueFormat="YYYY-MM-DD HH:mm:ss"
              placeholder="请选择巡检时间"
            />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="巡检结果" name="inspectionResult">
            <a-select
              style="width: 100%"
              v-model:value="form.inspectionResult"
              placeholder="请选择巡检结果"
            >
              <a-select-option :value="true">正常</a-select-option>
              <a-select-option :value="false">异常</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="巡检人员" name="inspectionPersonId">
            <EmployeeSelect
              style="width: 100%"
              v-model:value="form.inspectionPersonId"
              placeholder="请选择巡检人员"
            />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="经度" name="longitude">
            <a-input-number
              style="width: 100%"
              v-model:value="form.longitude"
              placeholder="请输入经度"
              :precision="6"
            />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="纬度" name="latitude">
            <a-input-number
              style="width: 100%"
              v-model:value="form.latitude"
              placeholder="请输入纬度"
              :precision="6"
            />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="巡检备注" name="inspectionRemark">
            <a-textarea
              v-model:value="form.inspectionRemark"
              placeholder="请输入巡检备注"
              :rows="3"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 巡检项目列表-->
      <a-form-item label="巡检项目列表" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
        <a-table
          :columns="itemColumns"
          :data-source="form.items"
          :pagination="false"
          size="small"
          bordered
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'index'">
              {{ index + 1 }}
            </template>
            <template v-if="column.dataIndex === 'itemName'">
              <a-input v-model:value="record.itemName" placeholder="请输入巡检项目名称"/>
            </template>
            <template v-if="column.dataIndex === 'inspectionMethod'">
              <a-input v-model:value="record.inspectionMethod" placeholder="请输入检查方法"/>
            </template>
            <template v-if="column.dataIndex === 'minValue'">
              <a-input-number v-model:value="record.minValue" placeholder="最小值" style="width: 100%"/>
            </template>
            <template v-if="column.dataIndex === 'maxValue'">
              <a-input-number v-model:value="record.maxValue" placeholder="最大值" style="width: 100%"/>
            </template>
            <template v-if="column.dataIndex === 'result'">
              <a-input v-model:value="record.result" placeholder="请输入结果"/>
            </template>
            <template v-if="column.dataIndex === 'remark'">
              <a-input v-model:value="record.remark" placeholder="请输入备注"/>
            </template>
          </template>   
        </a-table>
      </a-form-item>
    </a-form>

  </a-modal>
</template>

<script setup>
import { reactive, ref, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import { inspectApi } from '/@/api/business/maintain/inspect/inspect-api';
import { smartSentry } from '/@/lib/smart-sentry';
import EmployeeSelect from '/@/components/system/employee-select/index.vue';
import IotDeviceSelect from '/@/components/business/iot/iot-device-select/index.vue';
import inspectProgrammeSelect from '/@/components/business/maintain/inspect-programme-select/index.vue';

// ------------------------ 事件 ------------------------
const emits = defineEmits(['reloadList']);

// ------------------------ 显示与隐藏 ------------------------
const visibleFlag = ref(false);

async function show(id) {
    try{
        const res = await inspectApi.getById(id);
        Object.assign(form, formDefault, res.data);
        visibleFlag.value = true;
    }catch (e) {
        smartSentry.captureException(e);
        message.error('获取巡检单信息失败，请稍后重试！');
    }
  nextTick(() => {
    formRef.value?.clearValidate();
  });
}

function onClose() {
  Object.assign(form, formDefault);
  form.items = [];
  visibleFlag.value = false;
}

// ------------------------ 表单 ------------------------
const formRef = ref();

const formDefault = {
  id: undefined,           // 主键ID
  deviceId: undefined,     // 设备ID
  programmeId: undefined,  // 巡检方案ID
  inspectionTime: undefined, // 巡检时间
  inspectionResult: undefined, // 巡检结果
  inspectionRemark: undefined, // 巡检备注
  inspectionPersonId: undefined, // 巡检人员ID
  longitude: undefined,    // 经度
  latitude: undefined,     // 纬度
  items: []               // 巡检项目列表
};

let form = reactive({ ...formDefault });

// 巡检项目表格列定义
const itemColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 60,
    align: 'center'
  },
  {
    title: '巡检项目名称',
    dataIndex: 'itemName'
  },
  {
    title: '检查方法',
    dataIndex: 'inspectionMethod'
  },
  {
    title: '最小值',
    dataIndex: 'minValue',
    width: 120
  },
  {
    title: '最大值',
    dataIndex: 'maxValue',
    width: 120
  },
  {
    title: '结果',
    dataIndex: 'result'
  },
  {
    title: '备注',
    dataIndex: 'remark'
  },
];

const rules = {
  deviceId: [
    { required: true, message: '请选择设备' }
  ],
  programmeId: [
    { required: true, message: '请输入巡检方案ID' }
  ],
  inspectionTime: [
    { required: true, message: '请选择巡检时间' }
  ],
  inspectionResult: [
    { required: true, message: '请选择巡检结果' }
  ],
  inspectionPersonId: [
    { required: true, message: '请选择巡检人员' }
  ]
};


defineExpose({
  show,
});
</script>