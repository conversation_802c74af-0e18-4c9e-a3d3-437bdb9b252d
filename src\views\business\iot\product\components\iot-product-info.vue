<template>
    <a-card>
        <a-descriptions title="产品详情" :column="3" size="default" :bordered="false">
            <a-descriptions-item label="产品名称">
                {{ productInfo.productName }}
                <a-button type="link" @click="showEditModal" size="small">编辑</a-button>
            </a-descriptions-item>
            <a-descriptions-item label="设备类型">
                {{ form.deviceTypeName }}
            </a-descriptions-item>
            <a-descriptions-item label="创建时间">
                {{ form.createTime }}
            </a-descriptions-item>
            <a-descriptions-item label="所属品类">
                {{ form.categoryNameFull }}
            </a-descriptions-item>
            <a-descriptions-item label="数据状态">
                {{ form.statusName }}
            </a-descriptions-item>
            <a-descriptions-item label="动态注册">
                {{ form.dynamicRegisterName }}
            </a-descriptions-item>
            <a-descriptions-item label="链接协议">
                {{ form.linkProtocolName }}
            </a-descriptions-item>
            <a-descriptions-item label="联网方式">
                {{ form.connectModeName }}
            </a-descriptions-item>
            <a-descriptions-item label="数据格式">
                {{ form.dataTypeName }}
            </a-descriptions-item>
            <a-descriptions-item label="生产厂商">
                {{ form.vendors }}
            </a-descriptions-item>
            <a-descriptions-item label="产品型号">
                {{ form.model }}
            </a-descriptions-item>
            <a-descriptions-item label="产品描述">
                {{ form.productDesc }}
            </a-descriptions-item>
        </a-descriptions>

        <IotProductUpdateName ref="updateNameRef" :productInfo="productInfo"  @update-product="handleUpdateProduct" />
    </a-card>
</template>

<script setup>
import { ref, watch, reactive } from 'vue';
import { message } from 'ant-design-vue';
import IotProductUpdateName from './iot-product-updateName.vue';

const props = defineProps({
  productInfo: {
    type: Object,
  }
});

const emit = defineEmits(['update-product']);

const formState = {
  productName: '',
  deviceTypeName: '',
  createTime: '',
  categoryNameFull: '',
  statusName: '',
  dynamicRegisterName: '',
  linkProtocolName: '',
  connectModeName: '',
  dataTypeName: '',
  vendors: '',
  model: '',
  productDesc: '',
};

const form = reactive({...formState});

watch(() => props.productInfo, (newVal) => {
  if (newVal) {
    Object.assign(form, newVal);
  }
}, { immediate: true, deep: true });

// 显示编辑模态框
const updateNameRef = ref(null);
const showEditModal = () => {
  updateNameRef.value.showModal();
};

// 处理更新产品信息
const handleUpdateProduct = (updatedInfo) => {
  emit('update-product', updatedInfo);
};
</script>

<style scoped>
.edit-button {
  margin-left: 8px;
}
</style>