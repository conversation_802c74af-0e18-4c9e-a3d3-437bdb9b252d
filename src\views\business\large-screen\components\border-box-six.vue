<template>
  <div ref="borderBoxContainer" class="border-box-container">
    <Decoration7 class="top-left-decoration" :color="['#18abd9', '#1ba6d0']">
      <text style="margin:1vw;">当前区域下设备报警信息</text>
    </Decoration7>
    <div class="border-box-content">
      <BorderBox10 :color="['#3896b3', '#329bbb']">
        <div class="content">
          <ScrollBoard :config="scrollConfig" class="scroll-board" />
        </div>
      </BorderBox10>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { BorderBox10, Decoration7, ScrollBoard } from '@kjgl77/datav-vue3';

const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
});

// -----------------------------------------------------数据处理
// 转换数据格式
const tableData = computed(() => {
  console.log('tableData',props.list);
  return props.list.map((item) => {
    return [
      item.regionName || '-',
      item.deviceName || '-',
      getStatusText(item.faultHandleStatus),
      item.faultRemark || '-',
    ];
  });
});

// 处理状态映射
function getStatusText(status){
    switch (status) {
      case '0':
        return '<span style="color: red;">待处理</span>';
      case '1':
        return '<span style="color: orange;">处理中</span>';
      case '2':
        return '<span style="color: green;">已处理</span>';
      default:
        return '<span style="color: gray;">未知</span>';
    }
}
// -----------------------------------------------------轮播表格配置
const scrollConfig = computed(() => {
  return {
    header: [
      '<span style="color:#0eeaea;">区域名</span>',
      '<span style="color:#0eeaea;">设备名</span>',
      '<span style="color:#0eeaea;">处理进度</span>',
      '<span style="color:#0eeaea;">故障信息</span>',
    ],
    data: tableData.value,
    align: ['center', 'center', 'center', 'center'],
    evenRowBGC: '#0d3f4d',
    oddRowBGC: '#0a313c',
    headerBGC: '#075364',
    headerHeight: 40,
    columnWidth: [140, 120, 90, 230],
    waitTime: 2000,
    rowNum: 3,
    carousel: 'single',
  };
});

// -----------------------------------------------------边框容器
const borderBoxContainer = ref(null);

onMounted(() => {
  if (!borderBoxContainer.value) {
    console.error('BorderBox container ref not exist');
    return;
  }
  console.log('BorderBox container mounted:', borderBoxContainer.value);
});
</script>

<style scoped>
.border-box-container {
  position: absolute;
  top: 65vh;
  right: 1vw;
  width: 25vw;
  height: 28vh;
  z-index: 1000;
}

.top-left-decoration {
  position: absolute;
  color: white;
  font-size: 1.1vw;
  height: 3vh;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.border-box-content {
  position: absolute;
  top: 4.5vh;
  left: 0;
  width: 100%;
  height: calc(100% - 4.5vh);
}

.content {
  width: 100%;
  height: 100%;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
}

.scroll-board {
  width: 100%;
  height: 100%;
}
</style>