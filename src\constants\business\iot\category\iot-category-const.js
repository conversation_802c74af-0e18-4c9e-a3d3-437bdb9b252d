/**
 * 品类表 枚举
 *
 * @Author:    戴松挺
 * @Date:      2025-03-23 17:17:51
 * @Copyright  中山睿数信息技术有限公司 2025
 */
// 品类行业
export const CATEGORY_INDUSTRY_ENUM = {
  city: {
    value: 1,
    desc: '智能城市'
  },
  life: {
    value: 2,
    desc: '智能生活'
  },
  industry: {
    value: 3,
    desc: '智能工业'
  },
  agriculture: {
    value: 4,
    desc: '智能农业'
  },
  template: {
    value: 5,
    desc: '智能模版'
  },
  electricity: {
    value: 6,
    desc: '智能电力'
  },
  building: {
    value: 7,
    desc: '智能建筑'
  },
  park: {
    value: 8,
    desc: '智能园区'
  }
};


//品类场景
export const DICT_TYPE_ENUM = {
  public_service: {
      value: 1,
      desc: '公共服务'
  },
  environment_sensing: {
      value: 2,
      desc: '环境感知'
  },
  fire_safety: {
      value: 3,
      desc: '消防安全'
  },
  smart_building: {
      value: 4,
      desc: '智能楼宇'
  },
  home_security: {
      value: 5,
      desc: '家居安防'
  },
  environment_appliance: {
      value: 6,
      desc: '环境电器'
  },
  electric_lighting: {
      value: 7,
      desc: '电工照明'
  },
  large_appliance: {
      value: 8,
      desc: '大型家电'
  },
  small_appliance: {
      value: 9,
      desc: '小型家电'
  },
  kitchen_equipment: {
      value: 10,
      desc: '厨房设备'
  },
  network_equipment: {
      value: 11,
      desc: '网络设备'
  },
  smart_device: {
      value: 12,
      desc: '智能设备'
  },
  retail_equipment: {
      value: 13,
      desc: '零售设备'
  },
  health: {
      value: 14,
      desc: '个护健康'
  },
  edge_computing: {
      value: 15,
      desc: '边缘计算'
  }
};

// 发布状态
export const PUBLISH_STATUS_ENUM = {
  unpublished: {
    value: 0,
    desc: '未发布',
  },
  published: {
    value: 1,
    desc: '发布',
  },
};


export default {
  CATEGORY_INDUSTRY_ENUM,
  DICT_TYPE_ENUM,
  PUBLISH_STATUS_ENUM
};
