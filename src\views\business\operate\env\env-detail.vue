<template>
  <a-card size="small" :bordered="false" :hoverable="true">
    <!-- 信息行 -->
    <a-space style="margin-bottom: 10px">
      <a-button type="text" @click="back"> <left-outlined /> 返回 </a-button>
      <a-divider type="vertical" style="border-color: #dcdfe6" />
      <span class="title">区域名称：{{ regionName }}</span>
    </a-space>

    <!-- 图表展示区域 -->
    <div class="background">
      <div class="charts-container">
        <a-row :gutter="[16, 16]">
          <a-col :span="24" :lg="24">
            <div class="chart-card">
              <div class="chart-title">空气质量指标</div>
              <div ref="airQualityChart" style="width: 100%; height: 300px"></div>
            </div>
          </a-col>
        </a-row>
      </div>
      <div class="charts-container">
        <a-row :gutter="[16, 16]">
          <!-- 左侧温湿度图表 -->
          <a-col :span="24" :lg="10">
            <div class="chart-card">
              <div class="chart-title">温度&湿度指标</div>
              <div ref="tempHumidityChart" style="width: 100%; height: 600px"></div>
            </div>
          </a-col>

          <!-- 右侧四个图表布局 -->
          <a-col :span="24" :lg="14">
            <a-row :gutter="[16, 16]">
              <a-col :span="12">
                <div class="chart-card">
                  <div class="chart-title">高温日期排行榜</div>
                  <div ref="highTempChart" style="width: 100%; height: 290px"></div>
                </div>
              </a-col>

              <a-col :span="12">
                <div class="chart-card">
                  <div class="chart-title">潮湿日期排行榜</div>
                  <div ref="highHumidityChart" style="width: 100%; height: 290px"></div>
                </div>
              </a-col>

              <a-col :span="12">
                <div class="chart-card">
                  <div class="chart-title">
                    噪音监测
                    <a-radio-group
                      v-model:value="noiseChartDate"
                      button-style="solid"
                      size="small"
                      style="margin-left: 10px; position: absolute; right: 0"
                      @change="updateNoiseChart"
                    >
                      <a-radio-button value="today">当月</a-radio-button>
                      <a-radio-button value="yesterday">上月</a-radio-button>
                    </a-radio-group>
                  </div>
                  <div ref="noiseRadarChart" style="width: 100%; height: 290px"></div>
                </div>
              </a-col>

              <a-col :span="12">
                <div class="chart-card">
                  <div class="chart-title">
                    光强亮度
                    <a-radio-group
                      v-model:value="lumBrightnessChartDate"
                      button-style="solid"
                      size="small"
                      style="margin-left: 10px; position: absolute; right: 0"
                      @change="updateLumBrightnessChart"
                    >
                      <a-radio-button value="today">当月</a-radio-button>
                      <a-radio-button value="yesterday">上月</a-radio-button>
                    </a-radio-group>
                  </div>
                  <div ref="lumBrightnessChart" style="width: 100%; height: 290px"></div>
                </div>
              </a-col>
            </a-row>
          </a-col>
        </a-row>
      </div>
    </div>
  </a-card>
</template>

<script setup>
  import { ref, onMounted, nextTick, onUnmounted } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { useUserStore } from '/@/store/modules/system/user';
  import { LeftOutlined } from '@ant-design/icons-vue';
  import { operateEnvApi } from '/@/api/business/operate/env/env-device-by-region';
  import * as echarts from 'echarts'; // 替换VChart为ECharts
  import dayjs from 'dayjs';
  import formatter from '/@/utils/formatter';

  //---------------------------------路由跳转-页面返回-------------------------------------
  const route = useRoute();
  const router = useRouter();
  //传来的的路由参数
  const regionName = route.query.regionName; //区域名称
  const deviceId = route.query.deviceId; //设备id
  const timeValue = route.query.timeValue; //时间值

  //页面返回
  const back = () => {
    router.push({
      path: '/iot/operate/env/product', //返回设备信息页
    });
    //关闭当前标签页
    useUserStore().closeTagNav(route.name, false);
  };

  // 图表DOM引用
  const airQualityChart = ref(null); // 空气质量
  const tempHumidityChart = ref(null); //温度湿度
  const highTempChart = ref(null); // 高温日数
  const highHumidityChart = ref(null); // 潮湿日数
  const noiseRadarChart = ref(null); // 噪音监测
  const lumBrightnessChart = ref(null); // 光强和亮度

  // 图表实例
  let airQualityChartInstance = null; // 空气质量指标
  let tempHumidityInstance = null; // 温度湿度
  let highTempChartInstance = null; // 高温日数
  let highHumidityChartInstance = null; // 潮湿日数
  let noiseRadarChartInstance = null; // 噪音监测
  let lumBrightnessChartInstance = null; // 光强和亮度

  //当前月份
  const currentMonth = dayjs().format('M月');
  //当前年月日
  const currentDate = dayjs().format('YYYY-MM-DD');
  //当前年月日的第一天
  const firstDayOfMonth = dayjs().startOf('month').format('YYYY-MM-DD');

  // 根据传递的时间值计算日期范围
  const getDateRangeFromTimeValue = () => {
    if (!timeValue) return { beginDate: firstDayOfMonth, endDate: currentDate };
    let year, month;
    if (timeValue.includes('-')) {
      [year, month] = timeValue.split('-');
    } else if (timeValue.includes('年')) {
      [year, month] = timeValue.split('年');
      month = month.replace('月', '');
    } else {
      return { beginDate: firstDayOfMonth, endDate: currentDate };
    }

    // 构建日期范围
    const beginDate = `${year}-${month.padStart(2, '0')}-01`;
    const endDate = dayjs(`${year}-${month.padStart(2, '0')}`)
      .endOf('month')
      .format('YYYY-MM-DD');

    return { beginDate, endDate };
  };

  // 初始化图表函数
  const initCharts = () => {
    // 确保DOM已经渲染完成后再初始化图表
    nextTick(() => {
      // 温度湿度图表
      if (tempHumidityChart.value) {
        tempHumidityInstance = echarts.init(tempHumidityChart.value);
        tempHumidityInstance.setOption(tempHumidityOption);
      }

      // 高温日数图表
      if (highTempChart.value) {
        highTempChartInstance = echarts.init(highTempChart.value);
        highTempChartInstance.setOption(highTempOption);
      }

      // 噪音数据图表
      if (noiseRadarChart.value) {
        noiseRadarChartInstance = echarts.init(noiseRadarChart.value);
        noiseRadarChartInstance.setOption(noiseRadarOption);
      }

      // 空气质量指标图表
      if (airQualityChart.value) {
        airQualityChartInstance = echarts.init(airQualityChart.value);
        airQualityChartInstance.setOption(airQualityOption);
      }

      //潮湿日数图表
      if (highHumidityChart.value) {
        highHumidityChartInstance = echarts.init(highHumidityChart.value);
        highHumidityChartInstance.setOption(highHumidityOption);
      }

      // 光强亮度图表
      if (lumBrightnessChart.value) {
        lumBrightnessChartInstance = echarts.init(lumBrightnessChart.value);
        lumBrightnessChartInstance.setOption(lumBrightnessOption);
      }
    });
  };

  //---------------------------------获取每小时环境数据-----------------------------------
  //存储环境数据
  const currentMonthData = ref(null); //当月数据
  const lastMonthData = ref(null); //上月数据

  // 获取特定日期的环境数据（目前的接口实际上请求的是今日昨日数据）
  const getEnvDataByDate = async (dateType) => {
    // 检查是否已经有缓存的数据
    if (dateType === 'today' && currentMonthData.value) {
      return currentMonthData.value;
    } else if (dateType === 'yesterday' && lastMonthData.value) {
      return lastMonthData.value;
    }

    // 根据日期类型获取日期字符串
    const dateParam = dateType === 'today' ? dayjs().format('YYYY-MM-DD') : dayjs().subtract(1, 'day').format('YYYY-MM-DD');

    try {
      const res = await operateEnvApi.getEnvDataPerHour({
        date: dateParam,
        deviceId,
      });
      // 缓存数据
      dateType === 'today' ? (currentMonthData.value = res.data) : (lastMonthData.value = res.data);
      console.log(res.data);
      res.data = {
        '2025-06-03 02:00:00': { temperature: 25, humidity: 60, noise: 50, luminance: 45000, brightness: 28000, pm25: 10, pm10: 10, co2: 800 },
      };
      console.log(res.data);

      return res.data;
    } catch (err) {
      console.error('获取环境数据失败:', err);
      return null;
    }
  };

  //----------------------------空气质量pm2.5、pm10、co2----------------------------------
  // 空气质量环境指标
  const airQualityOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
        },
      },
      formatter: function (params) {
        let result = params[0].name + '<br/>';
        params.forEach((param) => {
          let unit = param.seriesName === 'CO2' ? ' ppm' : ' μg/m³';
          let value = param.value;
          let quality = '';

          // 空气质量等级评估
          if (param.seriesName === 'PM2.5') {
            if (value <= 35) quality = '【优】';
            else if (value <= 75) quality = '【良】';
            else if (value <= 115) quality = '【轻度污染】';
            else if (value <= 150) quality = '【中度污染】';
            else if (value <= 250) quality = '【重度污染】';
            else quality = '【严重污染】';
          } else if (param.seriesName === 'PM10') {
            if (value <= 50) quality = '【优】';
            else if (value <= 150) quality = '【良】';
            else if (value <= 250) quality = '【轻度污染】';
            else if (value <= 350) quality = '【中度污染】';
            else if (value <= 420) quality = '【重度污染】';
            else quality = '【严重污染】';
          } else if (param.seriesName === 'CO2') {
            if (value <= 800) quality = '【优】';
            else if (value <= 1000) quality = '【良】';
            else if (value <= 1500) quality = '【中】';
            else if (value <= 2000) quality = '【差】';
          }

          result += param.marker + param.seriesName + ': ' + param.value + unit + ' ' + quality + '<br/>';
        });
        return result;
      },
    },
    grid: {
      top: '10%',
      left: '3%',
      right: '3%',
      bottom: '10%',
      containLabel: true,
    },
    legend: {
      data: [
        {
          name: 'PM2.5',
          itemStyle: {
            color: '#52c41a',
          },
        },
        {
          name: 'PM10',
          itemStyle: {
            color: '#1890ff',
          },
        },
        {
          name: 'CO2',
          itemStyle: {
            color: '#13c2c2',
          },
        },
      ],
      bottom: 0,
      itemWidth: 10,
      itemHeight: 10,
      textStyle: {
        color: '#606266',
        fontSize: 12,
      },
    },

    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        data: [],
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: 'PM浓度 (μg/m³)',
        position: 'left',
        axisLine: {
          show: true,
          lineStyle: {
            color: '#1890ff',
          },
        },
        axisLabel: {
          formatter: function (value) {
            let label = value.toString();
            // 为PM浓度添加等级标识
            if (value === 0) return '0';
            else if (value === 35) return '35 [优]';
            else if (value === 75) return '75 [良]';
            else if (value === 115) return '115 [轻污]';
            else if (value === 150) return '150 [中污]';
            else if (value === 250) return '250 [重污]';
            else if (value === 300) return '300 [严重]';

            return label;
          },
          color: function (value) {
            // 根据数值返回不同颜色
            if (value <= 35) return '#52c41a'; // 绿色 - 优
            else if (value <= 75) return '#fadb14'; // 黄色 - 良
            else if (value <= 115) return '#fa8c16'; // 橙色 - 轻度污染
            else if (value <= 150) return '#f5222d'; // 红色 - 中度污染
            else if (value <= 250) return '#722ed1'; // 紫色 - 重度污染
            else return '#780650'; // 褐红色 - 危险
          },
        },
        // 设置刻度值
        splitLine: {
          show: true,
          lineStyle: {
            color: ['#f0f0f0'],
            type: 'dashed',
          },
        },
        interval: null,
        min: 0,
        max: 500,
      },
      {
        type: 'value',
        name: 'CO2浓度 (ppm)',
        position: 'right',
        offset: 0,
        axisLine: {
          show: true,
        },
        axisLabel: {
          formatter: function (value) {
            let label = value.toString();
            // 为CO2浓度添加等级标识
            if (value === 380) return '380';
            else if (value === 800) return '800 [优]';
            else if (value === 1000) return '1000 [良]';
            else if (value === 1500) return '1500 [中]';
            else if (value === 2000) return '2000 [差]';
            return label;
          },
          color: function (value) {
            // 根据数值返回不同颜色
            if (value <= 800) return '#52c41a'; // 绿色 - 优
            else if (value <= 1000) return '#fadb14'; // 黄色 - 良
            else if (value <= 1500) return '#fa8c16'; // 橙色 - 中
            else return '#f5222d'; // 红色 - 差
          },
        },
        splitLine: {
          show: true,
        },
        min: 380,
        max: 2000,
        interval: null,
      },
    ],
    series: [
      {
        name: 'PM2.5',
        type: 'line',
        smooth: true,
        lineStyle: { width: 0 },
        showSymbol: false,
        areaStyle: {
          opacity: 0.8,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(82, 196, 26, 0.8)' },
            { offset: 1, color: 'rgba(82, 196, 26, 0.1)' },
          ]),
        },
        emphasis: { focus: 'series' },
        data: [],
      },
      {
        name: 'PM10',
        type: 'line',
        smooth: true,
        lineStyle: { width: 0 },
        showSymbol: false,
        areaStyle: {
          opacity: 0.8,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(24, 144, 255, 0.8)' },
            { offset: 1, color: 'rgba(24, 144, 255, 0.1)' },
          ]),
        },
        emphasis: { focus: 'series' },
        data: [],
      },
      {
        name: 'CO2',
        type: 'line',
        yAxisIndex: 1,
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#13c2c2',
        },
        itemStyle: {
          color: '#13c2c2',
          borderWidth: 2,
          borderColor: '#fff',
        },
        showSymbol: true,
        symbolSize: 6,
        emphasis: { focus: 'series' },
        data: [],
      },
    ],
  };

  //更新空气质量数据
  const updateAirQualityChartData = async () => {
    // 获取基于时间值的日期范围
    const { beginDate, endDate } = getDateRangeFromTimeValue();

    const params = {
      deviceId: deviceId,
      beginDate: beginDate,
      endDate: endDate,
    };
    const res = await operateEnvApi.getEnvDataPerDay(params);
    res.data = {
      '2025-06-09 02:00:00': { temperature: 20, humidity: 40, noise: 50, luminance: 45000, brightness: 28000, pm25: 100, pm10: 80, co2: 200 },
      '2025-06-09 03:00:00': { temperature: 21, humidity: 80, noise: 50, luminance: 45000, brightness: 28000, pm25: 100, pm10: 120, co2: 800 },
      '2025-06-09 04:00:00': { temperature: 22.2, humidity: 45, noise: 150, luminance: 45000, brightness: 28000, pm25: 250, pm10: 250, co2: 800 },
      '2025-06-09 05:00:00': { temperature: 24, humidity: 30, noise: 100, luminance: 45000, brightness: 28000, pm25: 90, pm10: 420, co2: 800 },
      '2025-06-09 06:00:00': { temperature: 25, humidity: 60, noise: 120, luminance: 45000, brightness: 28000, pm25: 90, pm10: 358, co2: 800 },
      '2025-06-09 07:00:00': { temperature: 26, humidity: 75, noise: 140, luminance: 45000, brightness: 28000, pm25: 200, pm10: 420, co2: 800 },
      '2025-06-09 08:00:00': { temperature: 27, humidity: 70, noise: 80, luminance: 45000, brightness: 28000, pm25: 230, pm10: 500, co2: 1800 },
    };
    //排序
    const sortedData = Object.entries(res.data)
      .sort(([dateA], [dateB]) => dateA.localeCompare(dateB))
      .reduce(
        (acc, [date, value]) => {
          acc.dates.push(date);
          acc.pm25.push(value.pm25);
          acc.pm10.push(value.pm10);
          acc.co2.push(value.co2);
          return acc;
        },
        { dates: [], pm25: [], pm10: [], co2: [] }
      );

    // 更新图表数据
    airQualityChartInstance.setOption({
      xAxis: {
        data: sortedData.dates,
      },
      series: [
        {
          data: sortedData.pm25,
        },
        {
          data: sortedData.pm10,
        },
        {
          data: sortedData.co2,
        },
      ],
    });
  };

  //-------------------------------------温度湿度-----------------------------------------
  // 配置温度湿度图表
  const tempHumidityOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '0%',
      containLabel: true,
    },
    legend: {
      data: ['温度', '湿度'],
      top: 0,
    },
    xAxis: [
      {
        type: 'category',
        data: ['2025-04-03', '2025-04-04', '2025-04-05', '2025-04-06', '2025-04-07', '2025-04-08', '2025-04-09', '2025-04-10', '2025-04-11'],
        axisPointer: {
          type: 'shadow',
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '湿度（相对湿度）',
        min: 0,
        max: 100,
        interval: 10,
        axisLabel: {
          formatter: '{value} %RH',
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#40a9ff',
          },
        },
      },
      {
        type: 'value',
        name: '温度（摄氏度）',
        min: -100,
        max: 100,
        interval: 20,
        axisLabel: {
          formatter: '{value} °C',
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#ff7a45',
          },
        },
      },
    ],
    series: [
      {
        name: '湿度',
        type: 'bar',
        min: 0,
        max: 100,
        step: 5,
        tooltip: {
          valueFormatter: function (value) {
            return value + ' %RH';
          },
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#69c0ff' },
            { offset: 1, color: '#1890ff' },
          ]),
        },
        data: [20, 49, 70, 23, 25, 76, 60, 43, 32, 20, 45, 27],
      },
      {
        name: '温度',
        type: 'line',
        yAxisIndex: 1,
        tooltip: {
          valueFormatter: function (value) {
            return value + ' °C';
          },
        },
        itemStyle: {
          color: '#ff7a45',
          borderWidth: 2,
          borderColor: '#fff',
        },
        data: [-80, -60, -30, 10, 15, 26, 9, -10, 23.0, 16.5, 12.0, 6.2],
      },
    ],
  };

  //更新温湿度数据
  const updateTempHumidityChartData = async () => {
    // 获取基于时间值的日期范围
    const { beginDate, endDate } = getDateRangeFromTimeValue();

    const params = {
      deviceId: deviceId,
      beginDate: beginDate,
      endDate: endDate,
    };
    const res = await operateEnvApi.getEnvDataPerDay(params);

    console.log(res.data);
    res.data = {
      '2025-06-09 02:00:00': { temperature: 20, humidity: 40, noise: 50, luminance: 45000, brightness: 28000, pm25: 100, pm10: 80, co2: 200 },
      '2025-06-09 03:00:00': { temperature: 21, humidity: 80, noise: 50, luminance: 45000, brightness: 28000, pm25: 100, pm10: 120, co2: 800 },
      '2025-06-09 04:00:00': { temperature: 22.2, humidity: 45, noise: 150, luminance: 45000, brightness: 28000, pm25: 250, pm10: 250, co2: 800 },
      '2025-06-09 05:00:00': { temperature: 24, humidity: 30, noise: 100, luminance: 45000, brightness: 28000, pm25: 90, pm10: 420, co2: 800 },
      '2025-06-09 06:00:00': { temperature: 25, humidity: 60, noise: 120, luminance: 45000, brightness: 28000, pm25: 90, pm10: 358, co2: 800 },
      '2025-06-09 07:00:00': { temperature: 26, humidity: 75, noise: 140, luminance: 45000, brightness: 28000, pm25: 200, pm10: 420, co2: 800 },
      '2025-06-09 08:00:00': { temperature: 27, humidity: 70, noise: 80, luminance: 45000, brightness: 28000, pm25: 230, pm10: 500, co2: 1800 },
    };

    //排序
    const sortedData = Object.entries(res.data)
      .sort(([dateA], [dateB]) => dateA.localeCompare(dateB))
      .reduce(
        (acc, [date, value]) => {
          acc.dates.push(date);
          acc.humidity.push(value.humidity);
          acc.temperature.push(value.temperature);
          return acc;
        },
        { dates: [], humidity: [], temperature: [] }
      );

    // 更新图表数据
    tempHumidityInstance.setOption({
      xAxis: {
        data: sortedData.dates,
      },
      series: [
        {
          data: sortedData.humidity,
        },
        {
          data: sortedData.temperature,
        },
      ],
    });
  };

  //-------------------- -----------处理高温和潮湿排行榜数据---------------------------------
  const processTempHumidity = (tempData) => {
    if (!tempData) return null;

    // 处理数据
    const result = {
      yAxis: [], // 日期数组
      data: [], // 温度数据
    };

    // 将对象的键值对转换为数组对象
    const tempArray = Object.entries(tempData).map(([date, value]) => ({
      date,
      value: parseFloat(value), // 将字符串转换为数值
    }));

    // 提取日期为 DD日 格式
    tempArray.forEach((item) => {
      const day = item.date.split('-')[2];
      result.yAxis.push(`${day}日`);
      result.data.push(item.value);
    });

    return result;
  };

  //-------------------------------------高温、潮湿日期排行榜------------------------------------
  // 配置高温图表
  const highTempOption = {
    tooltip: {
      trigger: 'axis',
      formatter: function (params) {
        return params[0].name + ': ' + params[0].value + ' ℃';
      },
    },
    grid: {
      top: '10%',
      left: '0%',
      right: '15%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      name: '温度(°C)',
      min: 0,
      max: 50,
      interval: 10,
      axisLine: {
        show: true,
      },
      axisLabel: {
        formatter: function (value) {
          return value % 5 === 0 ? value : '';
        },
      },
    },
    yAxis: {
      type: 'category',
      name: `${timeValue ? timeValue : currentMonth}`,
      // 数据已按温度从高到低重新排序
      data: [],
      axisLabel: {
        formatter: '{value}',
        margin: 16,
      },
    },
    series: [
      {
        name: '正温度',
        type: 'bar',
        data: [], // 只包含正温度值
        barWidth: '40%',
        barMaxWidth: 15,
        itemStyle: {
          // 使用回调函数根据数据索引（排名）动态设置颜色
          color: function (params) {
            // 获取数据索引（排名）
            const index = params.dataIndex;
            // 颜色数组，从深红到浅橙
            const colors = [
              '#ff8505', // 最浅的橙色（第一名）
              '#ff7807',
              '#f96b0a',
              '#f35e0c',
              '#ec520f',
              '#e44511',
              '#dc3714',
              '#d02a17',
              '#c41d19',
              '#b7191c', // 最深的红色（第十名）
            ];

            return colors[index];
          },
          borderRadius: [0, 4, 4, 0],
        },
        label: {
          show: true,
          position: 'right',
          formatter: '{c}°C',
          fontSize: 12,
          color: '#606266',
          distance: 5,
        },
      },
    ],
  };

  // 配置潮湿图表
  const highHumidityOption = {
    tooltip: {
      trigger: 'axis',
      formatter: function (params) {
        return params[0].name + ': ' + params[0].value + ' %';
      },
    },
    grid: {
      top: '10%',
      left: '0%',
      right: '14%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      name: '湿度(%)',
      min: 0,
      max: 100,
      interval: 10,
      axisLine: {
        show: true,
      },
      axisLabel: {
        formatter: function (value) {
          return value % 5 === 0 ? value : '';
        },
      },
    },
    yAxis: {
      type: 'category',
      name: `${timeValue ? timeValue : currentMonth}`,
      // 本月日期，按湿度从高到低排序
      data: [],
      axisLabel: {
        formatter: '{value}',
      },
    },
    series: [
      {
        name: '湿度',
        type: 'bar',
        // 对应日期的湿度数据，从高到低排序
        data: [],
        barWidth: '40%',
        barMaxWidth: 15,
        itemStyle: {
          // 使用回调函数根据数据索引（排名）动态设置颜色
          color: function (params) {
            // 获取数据索引（排名）
            const index = params.dataIndex;
            // 颜色数组，从浅蓝到深蓝
            const colors = [
              '#91D5FF', // 最浅的蓝色（第一名）
              '#69C0FF',
              '#40A9FF',
              '#1890FF',
              '#096DD9',
              '#0050B3',
              '#003A8C',
              '#002766',
              '#001A4D',
              '#000D33', // 最深的蓝色（第十名）
            ];

            return colors[index];
          },
          borderRadius: [0, 4, 4, 0],
        },
        label: {
          show: true,
          position: 'right',
          formatter: '{c}%',
          fontSize: 12,
          color: '#606266',
          distance: 5,
        },
      },
    ],
  };
  //更新高温、潮湿排行榜数据
  const updateTopChartData = async (chartType) => {
    const isTemp = chartType === 'temperature';
    const chartInstance = isTemp ? highTempChartInstance : highHumidityChartInstance;

    // 获取基于时间值的日期范围
    const { beginDate, endDate } = getDateRangeFromTimeValue();

    const params = {
      deviceId: deviceId,
      beginDate: beginDate,
      endDate: endDate,
      topNum: 10, // top10条数据
      envData: chartType,
    };

    const res = await operateEnvApi.getEnvDataTop(params);
    console.log(res.data);
    res.data = {
      '2025-06-01': '28.5',
      '2025-06-02': '25.5',
      '2025-06-03': '24.5',
      '2025-06-04': '26.5',
      '2025-06-05': '27.5',
      '2025-06-06': '28.5',
      '2025-06-07': '28.5',
      '2025-06-08': '28.5',
      '2025-06-09': '27.5',
    };

    const chartData = processTempHumidity(res.data);
    console.log(chartData);

    // 更新图表标题
    const monthDisplay = timeValue ? timeValue : currentMonth;

    chartInstance.setOption({
      yAxis: {
        name: monthDisplay,
        data: [...chartData.yAxis].reverse(),
      },
      series: [
        {
          data: [...chartData.data].reverse(),
        },
      ],
    });
  };

  //-------------------------------------光强亮度-----------------------------------------
  //默认选择当月
  const lumBrightnessChartDate = ref('today');
  // 配置光强亮度图表
  const lumBrightnessOption = {
    tooltip: {
      trigger: 'axis',
      formatter: function (params) {
        let result = params[0].axisValue + '<br/>';
        params.forEach((param) => {
          if (param.value !== '-') {
            let unit = param.seriesName === '光强' ? ' lux' : ' cd/m²';
            result += param.marker + param.seriesName + ': ' + param.value + unit + '<br/>';
          }
        });
        return result;
      },
    },
    legend: {
      data: ['光强', '亮度'],
      top: 0,
    },
    grid: {
      left: '1%',
      right: '1%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: [],
    },
    yAxis: [
      {
        type: 'value',
        name: '光强(lux)',
        min: 100,
        max: 100000,
        interval: null,
        axisLine: { show: true, lineStyle: { color: '#ffc53d' } },
        axisLabel: { formatter: '{value} lux' },
      },
      {
        type: 'value',
        name: '亮度(cd/m²)',
        min: 1000,
        max: 100000,
        interval: null,
        position: 'right',
        axisLine: { show: true, lineStyle: { color: '#36cfc9' } },
        axisLabel: { formatter: '{value} cd/m²' },
      },
    ],
    series: [
      {
        name: '光强',
        type: 'bar',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#ffd666' },
            { offset: 1, color: '#faad14' },
          ]),
        },
        data: [],
      },
      {
        name: '亮度',
        type: 'bar',
        yAxisIndex: 1,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#5cdbd3' },
            { offset: 1, color: '#13c2c2' },
          ]),
        },
        data: [],
      },
    ],
  };
  // 处理光强亮度数据
  const processLumBrightnessChartData = (envData) => {
    if (!envData) return null;

    // 提取有效数据
    const result = {
      xAxis: [], // 时间点数组 (HH:MM)
      luminance: [], // 光强值数组
      brightness: [], // 亮度值数组
    };

    // 过滤、排序并提取数据
    Object.entries(envData)
      // 只保留有光强或亮度数据的时间点
      .filter(([_, data]) => data && (data.luminance != null || data.brightness != null))
      // 按时间排序
      .sort(([timeA], [timeB]) => timeA.localeCompare(timeB))
      // 提取数据
      .forEach(([timeKey, data]) => {
        result.xAxis.push(timeKey.split(' ')[1].substring(0, 5)); //x轴
        result.luminance.push(data.luminance); //光强
        result.brightness.push(data.brightness); //亮度
      });

    return result;
  };
  // 更新光强亮度图表
  const updateLumBrightnessChart = async () => {
    if (!lumBrightnessChartInstance) return;

    // 获取所选日期的环境数据
    let envData = await getEnvDataByDate(lumBrightnessChartDate.value);
    if (!envData) return;
    console.log(envData);
    envData = {
      '2025-06-09 02:00:00': { temperature: 20, humidity: 40, noise: 50, luminance: 65000, brightness: 28000, pm25: 100, pm10: 80, co2: 200 },
      '2025-06-09 03:00:00': { temperature: 21, humidity: 80, noise: 50, luminance: 45000, brightness: 28000, pm25: 100, pm10: 120, co2: 800 },
      '2025-06-09 04:00:00': { temperature: 22.2, humidity: 45, noise: 150, luminance: 45000, brightness: 28000, pm25: 250, pm10: 250, co2: 800 },
      '2025-06-09 05:00:00': { temperature: 24, humidity: 30, noise: 100, luminance: 45000, brightness: 28000, pm25: 90, pm10: 420, co2: 800 },
      '2025-06-09 06:00:00': { temperature: 25, humidity: 60, noise: 120, luminance: 45000, brightness: 28000, pm25: 90, pm10: 358, co2: 800 },
      '2025-06-09 07:00:00': { temperature: 26, humidity: 75, noise: 140, luminance: 45000, brightness: 28000, pm25: 200, pm10: 420, co2: 800 },
      '2025-06-09 08:00:00': { temperature: 27, humidity: 70, noise: 80, luminance: 45000, brightness: 28000, pm25: 230, pm10: 500, co2: 1800 },
    };

    // 获取处理好的数据
    const chartData = processLumBrightnessChartData(envData);
    if (!chartData || chartData.xAxis.length === 0) return;

    // 更新图表
    lumBrightnessChartInstance.setOption({
      xAxis: { data: chartData.xAxis },
      series: [{ data: chartData.luminance }, { data: chartData.brightness }],
    });
  };

  //--------------------------------------噪音-------------------------------------------
  //默认选择当月（目前接口查的是今日昨日的数据）
  const noiseChartDate = ref('today');

  // 配置噪音图表
  const noiseRadarOption = {
    tooltip: {
      trigger: 'item',
      formatter: function (params) {
        let result = '噪音监测数据<br/>';
        params.data.value.forEach((val, index) => {
          let quality = val <= 30 ? '【非常安静】' : val <= 50 ? '【安静】' : val <= 70 ? '【中等】' : val <= 90 ? '【吵闹】' : '【极吵】';
          result += `${params.marker}${noiseRadarOption.radar.indicator[index].name}: ${val} dB ${quality}<br/>`;
        });
        return result;
      },
    },
    radar: {
      center: ['50%', '50%'],
      radius: '65%',
      indicator: [
        { name: '凌晨\n(00:00-05:00)', max: 130 },
        { name: '早晨\n(05:00-09:00)', max: 130 },
        { name: '白天\n(09:00-15:00)', max: 130 },
        { name: '傍晨\n(15:00-20:00)', max: 130 },
        { name: '夜晚\n(20:00-24:00)', max: 130 },
      ],
      splitArea: { areaStyle: { color: ['#f9f9f9', '#f1f1f1'] } },
    },
    series: {
      type: 'radar',
      data: [],
    },
  };
  // 处理噪音数据
  const processNoiseRadarData = (envData) => {
    if (!envData) return null;

    // 时间段定义
    const timeRanges = [
      { name: '凌晨\n(00:00-05:00)', hours: ['00:00', '01:00', '02:00', '03:00', '04:00'] }, // 凌晨
      { name: '早晨\n(05:00-09:00)', hours: ['05:00', '06:00', '07:00', '08:00'] }, // 早晨
      { name: '白天\n(09:00-15:00)', hours: ['09:00', '10:00', '11:00', '12:00', '13:00', '14:00'] }, // 白天
      { name: '傍晨\n(15:00-20:00)', hours: ['15:00', '16:00', '17:00', '18:00', '19:00'] }, // 傍晚
      { name: '夜晚\n(20:00-24:00)', hours: ['20:00', '21:00', '22:00', '23:00'] }, // 夜晚
    ];

    // 根据选择的日期获取正确的日期字符串
    const selectedDate = noiseChartDate.value === 'today' ? dayjs().format('YYYY-MM-DD') : dayjs().subtract(1, 'day').format('YYYY-MM-DD');
    // 计算各时间段平均噪音值
    const noiseValues = timeRanges.map((range) => {
      let sum = 0,
        count = 0;

      range.hours.forEach((hour) => {
        const key = `${selectedDate} ${hour}:00`;
        if (envData[key]?.noise != null) {
          sum += parseFloat(envData[key].noise);
          count++;
        }
      });

      return count ? parseFloat((sum / count).toFixed(1)) : 0;
    });

    // 返回雷达图所需的数据格式
    return {
      indicator: timeRanges.map((range) => ({ name: range.name, max: 150 })),
      data: [
        {
          value: noiseValues,
          name: '噪音监测',
          areaStyle: {
            color: new echarts.graphic.RadialGradient(0.5, 0.5, 1, [
              { color: 'rgba(255, 145, 124, 0.7)', offset: 0 },
              { color: 'rgba(255, 145, 124, 0.3)', offset: 1 },
            ]),
          },
        },
      ],
    };
  };
  // 更新噪音图表
  const updateNoiseChart = async () => {
    if (!noiseRadarChartInstance) return;

    // 获取所选日期的环境数据
    let envData = await getEnvDataByDate(noiseChartDate.value);
    if (!envData) return;
    console.log(envData);
    envData = {
      '2025-06-09 17:00:00': { temperature: 20, humidity: 40, noise: 50, luminance: 65000, brightness: 28000, pm25: 100, pm10: 80, co2: 200 },
      '2025-06-09 20:00:00' : { temperature: 20, humidity: 40, noise: 50, luminance: 65000, brightness: 28000, pm25: 100, pm10: 80, co2: 200 },
      '2025-06-09 02:00:00': { temperature: 20, humidity: 40, noise: 50, luminance: 65000, brightness: 28000, pm25: 100, pm10: 80, co2: 200 },
      '2025-06-09 03:00:00': { temperature: 21, humidity: 80, noise: 50, luminance: 45000, brightness: 28000, pm25: 100, pm10: 120, co2: 800 },
      '2025-06-09 04:00:00': { temperature: 22.2, humidity: 45, noise: 150, luminance: 45000, brightness: 28000, pm25: 250, pm10: 250, co2: 800 },
      '2025-06-09 05:00:00': { temperature: 24, humidity: 30, noise: 100, luminance: 45000, brightness: 28000, pm25: 90, pm10: 420, co2: 800 },
      '2025-06-09 06:00:00': { temperature: 25, humidity: 60, noise: 120, luminance: 45000, brightness: 28000, pm25: 90, pm10: 358, co2: 800 },
      '2025-06-09 07:00:00': { temperature: 26, humidity: 75, noise: 140, luminance: 45000, brightness: 28000, pm25: 200, pm10: 420, co2: 800 },
      '2025-06-09 08:00:00': { temperature: 27, humidity: 70, noise: 80, luminance: 45000, brightness: 28000, pm25: 230, pm10: 500, co2: 1800 },
      '2025-06-09 12:00:00': { temperature: 28, humidity: 80, noise: 80, luminance: 45000, brightness: 28000, pm25: 230, pm10: 500, co2: 1800 },
    };
    // 处理和更新雷达图...
    const chartData = processNoiseRadarData(envData);
    if (!chartData) return;

    noiseRadarChartInstance.setOption({
      radar: {
        indicator: chartData.indicator,
      },
      series: {
        data: chartData.data,
      },
    });
  };

  // 组件挂载
  onMounted(async () => {
    initCharts();
    await getEnvDataByDate('today'); //默认加载当月的数据
    updateAirQualityChartData(); // 更新空气质量数据
    updateTempHumidityChartData(); // 更新温湿度数据
    updateTopChartData('temperature'); // 更新高温top数据
    updateTopChartData('humidity'); // 更新潮湿top数据
    updateNoiseChart(); // 更新噪音图表
    updateLumBrightnessChart(); // 更新光强亮度图表
  });

  // 组件卸载时清理资源
  onUnmounted(() => {
    // 销毁图表实例，释放资源
    tempHumidityInstance?.dispose();
    highTempChartInstance?.dispose();
    airQualityChartInstance?.dispose();
    noiseRadarChartInstance?.dispose();
    highHumidityChartInstance?.dispose();
    lumBrightnessChartInstance?.dispose();
  });
</script>

<style scoped>
  .title {
    color: #606266;
    font-weight: bold;
    font-size: 18px;
  }

  .category-name {
    color: #409eff;
    font-size: 18px;
  }

  .background {
    background-color: #f0f2f5;
    border-radius: 12px;
  }
  .charts-container {
    margin-top: 16px;
  }

  .chart-card {
    background-color: #ffffff;
    border-radius: 12px;
    padding: 20px;
    height: 100%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  :deep(.ant-radio-button-wrapper) {
    border: 1px solid #40a9ff;
    color: #40a9ff;
    background: white;
    font-size: 12px;
    height: 26px;
    line-height: 26px;
    padding: 0 12px;
  }
  .chart-title {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 16px;
    text-align: center;
    position: relative;
    padding-bottom: 8px;
  }
</style>
