<!--
  * 计划下达投放计划选择模态框
  *
  * @Author:    潘显镇
  * @Date:      2025-05-30 
  * @Copyright  2025 
-->
  
<template>
    <a-modal title="选择投放计划" :width="1000" :open="visibleFlag" @cancel="onClose" :maskClosable="false"
        :destroyOnClose="true">

        <div style="margin-bottom: 16px;">
            <h3>已选设备信息</h3>
            <a-descriptions :column="4" size="small" bordered>
                <a-descriptions-item label="设备名称">
                    {{ deviceInfo.deviceName }}
                </a-descriptions-item>
                <a-descriptions-item label="备注名称">
                    {{ deviceInfo.deviceNoteName }}
                </a-descriptions-item>
                <a-descriptions-item label="所属产品">
                    {{ deviceInfo.productName }}
                </a-descriptions-item>
                <a-descriptions-item label="所属区域">
                    {{ deviceInfo.regionName }}
                </a-descriptions-item>
            </a-descriptions>
        </div>
        <!-- 表格 -->
        <a-table :columns="columns" :data-source="campaignList" :loading="tableLoading" :pagination="false"
            :row-selection="{ 
          type: 'radio', 
          selectedRowKeys: selectedRowKeyList, 
          onChange: handleSelectionChange 
        }" row-key="id" bordered size="middle">
            <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'reviewStatus'">
                    {{ MEDIA_STATUS_ENUM.getDesc(record.reviewStatus) }}
                </template>
            </template>
        </a-table>

        <div class="smart-query-table-page">
            <a-pagination showSizeChanger showQuickJumper show-less-items :pageSizeOptions="PAGE_SIZE_OPTIONS"
                :defaultPageSize="10" v-model:current="pageNum" v-model:pageSize="pageSize" :total="total"
                @change="handlePageChange" @showSizeChange="handlePageSizeChange"
                :show-total="(total) => `共${total}条`" />
        </div>

        <template #footer>
            <a-space>
                <a-button @click="onPrevious">上一步</a-button>
                <a-button type="primary" @click="onSubmit" :disabled="selectedRowKeyList.length !== 1">确定下达</a-button>
            </a-space>
        </template>
    </a-modal>
</template>
<script setup>
import { reactive, ref } from 'vue';
import { message } from 'ant-design-vue';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { smartSentry } from '/@/lib/smart-sentry';
import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
import { mediaCampaignApi } from '/@/api/business/media/campaign/media-campaign-api';
import { MEDIA_STATUS_ENUM } from '/@/constants/business/media/material/media-material-const';

// ---------------------------- 表格列定义 ----------------------------
const columns = [
  {
    title: '投放计划名称',
    dataIndex: 'mediaCampaignName',
    ellipsis: true,
  },
  {
    title: '播放总时长(秒)',
    dataIndex: 'playDuration',
    ellipsis: true,
  },
  {
    title: '审核状态',
    dataIndex: 'reviewStatus',
    ellipsis: true,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    ellipsis: true,
  },
  {
    title: '审核时间',
    dataIndex: 'reviewTime',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    ellipsis: true,
  }
];

// ---------------------------- 数据和状态 ----------------------------
const visibleFlag = ref(false);
const loading = ref(false);
const tableLoading = ref(false);
const deviceInfo = ref({});
const campaignList = ref([]);
const total = ref(0);
const pageNum = ref(1);
const pageSize = ref(10);
const selectedRowKeyList = ref([]);
const selectedCampaigns = ref([]);

// 显示模态框
function show(device) {
  visibleFlag.value = true;
  deviceInfo.value = device;
  selectedRowKeyList.value = [];
  selectedCampaigns.value = [];
  pageNum.value = 1;
  loadCampaignList();
}

// 关闭模态框
function onClose() {
  visibleFlag.value = false;
}

// 返回上一步（设备选择）
function onPrevious() {
  visibleFlag.value = false;
  emits('goBack');
}

// 加载投放计划列表
async function loadCampaignList() {
  if (!deviceInfo.value || !deviceInfo.value.id) {
    return;
  }
  
  loading.value = true;
  tableLoading.value = true;
  
  try {
    const param = {
      deviceId: deviceInfo.value.id,
      pageNum: pageNum.value,
      pageSize: pageSize.value
    };
    
    const res = await mediaCampaignApi.getMediaCampaignByDevice(param);
    
    if (res && res.data) {
      campaignList.value = res.data.list || [];
      total.value = res.data.total || 0;
    } else {
      campaignList.value = [];
      total.value = 0;
    }
  } catch (error) {
    smartSentry.captureError(error);
    message.error('获取投放计划列表失败');
    campaignList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
    tableLoading.value = false;
  }
}

// 处理页码变化
function handlePageChange(page) {
  pageNum.value = page;
  loadCampaignList();
}

// 处理每页条数变化
function handlePageSizeChange(current, size) {
  pageNum.value = 1;
  pageSize.value = size;
  loadCampaignList();
}

// 选择表格行
function handleSelectionChange(selectedRowKeys, selectedRows) {
  selectedRowKeyList.value = selectedRowKeys;
  selectedCampaigns.value = selectedRows;
}

// 确认下达计划
async function onSubmit() {
  if (selectedCampaigns.value.length !== 1) {
    message.warning('请选择一个投放计划');
    return;
  }

  try {
    SmartLoading.show();
    
    const param = {
      campaignId: selectedCampaigns.value[0].id,
      deviceId: deviceInfo.value.id
    };
    
    await mediaCampaignApi.issuePlanToDevice(param);
    message.success('计划下达成功');
    emits('issueSuccess');
    onClose();
  } catch (error) {
    smartSentry.captureError(error);
    message.error('计划下达失败');
  } finally {
    SmartLoading.hide();
  }
}

// ------------------------ 事件 ------------------------
const emits = defineEmits(['issueSuccess', 'goBack']);

defineExpose({
  show,
});
</script>
