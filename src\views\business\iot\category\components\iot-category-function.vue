<!--
  * 功能定义
  *
  * @Author:    文希希
  * @Date:      2025-03-26 17:17:51
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
  <!-- 提示行 -->
  <a-row>
    <a-col :span="24">
      <a-alert type="info" show-icon closable>
        <template #message>
          <a-typography>
            <a-typography-text>当前展示的物模型是已发布到线上的版本 {{ versionTip }}，如需修改，请点击 </a-typography-text>
            <a-typography-text type="primary" class="cursor" @click="showDraft" style="color: #2c77f2">编辑草稿</a-typography-text>
          </a-typography>
        </template>
      </a-alert>
    </a-col>
  </a-row>
  <!-- 按钮行 -->
  <a-row style="padding-top: 20px">
    <a-button type="primary" @click="showDraft">
      <template #icon><aim-outlined /></template>
      前往编辑草稿
    </a-button>
    <a-tooltip title="查看物模型的 TSL(Thing Specification Language) 描述语言" placement="top" color="#2c77f2">
      <a-button style="margin-left: 8px">物模型 TSL</a-button>
    </a-tooltip>

    <a-tooltip title="将JSON格式的物模型配置读取并导出" placement="top" color="#2c77f2">
      <a-button style="margin-left: 8px">物模型 导出</a-button>
    </a-tooltip>

    <a-select style="width: 260px; margin-left: 8px" placeholder="历史版本"> </a-select>
  </a-row>

  <!-- 表格 -->
  <a-table :dataSource="tableData" :columns="columns" :pagination="false" bordered style="padding-top: 10px"></a-table>
</template>

<script setup>
  import { ref, onMounted } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { useCategoryStore } from '/@/store/modules/business/iot/category';

  const props = defineProps({
    categoryId: {
      type: [String, Number],
      required: true,
    },
  });

  //-------------------------页面跳转------------------------
  const route = useRoute();
  const router = useRouter();
  const categoryStore = useCategoryStore();

  function showDraft() {
    const categoryId = categoryStore.getCategoryId();
    if (!categoryId) {
      message.error('未获取到品类ID');
      return;
    }

    router.push({
      path: '/iot/category/draft',
      query: { categoryId },
    });
  }

  //-----------------------------------表格-----------------------
  //表格列定义
  const columns = [
    {
      title: '#',
      width: 50,
      dataIndex: 'index',
    },
    {
      title: '功能类型',
      dataIndex: 'functionType',
    },
    {
      title: '功能名称',
      dataIndex: 'name',
    },
    {
      title: '标识符',
      dataIndex: 'identifier',
    },
    {
      title: '数据类型',
      dataIndex: 'fieldType',
    },
    {
      title: '数据定义',
      dataIndex: 'definition',
    },
    {
      title: '操作',
      dataIndex: 'action',
    },
  ];

  const tableData = ref([]);

  onMounted(() => {
    if (props.categoryId) {
      categoryStore.setCategoryId(props.categoryId);
    }
  });
</script>

<style scoped></style>
