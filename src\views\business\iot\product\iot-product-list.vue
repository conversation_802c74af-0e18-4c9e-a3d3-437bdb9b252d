<!--
  * 设备管理
  *
  * @Author:    丘瑚珊
  * @Date:      2025-02-11 15:38:08
  * @Copyright  2025 电子科技大学中山学院大数据与智能计算实验室
-->
  
<template>
  <div class="device-management">
   <!---------- 查询表单form begin ----------->
   <a-form class="smart-query-form" :class="{ 'collapsed': isCollapsed }">
     <a-row class="smart-query-form-row">
       <a-form-item label="产品名称" class="smart-query-form-item" >
         <a-input style="width: 175px" v-model:value="queryForm.productName_like" placeholder="请输入产品名称"  />
       </a-form-item>
       <a-form-item label="产品编码" class="smart-query-form-item" >
         <a-input style="width: 175px" v-model:value="queryForm.productKey_like" placeholder="请输入产品编码" />
       </a-form-item>
       <a-form-item label="设备类型" class="smart-query-form-item" >
         <SmartEnumSelect width="120px" v-model:value="queryForm.deviceType_equal" placeholder="请选择设备类型" enum-name="PRODUCT_DEVICE_TYPE_ENUM" />
       </a-form-item>
       <a-form-item label="连接协议" class="smart-query-form-item" v-show="!isCollapsed">
         <SmartEnumSelect width="120px" v-model:value="queryForm.linkProtocol" placeholder="请选择连接协议" enum-name="PROTOCOL_ENUM" />
       </a-form-item>
       <a-form-item label="数据状态" class="smart-query-form-item" v-show="!isCollapsed">
         <SmartEnumSelect width="120px" v-model:value="queryForm.status_equal" placeholder="请选择数据状态" enum-name="STATUS_EQUAL_STATUS_ENUM" />
       </a-form-item>
       <a-form-item class="smart-query-form-item">
         <a-button type="primary" @click="onSearch">
           <template #icon>
             <SearchOutlined />
           </template>
           查询
         </a-button>
         <a-button @click="resetQuery" class="smart-margin-left10">
           <template #icon>
             <ReloadOutlined />
           </template>
           重置
         </a-button>
         <a-button @click="toggleCollapse" class="smart-margin-left16" style="margin-left: 10px">
           <template #icon>
             <MenuFoldOutlined v-if="!isCollapsed" />
             <MenuUnfoldOutlined v-else />
           </template>
           {{ isCollapsed ? "展开" : "收起" }}
         </a-button>
       </a-form-item>
     </a-row>
   </a-form>
 </div>

   <a-card size="small" :bordered="false" :hoverable="true">
       <!-- 表格操作行 begin -->
       <a-row class="smart-table-btn-block">
       <div class="smart-table-operate-block">
           <a-button @click="showForm" type="primary" size="small">
           <template #icon>
               <PlusOutlined />
           </template>
           新建
           </a-button>
           <a-button @click="confirmBatchDelete" type="primary" danger size="small" :disabled="selectedRowKeyList.length == 0">
           <template #icon>
               <DeleteOutlined />
           </template>
           批量删除
           </a-button>
       </div>
       <div class="smart-table-setting-block">
           <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
       </div>
       </a-row>
       
       <!---------- 表格操作行 end ----------->

       <a-list :grid="{ gutter: 10, xs: 1, sm: 1, md: 2, lg: 2, xl: 3, xxl: 4 }" :data-source="data" :loading="initLoading">
           <template #renderItem="{ item, index }">
               <a-list-item>
               <a-card
                   hoverable
                   :loading="loading"
                   size="default"
                   :style="
                   item.status == 1
                       ? 'background: linear-gradient(rgba(88, 158, 255, 0.1), white)'
                       : item.status == 0
                       ? 'background: linear-gradient(rgba(128, 128, 128, 0.1), white)'
                       : 'background: linear-gradient(rgba(128, 128, 128, 0.1), white)'
                   "
               >
                   <template #title>
                   <a  class="detail">{{ item.productName }}</a>
                   </template>
                   <template #extra>
                   <div style="width: inherit; position: absolute; right: 30px; top: 15px">No{{ index + 1 }}</div>
                   <a-checkbox v-model:checked="item.checked" style="position: absolute; right: 5px; top: 3px" @change="onSelectChange(item)"
                   /></template>
                   <div style="height: 100%; width: 100%; display: flex;" >
                     <div style="flex: 1; display: flex; flex-direction: column;z-index: 2;">
                         <span class="span-multiline-ellipsis">产品编码：{{ item.productKey }}</span>
                         <span class="span-multiline-ellipsis" >产品品类：{{item.categoryNameFull}}</span>
                         <span class="span-multiline-ellipsis">设备类型：{{ findDescByValue(item.deviceType, DEVICE_TYPE_ENUM) }}</span>
                         <span class="span-multiline-ellipsis">连接协议：{{ findDescByValue(item.linkProtocol, GATEWAY_PROTOCOL_ENUM) }}</span>
                         <span class="span-multiline-ellipsis"
                         >数据状态：
                         <a-switch 
                             :checked="item.status === 1" checked-children="已上线" un-checked-children="开发中" @change="switchChange(item)"
                         /></span>
                         <span class="span-multiline-ellipsis">更新时间：{{ item.updateTime }}</span>
                     </div>
                     <div style="flex: 1; display: flex; justify-content: center; align-items: center; z-index: 1;">
                         <img src="/@/assets/images/product/icon2.svg" alt="" style="max-width: 150px; max-height: 150px; object-fit: contain" />
                     </div>
                   </div>
                   <template #actions>
                   <span style="color: #108ee9" @click="showDetail(item)"><setting-outlined key="setting" /> 配置</span>
                   <span style="color: #108ee9" @click="showForm(item)"><FormOutlined /> 编辑</span>
                   <span style="color: #108ee9" @click="showDetail(item)"><MobileOutlined /> 管理设备</span>
                   <span style="color: #f56c6c" @click="onDelete(item)"><DeleteOutlined /> 删除</span>
                   </template>
               </a-card>
               </a-list-item>
           </template>
       </a-list>
       <div class="smart-query-table-page">
           <a-pagination
               showSizeChanger
               showQuickJumper
               show-less-items
               :pageSizeOptions="PAGE_SIZE_OPTIONS"
               :defaultPageSize="queryForm.pageSize"
               v-model:current="queryForm.pageNum"
               v-model:pageSize="queryForm.pageSize"
               :total="total"
               @change="queryData"
               @showSizeChange="queryData"
               :show-total="(total) => `共${total}条`"
           />
       </div>

       <iotProductForm ref="formRef" @reloadList="queryData" />
       <!-- <EquipmentInstanceDetail ref="detailRef" /> -->
   </a-card>
 </template>
 <script setup>
   import { reactive, ref, onMounted } from 'vue';
   import { message, Modal } from 'ant-design-vue';
   import { SmartLoading } from '/@/components/framework/smart-loading';
   import { smartSentry } from '/@/lib/smart-sentry';
   import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
   import DictSelect from '/@/components/support/dict-select/index.vue';// 字典下拉框
   import TableOperator from '/@/components/support/table-operator/index.vue';
   import { iotProductApi } from '/@/api/business/iot/product/iot-product-api.js';
   import { DEVICE_TYPE_ENUM ,GATEWAY_PROTOCOL_ENUM} from '/@/constants/business/iot/product/iot-product-const.js';
   import iotProductForm from './components/iot-product-form.vue';
   import { useRouter, useRoute } from 'vue-router';
   import { DICT_CODE_ENUM } from '/@/constants/support/dict-const.js';
   import SmartEnumSelect from '/@/components/framework/smart-enum-select/index.vue';

   //----------------------------路由跳转--------------------------------

   const route = useRoute();
   const router = useRouter();

   function showDetail(data) {
   router.push({
     path: '/iot/product/detail', // 跳转到chang'pin详情页
     query: {
       id: data.id,
       name: data.productName,
     },
   });
 }


   // ---------------------------- 表格列 ----------------------------
   const columns = []//? 待定
   const initLoading = ref(false);
   const loading = ref(false);
   const state = reactive({
     checked1: true,
   });
 
   // ---------------------------- 查询数据表单和方法 ----------------------------
 
   const queryFormState = {
     productName_like: undefined,
     productKey_like: undefined,
     deviceType_equal: undefined,
     status_equal: undefined,
     linkProtocol: undefined,
     pageNum: 1,
     pageSize: 9,
     sortItemList: [{ isAsc: false, column: 'create_time' }],
   };
   // 查询表单form
   const queryForm = reactive({ ...queryFormState });
   // 表格数据
   const data = ref([]);
   // 总数
   const total = ref(0);
    // 重置查询条件
 function resetQuery() {
   let pageSize = queryForm.pageSize;
   Object.assign(queryForm, queryFormState);
   queryForm.pageSize = pageSize;
   queryData();
 }

 // 搜索
 function onSearch() {
   console.log(queryForm,"查询")
   queryForm.pageNum = 1;
   queryData();
 }

 // 查询数据
 async function queryData() {
   initLoading.value = true;
   loading.value = true;
   try {
     let queryResult = await iotProductApi.queryPage(queryForm);
     total.value = queryResult.data.total;
     data.value = queryResult.data.records;
     selected = [];
     data.value.forEach((item) => {
       selected.push({
         id: item.id,
         checked: false,
       });
     });
   } catch (e) {
     smartSentry.captureError(e);
   } finally {
     initLoading.value = false;
     loading.value = false;
   }
 }
 onMounted(queryData);

 // ---------------------------- 添加/修改 ----------------------------
 const formRef = ref();
 const detailRef = ref();

 function showForm(data) {
   console.log("打开from表单")
   formRef.value.show(data);
 }
//   function showDetail(data) {
//     detailRef.value.show(data);
//   }

 async function switchChange(data) {
   if (!data.id) {
     return;
   }
   try {
     console.log("调用修改接口啦",data.status)
     data.status = data.status === 1 ? 0 : 1;
     await iotProductApi.update(data);
     message.success('操作成功');
     queryData();
   } catch (err) {
     smartSentry.captureError(err);
   }
 }

 // ---------------------------- 单个删除 ----------------------------
 //确认删除
 function onDelete(data) {
   Modal.confirm({
     title: '提示',
     content: '确定要删除选吗?',
     okText: '删除',
     okType: 'danger',
     onOk() {
       requestDelete(data);
     },
     cancelText: '取消',
     onCancel() {},
   });
 }

 //请求删除
 async function requestDelete(data) {
   console.log("调用删除接口啦")
   SmartLoading.show();
   try {
     let deleteForm = {
       goodsIdList: selectedRowKeyList.value,
     };
     const arr = [data.id];
     await iotProductApi.delete(arr);
     message.success('删除成功');
     queryData();
   } catch (e) {
     smartSentry.captureError(e);
   } finally {
     SmartLoading.hide();
   }
 }

 // ---------------------------- 批量删除 ----------------------------

 // 选择表格行
 const selectedRowKeyList = ref([]);
 let selected = [];

 function onSelectChange(item) {
   const index = selected.findIndex((select) => select.id === item.id);
   if (selected[index].checked) {
     selected[index].checked = false;
     selectedRowKeyList.value = selectedRowKeyList.value.filter((id) => id !== item.id);
   } else {
     selected[index].checked = true;
     selectedRowKeyList.value.push(item.id);
   }
 }

 // 批量删除
 function confirmBatchDelete() {
   Modal.confirm({
     title: '提示',
     content: '确定要批量删除这些数据吗?',
     okText: '删除',
     okType: 'danger',
     onOk() {
       requestBatchDelete();
     },
     cancelText: '取消',
     onCancel() {},
   });
 }

 //请求批量删除
 async function requestBatchDelete() {
   console.log("调用批量删除接口啦")
   try {
     SmartLoading.show();
     await iotProductApi.batchDelete(selectedRowKeyList.value);
     message.success('删除成功');
     queryData();
   } catch (e) {
     smartSentry.captureError(e);
   } finally {
     SmartLoading.hide();
   }
 }

 //返回键值
 function findDescByValue(value, enumObject) {
   // 遍历枚举对象的键值对
   for (const key in enumObject) {
       if (enumObject[key].value === value) {
       return enumObject[key].desc; // 找到对应的描述后返回
       }
   }
   return null; // 如果找不到对应的值，则返回 null
 }
 // 收缩，展开
 const isCollapsed = ref(false);
 const toggleCollapse = () => {
 isCollapsed.value = !isCollapsed.value;
};
 </script>
 
 <style scoped lang="less">
   :deep(.ant-card-body) {
     padding: 10px 20px;
   }
   .scroll-container {
     height: 580px; /* 设置容器的高度 */
     overflow-y: auto; /* 启用 y 轴滚动 */
   }
 
   ::-webkit-scrollbar-track {
     background: #f1f1f1;
   }
 
   ::-webkit-scrollbar-thumb {
     background: #888;
     border-radius: 8px;
   }
 
   ::-webkit-scrollbar-thumb:hover {
     background: #555;
   }
   .span-multiline-ellipsis {
     // display: flex;
     // width: 230px;
     // margin: 5px;
     display: -webkit-box;          /* Flexbox 模式 */
     -webkit-box-orient: vertical; /* 设置盒子为垂直方向 */
     overflow: hidden;             /* 隐藏多余内容 */
     text-overflow: ellipsis;      /* 增加省略号 */
     -webkit-line-clamp: 1;        /* 限制显示2行，多出的内容隐藏 */
     max-width: 100%;              /* 设置最大宽度 */
     line-height: 1.5;             /* 设置行高（根据需要调整） */
     max-height: calc(1.5em * 2);  /* 与行高和行数匹配 */
     word-break: break-word;       /* 防止单词溢出容器 */
     font-size: 0.8em;             /* 设置字体大小 */
     margin-bottom: 10px;           /* 增加下边距 */
   }

   .detail{
     display: inline-block;
     padding: 5px 10px;
     background-color: rgba(88, 158, 255, 0.1); /* 设置背景颜色为淡蓝色 */
     border: 1px solid rgba(88, 158, 255, 0.1); /* 边框颜色 */
     border-radius: 8px; /* 圆角 */
     color: #2c77f1; /* 字体颜色 */
     font-size: 16px; /* 字体大小 */
     text-align: center; /* 文字居中 */
     font-weight: bold; /* 加粗字体 */
     box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
   }
 </style>
 