<!--
  * 巡检方案表单
  *
  * @Author:    潘显镇
  * @Date:      2025-04-11
-->
<template>
  <a-modal
    :title="form.id ? '编辑巡检方案' : '新增巡检方案'"
    :width="1200"
    :open="visibleFlag"
    @cancel="onClose"
    :maskClosable="false"
    :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 6 }">
      <a-row>
        <a-col :span="12">
          <a-form-item label="方案名称" name="programmeName">
            <a-input
              style="width: 100%"
              v-model:value="form.programmeName"
              placeholder="请输入巡检方案名称"
            />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="巡检频率" name="inspectionFrequency">
            <a-select
              style="width: 100%"
              v-model:value="form.inspectionFrequency"
              placeholder="请选择巡检频率"
              :options="TIME_UNIT_ENUM.getOptions()"
            />
          </a-form-item>
        </a-col>
    
          <a-col :span="12">
          <a-form-item label="频次(天)" name="dailyInspectionCount" v-if="form.inspectionFrequency === 'day'">
            <a-input-number
              style="width: 100%"
              v-model:value="form.dailyInspectionCount"
              placeholder="请输入次数"
            />
          </a-form-item>
           <a-form-item label="频次(周)" name="dailyInspectionCount" v-if="form.inspectionFrequency === 'week'">
              <a-select
              style="width: 100%"
              v-model:value="form.ruleValues"
              placeholder="请选择星期"
               mode="multiple"
               :max-tag-count="2"
               :options="WEEK_DAY_ENUM.getOptions()"
            />
              
          </a-form-item>
           <a-form-item label="频次(月)" name="dailyInspectionCount" v-if="form.inspectionFrequency === 'month'">
             <a-select
              style="width: 100%"
              v-model:value="form.ruleValues"
              placeholder="请选择日期"
               mode="multiple"
               :max-tag-count="2"
               :options="dayOptions"
            />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="状态" name="enabledFlag">
            <a-select
              style="width: 100%"
              v-model:value="form.enabledFlag"
              placeholder="请选择状态"
            >
              <a-select-option :value="true">启用</a-select-option>
              <a-select-option :value="false">停用</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="方案描述" name="description">
            <a-textarea v-model:value="form.description" placeholder="请输入方案描述" :rows="3"/>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 巡检设备列表 -->
   <a-tabs v-model:value="activeKey">
    <a-tab-pane tab="巡检设备列表" key="device">
      <div class="smart-margin-bottom10">
        <a-button type="primary" @click="addDevice" class="smart-margin-right10">
          <template #icon><PlusOutlined /></template>
          添加设备
        </a-button>
      </div>
        <a-table
          :columns="deviceColumns"
          :data-source="form.deviceList"
          :pagination="false"
          size="small"
          bordered
          rowKey="deviceId"
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'index'">
              {{ index + 1 }}
            </template>
            <template v-if="column.dataIndex === 'device'">
              <iotDeviceSelect 
                v-model:value="record.deviceId" 
                style="width: 100%"
              />
            </template>
            <template v-if="column.dataIndex === 'ruleDate'">
              <a-date-picker 
                v-model:value="record.ruleDate"
                style="width: 100%"
                placeholder="选择规定日期"
                valueFormat="YYYY-MM-DD"
              />
            </template>
            <template v-if="column.dataIndex === 'action'">
              <a-button type="link" danger @click="removeDevice(index)" size="small">删除</a-button>
            </template>
          </template>   
        </a-table>
      </a-tab-pane>
      <a-tab-pane tab="巡检项目列表" key="item">
      <!-- 巡检项目列表-->
       <div class="smart-margin-bottom10">
        <a-button type="primary" @click="addItem" class="smart-margin-right10">
          <template #icon><PlusOutlined /></template>
          添加巡检项
        </a-button>
       </div>
        <a-table
          :columns="itemColumns"
          :data-source="form.itemList"
          :pagination="false"
          size="small"
          bordered
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'index'">
              {{ index + 1 }}
            </template>
            <template v-if="column.dataIndex === 'itemName'">
              <a-input v-model:value="record.itemName" placeholder="请输入检查项"/>
            </template>
            <template v-if="column.dataIndex === 'inspectionMethod'">
              <a-input v-model:value="record.inspectionMethod" placeholder="请输入检查方法"/>
            </template>
            <template v-if="column.dataIndex === 'minValue'">
              <a-input-number v-model:value="record.minValue" placeholder="最小值" style="width: 100%"/>
            </template>
            <template v-if="column.dataIndex === 'maxValue'">
              <a-input-number v-model:value="record.maxValue" placeholder="最大值" style="width: 100%"/>
            </template>
            <template v-if="column.dataIndex === 'description'">
              <a-input v-model:value="record.description" placeholder="请输入描述"/>
            </template>
            <template v-if="column.dataIndex === 'action'">
              <a-button type="link" danger @click="removeItem(index)" size="small">删除</a-button>
            </template>
          </template>   
        </a-table>
      </a-tab-pane>
    </a-tabs>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script setup>
import { reactive, ref, nextTick } from 'vue';
import _ from 'lodash';
import { message } from 'ant-design-vue';
import IotDeviceSelect from '/@/components/business/iot/iot-device-select/index.vue';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { inspectProgrammeApi } from '../../../../../api/business/maintain/inspect-programme/inspect-programme-api';
import { smartSentry } from '/@/lib/smart-sentry';
import { PlusOutlined } from '@ant-design/icons-vue';
import { TIME_UNIT_ENUM } from '/@/constants/business/ops/inspect-programme-const';
import { WEEK_DAY_ENUM } from '/@/constants/week-const.js';

// ------------------------ 事件 ------------------------
const emits = defineEmits(['reloadList']);
const activeKey = ref('device');
// ------------------------ 显示与隐藏 ------------------------
const visibleFlag = ref(false);

//日期数组
const dayOptions = Array.from({ length: 31 }, (_, index) => ({
  value: index + 1,
  label: `${index + 1}号`
}))

async function show(id) {
  try{
    if(id){
      console.log(id);
    const res = await inspectProgrammeApi.getById(id);
    Object.assign(form, res.data);
    }else{
      Object.assign(form, formDefault);
    }
    visibleFlag.value = true;
    }catch (error) {
      smartSentry.error(error);
      message.error('获取巡检方案详情失败！');
    }
  nextTick(() => {
    formRef.value?.clearValidate();
  });
}

function onClose() {
  Object.assign(form, formDefault);
  form.itemList = [];
  form.deviceList = [];
  visibleFlag.value = false;
}

// ------------------------ 设备相关方法 ------------------------

function addDevice() {
  form.deviceList.push({
    deviceId: undefined,
    ruleDate: undefined
  });
}

function removeDevice(index) {
  form.deviceList.splice(index, 1);
}

// ------------------------ 巡检项目相关方法 ------------------------
function addItem() {
  form.itemList.push({
    itemName: undefined,
    inspectionMethod: undefined,
    minValue: undefined,
    maxValue: undefined,
    description: undefined
  });
}

function removeItem(index) {
  form.itemList.splice(index, 1);
}

// ------------------------ 表单 ------------------------
const formRef = ref();

const formDefault = {
  id: undefined,           // 主键ID
  programmeName: undefined,  // 方案名称
  inspectionFrequency: 'day', // 巡检频率
  dailyInspectionCount: undefined, // 巡检次数
  enabledFlag: true,           // 启用状态，默认启用
  description: undefined, // 描述
  ruleValues: [], // 规定值
  itemList: [],       // 巡检项列表
  deviceList: [], // 设备列表
};

let form = reactive({ ...formDefault });

// 设备表格列定义
const deviceColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 60,
    align: 'center'
  },
  {
    title: '设备',
    dataIndex: 'device',
    width: 300
  },
  {
    title: '规则日期',
    dataIndex: 'ruleDate',
    width: 200
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 80,
    align: 'center'
  }
];

// 巡检项目表格列定义
const itemColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 60,
    align: 'center'
  },
  {
    title: '检查项',
    dataIndex: 'itemName'
  },
  {
    title: '检查方法',
    dataIndex: 'inspectionMethod'
  },
  {
    title: '最小值',
    dataIndex: 'minValue',
    width: 120
  },
  {
    title: '最大值',
    dataIndex: 'maxValue',
    width: 120
  },
  {
    title: '描述',
    dataIndex: 'description'
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 80,
    align: 'center'
  }
];

const rules = {
  programmeName: [
    { required: true, message: '方案名称不能为空' },
  ],
  inspectionFrequency: [
    { required: true, message: '巡检频率不能为空' }
  ],
  dailyInspectionCount: [
    { required: true, message: '巡检次数不能为空' },
    { type: 'number', min: 1, message: '巡检次数至少为1' }
  ],
  enabledFlag: [
    { required: true, message: '请选择状态' }
  ]
};

// 点击确定，验证表单
async function onSubmit() {
  try {
    //  if(form.inspectionFrequency=== 'week'|| form.inspectionFrequency=== 'month'){
    //     form.dailyInspectionCount = null;
    //   }else if(form.inspectionFrequency=== 'day'){
    //     form.ruleValues = null;
    //   }
    await formRef.value.validateFields();
    console.log(form);
    save();
  } catch (err) {
    message.error('请检查表单数据是否填写正确!');
  }
}

// 新建、编辑API
async function save() {
  SmartLoading.show();
  try {
    if (form.id) {
      await inspectProgrammeApi.update(form);
    } else {
      await inspectProgrammeApi.add(form);
    }
    message.success('操作成功');
    emits('reloadList');
    onClose();
  } catch (err) {
    smartSentry.captureError(err);
  } finally {
    SmartLoading.hide();
  }
}


defineExpose({
  show,
});
</script>