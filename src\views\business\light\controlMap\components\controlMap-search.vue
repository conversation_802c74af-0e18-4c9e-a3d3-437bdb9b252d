<!--
  * 控制地图-搜索
  *
  * @Author:    骆伟林
  * @Date:      2025-03-24 17:51:27
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
  <div class="search-drawer">
    <a-row>
      <a-col :span="3">
        <a-dropdown :size="small">
          <template #overlay>
            <a-menu @click="handleMenuClick">
              <a-menu-item key="1">
                区域
              </a-menu-item>
              <a-menu-item key="2">
                设备
              </a-menu-item>
            </a-menu>
          </template>
          <a-button>
            {{ searchItem.label }}
            <DownOutlined />
          </a-button>
        </a-dropdown>
      </a-col>
      <a-col :span="18" style="margin-left: 12%;">
        <div style="display: flex; align-items: center">
          <a-input-search v-model:value="search" placeholder="输入搜索">
            <template #enterButton>
              <a-button type="primary" @click="handleSearch"> 搜索
              </a-button>
            </template>
          </a-input-search>
        </div>
      </a-col>
    </a-row>
    <a-list size="small" bordered :data-source="searchList" style="margin-top: 5%; height: 92%; overflow-y: auto;" loadMore>
      <template #renderItem="{ item }">
        <a-list-item @click="handleItemClick(item)">
          <a-card :bordered="false" hoverable style="width: 100%;">
            <div class="card-content" :title="item.name">
              <div class="card-header">
                <span style="margin-right: 5px;">{{ item.name || item.deviceName }}</span>
                <span style="font-size: 14px; color: #999;">{{ item.address || item.deviceNoteName }}</span>
              </div>
              <div class="card-footer">
                <span style="font-size: 13px; color: #999; width: 50%; margin-right: 10px;">{{ item.contact || item.regionName }}</span>
                <span style="font-size: 13px; color: #999;">{{ item.phone || item.recordStatus }}</span>
              </div>
            </div>
          </a-card>
        </a-list-item>
      </template>
    </a-list>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { message } from 'ant-design-vue';
import { DownOutlined } from '@ant-design/icons-vue';
import { lightApi } from '/@/api/business/light/light';
import { on } from 'process';

const emit = defineEmits(['getTarget']);

let searchItem = ref({
  label: '区域',
  value: 'region',
  key: '1'
});

const handleMenuClick = e => {
  searchItem.value = dropItem.find(item => item.key === e.key);
  console.log(searchItem.value);
};

const dropItem = [
  {
    label: '区域',
    key: '1',
    value: 'region'
  },
  {
    label: '设备',
    key: '2',
    value: 'device'
  },
];

let search = ref('');
let deviceList = undefined;
let regionList = undefined;
let searchList = ref([]);

async function handleSearch() {
  let parm = {
    queryValue: search.value,
    queryTarget: searchItem.value.value
  };
  const res = await lightApi.queryLightInformation(parm);
  deviceList = res.data.deviceList;
  regionList = res.data.regionList;
  searchList.value = parm.queryTarget === 'region' ? res.data.regionList : res.data.deviceList;
  regionList=res.data.regionList
}
onMounted(() => {
  handleSearch();
});
function handleItemClick(item) {
  emit('getTarget', { item, deviceList, searchType: searchItem.value.value });
}
</script>

<style scoped>
.search-drawer {
  padding: 10px;
}

.list-item {
  padding: 8px;
  transition: background-color 0.3s ease;
}

.list-item:hover {
  background-color: #f0f0f0;
}
</style>