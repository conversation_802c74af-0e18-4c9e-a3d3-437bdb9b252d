/**
 * 能耗查询api
 */


import { postRequest, getRequest } from '/@/lib/axios';

export const energyApi ={
    /**
     * 能耗统计-按小时统计
     */
    energyByHour:(param)=>{
        return postRequest('/energy/chart/energyConsumeByHour',param)
    },
    /**
     * 能耗统计-按天统计
     */
    energyByDay:(param)=>{
        return postRequest('/energy/chart/energyConsumeByDay',param)
    },
    /**
     * 电流统计 - 按天
     */
    electricityByDay:(param)=>{
        return postRequest('/energy/chart/electricityByDay',param)
    },
    /**
     * 电压统计 - 按天统计
     */
    voltageByDay:(param)=>{
        return postRequest('/energy/chart/voltageByDay',param)
    },
    /**
     * 功率统计 - 按天统计
     */
    powerByDay:(param)=>{
        return postRequest('/energy/chart/powerByDay',param)
    },
    /**
     * 平均功率统计 - 返回天数累加
     */
    powerAvg:(param)=>{
        return postRequest('/energy/stats/powerAvg',param)
    },
    /**
     * 能耗累计 - 返回累计值
     */
    energyConsume:(param)=>{
        return postRequest('/energy/stats/energyConsume',param)
    },
    /**
     * 能耗平均 - 返回平均值
     */
    energyConsumeAvg:(param)=>{
        return postRequest('/energy/stats/energyConsumeAvg',param)
    },
    /**
     * 分页查询设备列表
     */
    queryDeviceList:(param)=>{
        return postRequest('/energyReport/queryPage',param)
    },
    /**
     * 分页查询设备信息
     */
    queryPageByDevice:(param)=>{
        return postRequest('/energyReport/queryPageByDevice',param)
    },
    /**
     * 分页查询区域信息
     */
    queryPageByRegion:(param)=>{
        return postRequest('/energyReport/queryPageByRegin',param)
    },
    /**
     * 根据id查询详细信息 
     */
    energyReporyId:(id)=>{
        return getRequest(`/energyReport/queryById/${id}`)
    }
}