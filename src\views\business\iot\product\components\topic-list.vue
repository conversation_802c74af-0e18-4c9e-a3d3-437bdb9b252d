<template>
    <a-card>
        <a-row>
            <a-col :span="3">
                <a-tabs v-model:activeKey="activeTab" tab-position="left" type="card" class="tabs-no-margin"
                    @change="handleTabChange">
                    <a-tab-pane key="base" tab="基础通信 Topic"></a-tab-pane>
                    <a-tab-pane key="tsl" tab="物模型通信 Topic"></a-tab-pane>
                    <a-tab-pane key="custom" tab="自定义 Topic"></a-tab-pane>
                </a-tabs>
            </a-col>
            <a-col :span="21">
                <a-card>
                    <a-table :columns="columns" :data-source="tableData" :pagination="false"
                        :row-key="(record) => record.id" bordered>
                    </a-table>

                    <div class="smart-query-table-page">
                        <a-pagination showSizeChanger showQuickJumper show-less-items :sizeOptions="PAGE_SIZE_OPTIONS"
                            :defaultsize="queryForm.size" v-model:current="queryForm.current"
                            v-model:pageSize="queryForm.size" :total="total" @change="handlePageChange"
                            @showSizeChange="handleSizeChange" :show-total="(total) => `共${total}条`" />
                    </div>
                </a-card>
            </a-col>
        </a-row>
    </a-card>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue';
import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
import { iotProductApi } from '/@/api/business/iot/product/iot-product-api.js';
import { smartSentry } from '/@/lib/smart-sentry';
const activeTab = ref('base');
const tableData = ref([]);

// 表格列定义
const columns = [
    {
        title: '#',
        dataIndex: 'seq',
        width: 80,
    },
    {
        title: '设备的Topic',
        dataIndex: 'topic',
        width: 400,
    },
    {
        title: '操作权限',
        dataIndex:'accessName',
        width:100,
    },
    {
        title: 'Topic描述',
        dataIndex: 'topicDesc',
    }
];

const formState = {
    type: 'base',
    current: 1,
    size: 10
}

const queryForm = reactive({...formState});

const total = ref(0);

// 获取数据
const getTopicList = async () => {
    try {
        const response = await iotProductApi.getTopicList(queryForm);
        
        // 添加序号
        tableData.value = response.data.records.map((item, index) => ({
            ...item,
            seq: (queryForm.current - 1) * queryForm.size + index + 1
        }));
        total.value = response.data.total;
    } catch (error) {
       smartSentry.captureError(error);
    }
};

// 监听标签页切换
const handleTabChange = (key) => {
    queryForm.type = key;
    queryForm.current = 1;
    getTopicList();
};

// 分页方法
const handlePageChange = (page) => {
    queryForm.current = page;
    getTopicList();
};

const handleSizeChange = (current, size) => {
    queryForm.current = 1;
    queryForm.size = size;
    getTopicList();
};

onMounted(() => {
    // 初始加载基础通信Topic
    queryForm.type = activeTab.value;
    getTopicList();
});
</script>
<style lang="less" scoped>
.tabs-no-margin {
    margin: 0 !important;

    :deep(.ant-tabs-nav) {
        margin: 0;
        width: 100%;
        }
    :deep(.ant-tabs-content-holder) {
        display: none;
    }
    :deep(.ant-tabs-nav-operations) {
        display: none;
    }
}
</style>