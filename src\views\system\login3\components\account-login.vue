<template>
    <a-form ref="formRef" class="login-form" :model="loginForm" :rules="rules">
        <a-form-item name="loginName">
            <a-input v-model:value.trim="loginForm.loginName" placeholder="请输入用户名" bordered />
        </a-form-item>
        <a-form-item name="password">
            <a-input-password v-model:value="loginForm.password" autocomplete="on"
                :type="showPassword ? 'text' : 'password'" placeholder="请输入密码：至少三种字符，最小 8 位" />
        </a-form-item>
        <a-form-item name="captchaCode">
            <a-input class="captcha-input" v-model:value.trim="loginForm.captchaCode" placeholder="请输入验证码" />
            <img class="captcha-img" :src="captchaBase64Image" @click="getCaptcha" />
        </a-form-item>
        <a-form-item>
            <a-checkbox v-model:checked="rememberPwd" style="color: #fff;">记住密码</a-checkbox>
        </a-form-item>
        <a-form-item>
            <div class="btn" @click="onLogin">登录</div>
        </a-form-item>
    </a-form>
</template>

<script setup>
import { message } from 'ant-design-vue';
import { onMounted, onUnmounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { loginApi } from '/@/api/system/login-api';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { LOGIN_DEVICE_ENUM } from '/@/constants/system/login-device-const';
import { useUserStore } from '/@/store/modules/system/user';
import { smartSentry } from '/@/lib/smart-sentry';
import { encryptData } from '/@/lib/encrypt';
import { localSave } from '/@/utils/local-util.js';
import LocalStorageKeyConst from '/@/constants/local-storage-key-const.js';
import { buildRoutes } from "/@/router/index.js";

const loginForm = reactive({
    loginName: 'admin',
    password: '',
    captchaCode: '',
    captchaUuid: '',
    loginDevice: LOGIN_DEVICE_ENUM.PC.value,
});

const rules = {
    loginName: [{ required: true, message: '用户名不能为空' }],
    password: [{ required: true, message: '密码不能为空' }],
    captchaCode: [{ required: true, message: '验证码不能为空' }],
};

const showPassword = ref(false);
const router = useRouter();
const formRef = ref();
const rememberPwd = ref(false);
const captchaBase64Image = ref('');

onMounted(() => {
    document.onkeyup = (e) => {
        if (e.keyCode === 13) {
            onLogin();
        }
    };
    getCaptcha();
});

onUnmounted(() => {
    document.onkeyup = null;
    stopRefreshCaptchaInterval();
});

async function onLogin() {
    formRef.value.validate().then(async () => {
        try {
            SmartLoading.show();
            let encryptPasswordForm = Object.assign({}, loginForm, {
                password: encryptData(loginForm.password),
            });
            const res = await loginApi.login(encryptPasswordForm);
            stopRefreshCaptchaInterval();
            localSave(LocalStorageKeyConst.USER_TOKEN, res.data.token ? res.data.token : '');
            message.success('登录成功');
            useUserStore().setUserLoginInfo(res.data);
            //构建系统的路由
            buildRoutes();
            router.push('/home');
        } catch (e) {
            if (e.data && e.data.code !== 0) {
                loginForm.captchaCode = '';
                getCaptcha();
            }
            smartSentry.captureError(e);
        } finally {
            SmartLoading.hide();
        }
    });
}

let refreshCaptchaInterval = null;

async function getCaptcha() {
    try {
        let captchaResult = await loginApi.getCaptcha();
        captchaBase64Image.value = captchaResult.data.captchaBase64Image;
        loginForm.captchaUuid = captchaResult.data.captchaUuid;
        beginRefreshCaptchaInterval(captchaResult.data.expireSeconds);
    } catch (e) {
        console.log(e);
    }
}

function beginRefreshCaptchaInterval(expireSeconds) {
    if (refreshCaptchaInterval === null) {
        refreshCaptchaInterval = setInterval(getCaptcha, (expireSeconds - 5) * 1000);
    }
}

function stopRefreshCaptchaInterval() {
    if (refreshCaptchaInterval != null) {
        clearInterval(refreshCaptchaInterval);
        refreshCaptchaInterval = null;
    }
}
</script>

<style lang="less" scoped>
.login-form {
    .captcha-input {
        width: 60%;
    }

    .captcha-img {
        height: 44px;
        margin-left: 10px;
        cursor: pointer;
    }

    .ant-input,
    .ant-input-affix-wrapper {
        height: 44px;
        border: 1px solid #ededed;
        border-radius: 4px;
    }

    .eye-box {
        position: absolute;
        right: 15px;
        top: 10px;

        .eye-icon {
            width: 20px;
            height: 20px;
            cursor: pointer;
        }
    }

    .btn {
        width: 350px;
        height: 50px;
        background: rgb(255, 98, 0);
        border-radius: 4px;
        font-size: 16px;
        font-weight: 700;
        text-align: center;
        color: #ffffff;
        line-height: 50px;
        cursor: pointer;
    }
}
</style>
