/**
 * 照明策略 api 封装
 *
 * @Author:    李帅兵
 * @Date:      2025-03-28 21:36:15
 * @Copyright  中山睿数信息技术有限公司 2025
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const lightStrategyApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/lightStrategy/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/lightStrategy/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/lightStrategy/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/lightStrategy/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
      return postRequest('/lightStrategy/batchDelete', idList);
  },
  /**
   *通过id获取照明策略详情 <AUTHOR>
   */
  queryDetailById: (id) => {
    return getRequest(`/lightStrategy/queryDetailById/${id}`);
},
};
