<template>
  <a-modal :title="'素材审核'" :width="500" :open="visibleFlag" @cancel="onClose" :maskClosable="false"
    :destroyOnClose="true">
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
      <a-form-item label="素材ID" name="id">
        <a-input v-model:value="form.id" disabled style="width: 300px" />
      </a-form-item>
      <a-form-item label="素材名称" name="name">
        <a-input v-model:value="form.name" disabled style="width: 300px" />
      </a-form-item>
      <a-form-item label="审核人" name="reviewerId">
        <employee-select v-model:value="form.reviewerId" style="width: 300px" @change="handleEmployeeChange" />
      </a-form-item>
      <a-form-item label="审核意见" name="reviewComment">
        <a-textarea v-model:value="form.reviewComment" placeholder="请输入审核意见" :rows="4" style="width: 300px" />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onRejected">审核不通过</a-button>
        <a-button type="primary" @click="onApproved">审核通过</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script setup>
import { reactive, ref, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { mediaMaterialApi } from '/@/api/business/media/material/media-material-api';
import { smartSentry } from '/@/lib/smart-sentry';
import EmployeeSelect from '/@/components/system/employee-select/index.vue';
import { useUserStore } from '/@/store/modules/system/user';
import { MEDIA_STATUS_ENUM } from '/@/constants/business/media/material/media-material-const';

const emits = defineEmits(['reviewSuccess']);
const visibleFlag = ref(false);
const formRef = ref();
const userStore = useUserStore();

const formDefault = {
  id: undefined,
  name: undefined,
  reviewerId: userStore.employeeId,
  reviewerName: userStore.actualName,
  reviewComment: undefined
};

const form = reactive({ ...formDefault });

const rules = {
  reviewComment: [{ required: true, message: '审核意见 必填' }]
};

// 重置表单
function resetForm() {
  Object.assign(form, formDefault);
}

async function show(record) {
  resetForm();
  console.log(record);
  if (record.id) {
    form.id = record.id;
    form.name = record.name;
    form.reviewComment = record.reviewComment;

  }
  visibleFlag.value = true;
  nextTick(() => formRef.value?.clearValidate());
}

function onClose() {
  resetForm();
  visibleFlag.value = false;
}

// 审核通过
async function onApproved() {
  SmartLoading.show();
  try {
    await formRef.value.validateFields();
    await mediaMaterialApi.approvedMaterial(form);
    message.success('审核成功');
    emits('reviewSuccess');
    onClose();
  } catch (err) {
    smartSentry.captureError(err);
  } finally {
    SmartLoading.hide();
  }
}

// 审核不通过
async function onRejected() {
  SmartLoading.show();
  try {
    await formRef.value.validateFields();
    await mediaMaterialApi.rejectedMaterial(form);
    message.success('审核成功');
    emits('reviewSuccess');
    onClose();
  } catch (err) {
    smartSentry.captureError(err);
  } finally {
    SmartLoading.hide();
  }
}
function handleEmployeeChange(employee) {
  form.reviewerName = employee.actualName;
}

defineExpose({ show });
</script>