<!--
  * 品类草稿
  *
  * @Author:    文希希
  * @Date:      2025-03-26 17:17:51
  * @Copyright  中山睿数信息技术有限公司 2025
-->

<template>
  <a-card size="small" :bordered="false" :hoverable="true">
    <!-- 信息行 -->
    <a-row style="margin-bottom: 10px">
      <a-col>
        <a-space>
          <a-button type="none" @click="back"> <left-outlined /> 返回 </a-button>
          <a-divider type="vertical" style="border-color: #dcdfe6" />
          <span class="title">品类草稿=>{{ categoryDetail.categoryName }}</span>
        </a-space>
      </a-col>
      <a-col flex="auto"></a-col>
      <a-col>
        <a-button type="primary">
          <template #icon>
            <CheckCircleTwoTone />
          </template>
          发布上线
        </a-button>
      </a-col>
    </a-row>
    <a-divider style="margin: 5px 0 10px 0" />
    <a-descriptions :column="1" :bordered="false" size="middle" style="padding-left: 30px">
      <a-descriptions-item label="品类名称">{{ categoryDetail.categoryNameFull }}</a-descriptions-item>
      <a-descriptions-item label="发布状态">{{ getCategoryStatus(categoryDetail.status) }}</a-descriptions-item>
      <a-descriptions-item label="品类说明">{{ categoryDetail.categoryDesc }}</a-descriptions-item>
    </a-descriptions>

    <!-- 提示行 -->
    <a-row style="padding-top: 20px">
      <a-col :span="24">
        <a-alert type="info" show-icon style="width: 100%" closable>
          <template #message>
            <a-space>
              <a-typography-text>您正在编辑的是草稿，需点击</a-typography-text>
              <a-typography-text type="primary" class="cursor" @click="publishOnline" style="color: #2c77f2">发布上线</a-typography-text>
              <a-typography-text>后，物模型才会正式生效。</a-typography-text>
            </a-space>
          </template>
        </a-alert>
      </a-col>
    </a-row>

    <!-- 按钮行 -->
    <a-row style="padding-top: 20px">
      <a-tooltip title="快速查找现有品类的物模型配置，并导入覆盖至当前品类的物模型草稿数据" placement="top" color="#2c77f2">
        <a-button>快速导入</a-button>
      </a-tooltip>

      <a-tooltip title="查看物模型的 TSL(Thing Specification Language) 描述语言" placement="top" color="#2c77f2">
        <a-button style="margin-left: 8px">物模型 TSL</a-button>
      </a-tooltip>

      <a-tooltip title="将JSON格式的物模型配置文件读取，并导入覆盖至当前品类的物模型草稿数据" placement="top" color="#2c77f2">
        <a-button style="margin-left: 8px">物模型 导入</a-button>
      </a-tooltip>

      <a-select style="width: 260px; margin-left: 8px" show-search placeholder="历史版本"> </a-select>
    </a-row>
    <a-row style="padding-top: 20px">
      <a-button type="primary" @click="openAdd">
        <template #icon>
          <plus-outlined />
        </template>
        添加功能
      </a-button>

      <iot-category-draft-add-form ref="addFormRef" />
    </a-row>

    <!-- 表格 -->
    <a-table :dataSource="tableData" :columns="columns" :pagination="false" bordered style="padding-top: 10px"></a-table>
  </a-card>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { useUserStore } from '/@/store/modules/system/user';
  import IotCategoryDraftAddForm from './iot-category-draft-add-form.vue';
  import { iotCategoryApi } from '/@/api/business/iot/category/iot-category-api.js';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { message } from 'ant-design-vue';
  import { PUBLISH_STATUS_ENUM } from '/@/constants/business/iot/category/iot-category-const';
  import { useCategoryStore } from '/@/store/modules/business/iot/category';

  //初始化品类id
  const initCategoryId = () => {
    const id = route.query.categoryId || categoryStore.getCategoryId();
    if (!id) {
      message.error('未获取到品类ID');
      router.push('/iot/category/list');
      return;
    }

    categoryStore.setCategoryId(id);
    getDetail();
  };

  //---------------------------------页面返回-------------------------------------
  const route = useRoute();
  const router = useRouter();
  const categoryStore = useCategoryStore();
  const back = () => {
    router.push({
      path: '/iot/category/detail', //返回品类详情页
    });

    //关闭当前标签页
    useUserStore().closeTagNav(route.name, false);
  };

  // 获取发布状态描述
  const getCategoryStatus = (status) => {
    return status === PUBLISH_STATUS_ENUM.published.value ? PUBLISH_STATUS_ENUM.published.desc : PUBLISH_STATUS_ENUM.unpublished.desc;
  };

  //--------------------------------添加功能表单-----------
  // 子组件引用
  const addFormRef = ref(null);
  //显示抽屉
  const showAdd = ref(false);

  //打开抽屉
  const openAdd = () => {
    addFormRef.value.open();
  };
  // 表单数据
  const formDefault = reactive({});

  const categoryDetail = ref({});

  //-----------------------------------表格-----------------------
  //表格列定义
  const columns = [
    {
      title: '#',
      width: 50,
      dataIndex: 'index',
    },
    {
      title: '功能类型',
      dataIndex: 'functionType',
    },
    {
      title: '功能名称',
      dataIndex: 'name',
    },
    {
      title: '标识符',
      dataIndex: 'identifier',
    },
    {
      title: '数据类型',
      dataIndex: 'fieldType',
    },
    {
      title: '数据定义',
      dataIndex: 'definition',
    },
    {
      title: '操作',
      dataIndex: 'action',
    },
  ];

  const tableData = ref([]);

  //请求品类信息详情

  const getDetail = async () => {
    try {
      SmartLoading.show();
      const param = { id: categoryStore.getCategoryId() };
      const response = await iotCategoryApi.getDetail(param);
      categoryDetail.value = response.data;
    } catch (error) {
      message.error('获取品类详情失败，请稍后重试');
    } finally {
      SmartLoading.hide();
    }
  };

  onMounted(() => {
    initCategoryId();
  });
</script>

<style scoped>
  .title {
    color: #606266;
    font-weight: bold;
    font-size: 18px;
  }
</style>
