<template>
  <!-- 名称编辑模态框 -->
  <a-modal
    v-model:open="modalVisible"
    title="编辑产品名称"
    @ok="handleSave"
    @cancel="handleCancel"
    :maskClosable="false"
    width="400px"
  >
    <a-form :model="form" :label-col="{ span: 5 }" :wrapper-col="{ span: 19 }">
      <a-form-item label="产品名称" name="productName">
        <a-input v-model:value="form.productName" placeholder="请输入产品名称" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { message } from 'ant-design-vue';
import { iotProductApi } from '/@/api/business/iot/product/iot-product-api.js';
import { SmartLoading } from '/@/components/framework/smart-loading';

const props = defineProps({
  productInfo: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update-product']);

const modalVisible = ref(false);

// 表单数据
const form = reactive({
  productName: ''
});

// 监听props变化，更新表单数据
watch(() => props.productInfo, (newVal) => {
  if (newVal) {
    form.productName = newVal.productName || '';
  }
}, { immediate: true, deep: true });

// 显示模态框方法
const showModal = () => {
  form.productName = props.productInfo.productName || '';
  modalVisible.value = true;
};

// 保存更新
const handleSave = async () => {
  try {
    SmartLoading.show();
    const params = {
      productId: props.productInfo.id,
      productName: form.productName,
    };
    await iotProductApi.updateName(params);
    message.success('更新成功');
    emit('update-product', { productName: form.productName });
    modalVisible.value = false;
  } catch (error) {
    message.error('更新失败');
  } finally {
    SmartLoading.hide();
  }
};

// 取消编辑
const handleCancel = () => {
  modalVisible.value = false;
};

// 暴露方法给父组件
defineExpose({
  showModal
});
</script>
