/**
 * 素材管理 api 封装
 *
 * @Author:    谢志豪
 * @Date:      2025-04-05 15:10:25
 * @Copyright  中山睿数信息技术有限公司 2025
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const mediaMaterialApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/mediaMaterial/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/mediaMaterial/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/mediaMaterial/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/mediaMaterial/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
      return postRequest('/mediaMaterial/batchDelete', idList);
  },
/**
 * 提交素材  <AUTHOR>
 */
  submittedMaterial: (id) => {
    return getRequest(`/mediaMaterial/submittedMaterial/${id}`);
  },
  /**
   * 审核通过素材  <AUTHOR>
   */
  approvedMaterial: (param) => {
    return postRequest('/mediaMaterial/approvedMaterial', param);
  },
/**
 * 审核不通过素材  <AUTHOR>
 */
  rejectedMaterial: (param) => {
    return postRequest('/mediaMaterial/rejectedMaterial', param);
  },
  /**
   * 反审核素材  <AUTHOR>
   */
  unapprovedMaterial: (id) => {
    return getRequest(`/mediaMaterial/unapprovedMaterial/${id}`);
  },
};
