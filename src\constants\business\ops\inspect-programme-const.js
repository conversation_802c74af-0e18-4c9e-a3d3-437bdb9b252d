export const TIME_UNIT_ENUM = {
    /**
     * 天
     */
    DAY: {
        value: "day",
        desc: "天",
    },

    /**
     * 周
     */
    WEEK: {
        value: "week",
        desc: "周",
    },

    /**
     * 月
     */
    MONTH: {
        value: "month",
        desc: "月",
    },

    /**
     * 根据 value 获取对应的枚举项
     */
    getOption(value) {
        if (value === 'day') {
            return TIME_UNIT_ENUM.DAY;
        } else if (value === 'week') {
            return TIME_UNIT_ENUM.WEEK;
        } else if (value === 'month') {
            return TIME_UNIT_ENUM.MONTH;
        } else {
            return "未知时间单位";
        }
    },

    getOptions() {
        return Object.values(this).reduce((options, item) => {
            if (item && typeof item === 'object' && 'value' in item && 'desc' in item) {
                options.push({ value: item.value, label: item.desc });
            }
            return options;
        }, []);
    },
};