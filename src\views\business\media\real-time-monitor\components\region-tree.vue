<template>
  <div style="flex: 1;height: 100%;background-color: #ffffff;border-radius: 5px;padding: 5px 10px">
    <a-typography-title :level="5">区域列表</a-typography-title>
    <a-spin :spinning="loading">
      <a-tree
        v-if="treeData.length > 0"
        :tree-data="treeData"
        default-expand-all
        @select="handleSelect"
        :selectedKeys="selectedKeys"
      />
      <a-empty v-else description="暂无区域数据" />
    </a-spin>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { iotRegionApi } from '/@/api/business/iot/region/iot-region-api';

const emit = defineEmits(['select']);

// 基础数据
const regions = ref([]);
const loading = ref(false);
const selectedKeys = ref(['0']);

// 构建树数据
const treeData = computed(() => [{
  title: '全部',
  key: '0',
  children: regions.value.map(region => ({
    title: region.name,
    key: region.id,
    isLeaf: true
  }))
}]);

// 加载区域数据
const loadRegions = async () => {
  loading.value = true;
  try {
    const res = await iotRegionApi.queryPage({ pageNum: 1, pageSize: 100 });
    regions.value = res.data?.list || [];
  } catch (error) {
    message.error('获取区域列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理选择事件
const handleSelect = (keys) => {
  if (!keys.length) return;
  selectedKeys.value = keys;
  const id = keys[0];
  
  if (id === '0') {
    emit('select', { id: '', name: '全部' });
  } else {
    const region = regions.value.find(r => r.id === id);
    if (region) {
      emit('select', region);
    }
  }
};

// 初始化
onMounted(loadRegions);


</script>