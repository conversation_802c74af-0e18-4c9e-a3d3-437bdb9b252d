<!--
  * 设备区域
  *
  * @Author:    骆伟林
  * @Date:      2025-03-22 17:51:27
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
  <a-modal
      :title="form.id ? '编辑区域' : '添加区域'"
      :width="2200"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 4 }">
      <a-row :gutter="8">
        <a-col :span="8">
          <a-form-item label="空间名称" name="name">
            <a-input style="width: 80%" v-model:value="form.name" placeholder="请输入空间名称" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="联系人" name="contact">
            <a-input style="width: 80%" v-model:value="form.contact" placeholder="请输入联系人" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="电话" name="phone">
            <a-input style="width: 80%" v-model:value="form.phone" placeholder="请输入电话" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="8">
        <a-col :span="8">
          <a-form-item label="面积" name="size">
            <a-input style="width: 80%" v-model:value="form.size" placeholder="自动计算面积" disabled/>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <MapComponent v-if="visibleFlag" @getArea="handleRegionData" :address="form.address" :longitude="form.longitude" :latitude="form.latitude" :latAndLon="form.latAndLon"/>
    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { reactive, ref, nextTick,onMounted } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { iotRegionApi } from '/@/api/business/iot/region/iot-region-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  import MapComponent from './ito-regin-map.vue';


function handleRegionData(data){
  form.size=data.area
  form.latAndLon=data.coordinates;
  form.latitude=data.latitude
  form.longitude=data.longitude
  form.address=data.address 
}


  //------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

function show(rowData) {
  Object.assign(form, formDefault);
  if (rowData && !_.isEmpty(rowData)) {
    Object.assign(form, rowData);
  }
  visibleFlag.value = true;
  nextTick(() => {
    formRef.value.clearValidate();
  });
}

function onClose() {
  Object.assign(form, formDefault);
  visibleFlag.value = false;
}

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
    id: undefined,
    name: undefined, //name
    address:undefined,
    latAndLon:undefined,//经纬度数组
    size:undefined,
    contact:undefined,
    phone:undefined,
    longitude:undefined,//经度
    latitude:undefined,//维度
  };

  let form = reactive({ ...formDefault });

  const rules = {
  name: [{ required: true, message: '名称必填' }],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码', trigger: 'blur' }
  ],
};

  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      //如果没有填写地址
      if(!form.address){
        message.error('请填写地址');
        return;
      }
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      if (form.id) {
        await iotRegionApi.update(form);
      } else {
        await iotRegionApi.add(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  defineExpose({
    show,
  });
</script>
