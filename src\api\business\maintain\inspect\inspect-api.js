import { postRequest, getRequest } from '/@/lib/axios';

/**
 * 巡检单相关API
 */
export const inspectApi = {
    /**
     * 新增巡检单
     * @param {Object} params - 巡检单信息
     */
    add(params) {
        return postRequest('/inspect/add', params);
    },

    /**
     * 更新巡检单
     * @param {Object} params - 巡检单信息
     */
    update(params) {
        return postRequest('/inspect/update', params);
    },

    /**
     * 分页查询巡检单
     * @param {Object} params - 查询参数
     */
    queryPage(params) {
        return postRequest('/inspect/queryPage', params);
    },

    /**
     * 批量删除巡检单
     * @param {Array|String} ids - 巡检单ID数组或单个ID
     * @returns {Promise} - 请求的Promise对象
     */
    batchDelete(ids) {
        return postRequest('/inspect/batchDelete', ids);
    },

    /**
     * 根据ID查询巡检单详情
     * @param {String|Number} id - 巡检单ID
     */
    getById(id) {
        return getRequest(`/inspect/query/${id}`);
    },

    /**
     * 删除巡检单
     * @param {String|Number} id - 巡检单ID
     */
    delete(id) {
        return getRequest(`/inspect/delete/${id}`);
    },

    /**
     * 查询巡检情况 
     * @param {Object} params - 查询参数
    */
    queryInspectCondition(params) {
        return postRequest('/inspect/queryInspectCondition/', params);
    },
};