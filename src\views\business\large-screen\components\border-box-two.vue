<template>
  <div ref="borderBoxContainer" class="border-box-container">
    <Decoration7 class="top-left-decoration" :color="['#18abd9', '#1ba6d0']">
      <text style="margin:1vw;">设备类型占比</text>
    </Decoration7>
    <div class="border-box-content">
      <BorderBox10 :color="['#3896b3', '#329bbb']">
        <div class="content">
          <div class="chart-container">
            <div class="legend">
              <div
                  v-for="(item, index) in chartData"
                  :key="index"
                  class="legend-item"
              >
                <div class="color-box" :style="{ backgroundColor: chartColors[index] }"></div>
                <div class="legend-text">{{ item.name }}</div>
              </div>
            </div>
            <div class="chart-wrapper">
              <ActiveRingChart :config="chartConfig" class="chart" />
            </div>
          </div>
        </div>
      </BorderBox10>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, onMounted, watch} from 'vue';
import { BorderBox10, Decoration7, ActiveRingChart } from '@kjgl77/datav-vue3';

const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
});

// ------------------------------------边框
const borderBoxContainer = ref(null);

onMounted(() => {
  if (!borderBoxContainer.value) {
    console.error('BorderBox container ref not exist');
    return;
  }
  console.log('BorderBox container mounted:', borderBoxContainer.value);
});

//---------------------------------------------------------------数据处理
// 初始化图表颜色和数据
const chartColors = ref([]);
const chartData = ref([]);
const chartConfig = ref({});

watch(() => props.list, () => {
  initializeData();
});

// 初始化数据
const initializeData = () => {
  const baseColors = ['#5145f8', '#f69d67', '#d83e74', '#3ecebe', '#b0f54a', '#47f74b', '#44dbf7', '#44aef1', '#f59944', '#ad47f2'];
  chartColors.value = props.list.map((_, index) => baseColors[index % baseColors.length]);

  chartData.value = props.list.map(item => ({
    name: item.type,
    value: item.number / props.list.length * 100
  }));

  chartConfig.value = {
    radius: '50%',
    activeRadius: '55%',
    lineWidth: 20,
    activeTimeGap: 3000,
    color: chartColors.value, // 使用动态颜色数组
    data: chartData.value, // 映射后的数据
    textColor: '#fff',
    digitalFlopStyle: {
      fontSize: 20,
      fill: '#fff'
    },
    numToFixed: 0,
    digitalFlopUnit: '%',
    animationCurve: 'easeOutCubic',
    animationFrame: 50,
    showOriginValue: false
  };
};

</script>

<style scoped>
.border-box-container {
  position: absolute;
  top: 35vh;
  left: 1vw;
  width: 25vw;
  height: 28vh;
  z-index: 1000;
}

.top-left-decoration {
  position: absolute;
  color: white;
  font-size: 1.1vw;
  height: 3vh;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.border-box-content {
  position: absolute;
  top: 4.5vh;
  left: 0;
  width: 100%;
  height: calc(100% - 4.5vh);
}

.content {
  width: 100%;
  height: 100%;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 1vw;
}

.chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.legend {
  width: 30%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.color-box {
  width: 12px;
  height: 12px;
  margin-right: 8px;
}

.legend-text {
  font-size: 0.8vw;
  color: white;
}

.chart-wrapper {
  width: 70%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.chart {
  width: 100%;
  height: 100%;
}
</style>