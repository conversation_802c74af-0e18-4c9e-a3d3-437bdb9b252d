<!--
  * 设备管理
  *
  * @Author:    qhs
  * @Date:      2025-02-11 15:38:08
  * @Copyright  2025 电子科技大学中山学院大数据与智能计算实验室
-->

<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="品类名称" class="smart-query-form-item">
        <a-input style="width: 200px" v-model:value="queryForm.categoryName_like" placeholder="请输入品类名称" />
      </a-form-item>
      <a-form-item label="品类行业" class="smart-query-form-item">
        <DictSelect keyCode="1" placeholder="请选择品类行业" v-model:value="queryForm.categoryIndustry_equal" width="200px" />
      </a-form-item>
      <a-form-item label="品类场景" class="smart-query-form-item">
        <DictSelect keyCode="2" placeholder="请选择品类场景" v-model:value="queryForm.categoryScene_equal" width="200px" />
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="onSearch">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>

  <a-card size="small" :bordered="false" :hoverable="true">
    <!-- 表格操作行 begin -->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="showForm" type="primary" size="small">
          <template #icon>
            <PlusOutlined />
          </template>
          新建
        </a-button>
        <a-button @click="confirmBatchDelete" type="primary" danger size="small" :disabled="selectedRowKeyList.length == 0">
          <template #icon>
            <DeleteOutlined />
          </template>
          批量删除
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
      </div>
    </a-row>

    <a-list :grid="{ gutter: 0, xs: 1, sm: 1, md: 1.5, lg: 2, xl: 3, xxl: 4 }" :data-source="data" :loading="initLoading">
      <template #renderItem="{ item, index }">
        <a-list-item>
          <a-card
            hoverable
            :loading="loading"
            size="default"
            :style="
              item.status == 1
                ? 'background: linear-gradient(rgba(88, 158, 255, 0.1), white)'
                : item.status == 0
                ? 'background: linear-gradient(rgba(128, 128, 128, 0.1), white)'
                : 'background: linear-gradient(rgba(128, 128, 128, 0.1), white)'
            "
          >
            <template #title>
              <a class="detail">{{ item.categoryName }}</a>
            </template>
            <template #extra>
              <div style="width: inherit; position: absolute; right: 30px; top: 15px">No{{ index + 1 }}</div>
              <a-checkbox v-model:checked="item.checked" style="position: absolute; right: 5px; top: 3px" @change="onSelectChange(item)"
            /></template>
            <div style="height: 100%; width: 100%; display: flex">
              <div style="flex: 2; display: flex; flex-direction: column; z-index: 2">
                <span class="span-multiline-ellipsis">品类行业：{{ findDescByValue(item.categoryIndustry, CATEGORY_INDUSTRY_ENUM) }}</span>
                <span class="span-multiline-ellipsis">品类场景：{{ findDescByValue(item.categoryScene, DICT_TYPE_ENUM) }}</span>
                <span class="span-multiline-ellipsis"
                  >发布状态： <a-switch :checked="item.status === 1" checked-children="是" un-checked-children="否" @change="switchChange(item)"
                /></span>
                <span class="span-multiline-ellipsis"
                  >排序号：
                  <a-input-number v-model:value="item.sort" min="1" style="margin-left: 8px; width: 80px" @change="switchChange(item)" />
                </span>
                <span class="span-multiline-ellipsis">最后更新时间：{{ item.updateTime }}</span>
              </div>
              <div style="flex: 1; display: flex; justify-content: center; align-items: center; z-index: 1">
                <img src="/@/assets/images/catagory/icon7.svg" alt="" style="max-width: 150px; max-height: 150px; object-fit: contain" />
              </div>
            </div>
            <template #actions>
              <span style="color: #108ee9" @click="showDetail(item)"><setting-outlined key="setting" /> 查看品类信息</span>
              <span style="color: #108ee9" @click="showForm(item)"><FormOutlined /> 编辑</span>
              <span style="color: #f56c6c" @click="onDelete(item)"><DeleteOutlined /> 删除</span>
            </template>
          </a-card>
        </a-list-item>
      </template>
    </a-list>

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.size"
        v-model:current="queryForm.current"
        v-model:pageSize="queryForm.size"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>

    <iotCategoryForm ref="formRef" @reloadList="queryData" />
    <!-- <EquipmentInstanceDetail ref="detailRef" /> -->
  </a-card>
</template>
<script setup>
  import { reactive, ref, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { message, Modal } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import DictSelect from '/@/components/support/dict-select/index.vue'; // 字典下拉框
  import TableOperator from '/@/components/support/table-operator/index.vue';
  import { iotCategoryApi } from '/@/api/business/iot/category/iot-category-api.js';
  import { CATEGORY_INDUSTRY_ENUM, DICT_TYPE_ENUM, PUBLISH_STATUS_ENUM } from '/@/constants/business/iot/category/iot-category-const.js';
  import iotCategoryForm from './components/iot-category-form.vue';
  import iotCategoryDetail from './components/iot-category-detail.vue';
  import { useCategoryStore } from '/@/store/modules/business/iot/category';
  // import EquipmentInstanceDetail from './components/equipment-instance-detail.vue';

  //import FilePreview from '/@/components/support/file-preview/index.vue'; // 图片预览组件

  // ---------------------------- 表格列 ----------------------------
  const columns = []; //? 待定
  const initLoading = ref(false);
  const loading = ref(false);
  const router = useRouter();
  const state = reactive({
    checked1: true,
  });

  // ---------------------------- 查询数据表单和方法 ----------------------------

  const queryFormState = {
    categoryName_like: undefined,
    categoryIndustry_equal: undefined,
    categoryScene_equal: undefined,
    current: 1,
    size: 9,
    sortItemList: [{ isAsc: false, column: 'create_time' }],
  };
  // 查询表单form
  const queryForm = reactive({ ...queryFormState });
  // 表格数据
  const data = ref([]);
  // 总数
  const total = ref(0);
  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.pageSize;
    Object.assign(queryForm, queryFormState);
    queryForm.pageSize = pageSize;
    queryData();
  }

  // 搜索
  function onSearch() {
    console.log(queryForm, '查询');
    queryForm.pageNum = 1;
    queryData();
  }

  // 查询数据
  async function queryData() {
    initLoading.value = true;
    loading.value = true;
    try {
      let queryResult = await iotCategoryApi.queryPage(queryForm);
      total.value = queryResult.data.total -1 ;
      data.value = queryResult.data.records.filter(item => item.categoryName !== "快速换模器" && item.categoryName !== "数控机床");
      selected = [];
      data.value.forEach((item) => {
        selected.push({
          id: item.id,
          checked: false,
        });
      });
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      initLoading.value = false;
      loading.value = false;
    }
  }
  onMounted(queryData);

  // ---------------------------- 添加/修改 ----------------------------
  const formRef = ref();
  const detailRef = ref();

  function showForm(data) {
    console.log('打开from表单');
    formRef.value.show(data);
  }
  // function showDetail(data) {
  //   detailRef.value.show(data);
  // }.

  function showDetail(data) {
    if (!data || !data.id) {
      message.error('品类ID不存在');
      return;
    }

    const categoryStore = useCategoryStore();
    categoryStore.setCategoryId(data.id);

    router.push({
      path: '/iot/category/detail',
      query: { categoryId: data.id },
    });
  }

  async function switchChange(data) {
    if (!data.id) {
      return;
    }
    try {
      data.status = data.status === 1 ? 0 : 1;
      await iotCategoryApi.update(data);
      message.success('操作成功');
      queryData();
    } catch (err) {
      smartSentry.captureError(err);
    }
  }

  // ---------------------------- 单个删除 ----------------------------
  //确认删除
  function onDelete(data) {
    Modal.confirm({
      title: '提示',
      content: '确定要删除选吗?',
      okText: '删除',
      okType: 'danger',
      onOk() {
        requestDelete(data);
      },
      cancelText: '取消',
      onCancel() {},
    });
  }

  //请求删除
  async function requestDelete(data) {
    console.log('调用删除接口啦');
    SmartLoading.show();
    try {
      let deleteForm = {
        goodsIdList: selectedRowKeyList.value,
      };
      const arr = [data.id];
      await iotCategoryApi.batchDelete(arr);
      message.success('删除成功');
      queryData();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }

  // ---------------------------- 批量删除 ----------------------------

  // 选择表格行
  const selectedRowKeyList = ref([]);
  let selected = [];

  function onSelectChange(item) {
    const index = selected.findIndex((select) => select.id === item.id);
    if (selected[index].checked) {
      selected[index].checked = false;
      selectedRowKeyList.value = selectedRowKeyList.value.filter((id) => id !== item.id);
    } else {
      selected[index].checked = true;
      selectedRowKeyList.value.push(item.id);
    }
  }

  // 批量删除
  function confirmBatchDelete() {
    Modal.confirm({
      title: '提示',
      content: '确定要批量删除这些数据吗?',
      okText: '删除',
      okType: 'danger',
      onOk() {
        requestBatchDelete();
      },
      cancelText: '取消',
      onCancel() {},
    });
  }

  //请求批量删除
  async function requestBatchDelete() {
    console.log('调用批量删除接口啦');
    try {
      SmartLoading.show();
      await iotCategoryApi.batchDelete(selectedRowKeyList.value);
      message.success('删除成功');
      queryData();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }

  //返回键值
  function findDescByValue(value, enumObject) {
    // 遍历枚举对象的键值对
    for (const key in enumObject) {
      if (enumObject[key].value === value) {
        return enumObject[key].desc; // 找到对应的描述后返回
      }
    }
    return null; // 如果找不到对应的值，则返回 null
  }
</script>

<style scoped lang="less">
  :deep(.ant-card-body) {
    padding: 10px 20px;
  }
  .scroll-container {
    height: 580px; /* 设置容器的高度 */
    overflow-y: auto; /* 启用 y 轴滚动 */
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  ::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 8px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
  .span-multiline-ellipsis {
    display: -webkit-box; /* Flexbox 模式 */
    -webkit-box-orient: vertical; /* 设置盒子为垂直方向 */
    overflow: hidden; /* 隐藏多余内容 */
    text-overflow: ellipsis; /* 增加省略号 */
    -webkit-line-clamp: 1; /* 限制显示2行，多出的内容隐藏 */
    max-width: 100%; /* 设置最大宽度 */
    line-height: 1.5; /* 设置行高（根据需要调整） */
    max-height: calc(1.5em * 2); /* 与行高和行数匹配 */
    word-break: break-word; /* 防止单词溢出容器 */
    font-size: 0.8em; /* 设置字体大小 */
    margin-bottom: 10px; /* 增加下边距 */
  }

  .detail {
    display: inline-block;
    padding: 5px 10px;
    background-color: rgba(88, 158, 255, 0.1); /* 设置背景颜色为淡蓝色 */
    border: 1px solid rgba(88, 158, 255, 0.1); /* 边框颜色 */
    border-radius: 8px; /* 圆角 */
    color: #2c77f1; /* 字体颜色 */
    font-size: 16px; /* 字体大小 */
    text-align: center; /* 文字居中 */
    font-weight: bold; /* 加粗字体 */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
  }
</style>
