/**
 * 连接协议 枚举
 *
 * @Author:    linwj
 * @Date:      2025-04-14
 * @Copyright  中山睿数信息技术有限公司 2025
 */

export const PROTOCOL_ENUM = {
  mqtt: {
    value: 'mqtt',
    desc: 'mqtt',
  },
  Modbus: {
    value: 'Modbus',
    desc: 'Modbus',
  },
  OpcUA: {
    value: 'OpcUA',
    desc: 'OpcUA',
  },
  BLE: {
    value: 'BLE',
    desc: 'BLE',
  },
  ZigBee: {
    value: 'ZigBee',
    desc: 'ZigBee',
  },
};
