<template>
  <a-modal :title="'投放计划审核'" :width="1000" :open="visibleFlag" @cancel="onClose" :maskClosable="false"
    :destroyOnClose="true">
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
      <a-row>
        <a-col :span="12">
          <a-form-item label="计划ID" name="id">
            <a-input v-model:value="form.id" disabled style="width: 300px" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="计划名称" name="mediaCampaignName">
            <a-input v-model:value="form.mediaCampaignName" disabled style="width: 300px" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="12">
          <a-form-item label="审核人" name="reviewerId">
            <employee-select v-model:value="form.reviewerId" style="width: 300px" @change="handleEmployeeChange" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="审核意见" name="reviewComment">
            <a-textarea v-model:value="form.reviewComment" placeholder="请输入审核意见" :rows="4" style="width: 300px" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>

    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="media" tab="素材列表">
        <a-card size="small" :bordered="false" :hoverable="false">
          <a-table size="small" :columns="materialColumns" :data-source="mediaMaterialList" rowKey="mediaId"
            :pagination="false" bordered>
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'mediaType'">
                {{ MEDIA_TYPE_ENUM.getDesc(record.mediaType) }}
              </template>
              <template v-if="column.dataIndex === 'status'">
                {{ MEDIA_STATUS_ENUM.getDesc(record.status) }}
              </template>
            </template>
          </a-table>
        </a-card>
      </a-tab-pane>
      <a-tab-pane key="device" tab="设备列表">
        <a-card size="small" :bordered="false" :hoverable="false">
          <a-table size="small" :columns="deviceColumns" :data-source="mediaDeviceList" rowKey="deviceId"
            :pagination="false" bordered>
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'deviceType'">
                {{ findDescByValue(record.deviceType, DEVICE_TYPE) }}
              </template>
            </template>
          </a-table>
        </a-card>
      </a-tab-pane>
      <a-tab-pane key="time" tab="投放时间列表">
        <a-card size="small" :bordered="false" :hoverable="false">
          <a-table size="small" :columns="timeColumns" :data-source="timeList" :pagination="false" bordered>
          </a-table>
        </a-card>
      </a-tab-pane>
    </a-tabs>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onRejected">审核不通过</a-button>
        <a-button type="primary" @click="onApproved">审核通过</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script setup>
import { reactive, ref, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { mediaCampaignApi } from '/@/api/business/media/campaign/media-campaign-api';
import { smartSentry } from '/@/lib/smart-sentry';
import { useUserStore } from '/@/store/modules/system/user';
import { MEDIA_TYPE_ENUM, MEDIA_STATUS_ENUM } from '/@/constants/business/media/material/media-material-const';
import { DEVICE_TYPE } from '/@/constants/business/iot/device/iot-device-const.js';
import EmployeeSelect from '/@/components/system/employee-select/index.vue';

const emits = defineEmits(['reviewSuccess']);
const userStore = useUserStore();
const visibleFlag = ref(false);
const formRef = ref();
const activeKey = ref('media');

const formDefault = {
  id: undefined,
  mediaCampaignName: undefined,
  reviewerId: userStore.employeeId,
  reviewerName: userStore.actualName,
  reviewComment: undefined
};

const form = reactive({ ...formDefault });

const rules = {
  reviewComment: [{ required: true, message: '审核意见 必填' }]
};

const materialColumns = [
  { title: '序号', dataIndex: 'playOrder', width: 80, align: 'center' },
  { title: '素材名称', dataIndex: 'mediaName', ellipsis: true },
  { title: '素材类型', dataIndex: 'mediaType', ellipsis: true },
  { title: '上传人', dataIndex: 'createUserId', ellipsis: true },
  { title: '播放时长(秒)', dataIndex: 'playDuration', width: 160 },
  { title: '播放次数', dataIndex: 'playTimes', width: 160 },
  { title: '创建时间', dataIndex: 'createTime', ellipsis: true, width: 160 }
];

const deviceColumns = [
  { title: '序号', dataIndex: 'index', width: 80, align: 'center', customRender: ({ index }) => index + 1 },
  { title: '设备名称', dataIndex: 'deviceName', ellipsis: true },
  { title: '备注名称', dataIndex: 'deviceNoteName', ellipsis: true },
  { title: '唯一编号', dataIndex: 'deviceUniqueNo', ellipsis: true },
  { title: '所属产品', dataIndex: 'deviceProductName', ellipsis: true }
];

const timeColumns = [
  { title: '序号', dataIndex: 'index', width: 80, align: 'center', customRender: ({ index }) => index + 1 },
  { title: '开始时间', dataIndex: 'startTime', width: 200 },
  { title: '结束时间', dataIndex: 'endTime', width: 200 }
];

// 重置表单
function resetForm() {
  Object.assign(form, formDefault);
  // 重置列表数据
  mediaDeviceList.value = [];
  mediaMaterialList.value = [];
  timeList.value = [];
}

const mediaDeviceList = ref([]);
const mediaMaterialList = ref([]);
const timeList = ref([]);

async function show(record) {
  resetForm();
  if (record.id) {
    form.id = record.id;
    form.mediaCampaignName = record.mediaCampaignName;
    form.reviewComment = record.reviewComment;
    
    // 加载关联数据
    SmartLoading.show();
    try {
      const res = await mediaCampaignApi.getById(record.id);
      if (res.data) {
        // 将获取的列表数据赋值给单独的响应式变量
        mediaDeviceList.value = res.data.mediaDeviceList || [];
        mediaMaterialList.value = res.data.mediaMaterialList || [];
        timeList.value = res.data.timeList || [];
      }
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }
  visibleFlag.value = true;
  nextTick(() => formRef.value?.clearValidate());
}

function onClose() {
  resetForm();
  visibleFlag.value = false;
}

// 审核通过
async function onApproved() {
  SmartLoading.show();
  try {
    await formRef.value.validateFields();
    await mediaCampaignApi.approvedCampaign(form);
    message.success('审核成功');
    emits('reviewSuccess');
    onClose();
  } catch (err) {
    smartSentry.captureError(err);
  } finally {
    SmartLoading.hide();
  }
}

// 审核不通过
async function onRejected() {
  SmartLoading.show();
  try {
    await formRef.value.validateFields();
    await mediaCampaignApi.rejectedCampaign(form);
    message.success('审核成功');
    emits('reviewSuccess');
    onClose();
  } catch (err) {
    smartSentry.captureError(err);
  } finally {
    SmartLoading.hide();
  }
}

// 返回枚举描述
function findDescByValue(value, enumObject) {
  for (const key in enumObject) {
    if (enumObject[key].value === value) {
      return enumObject[key].desc;
    }
  }
  return null;
}
function handleEmployeeChange(employee) {
  form.reviewerName = employee.actualName;
}

defineExpose({ show });
</script>