<template>
  <a-card>
    <template #title>
      设备信息 => {{ detail.productName }}
    </template>
    <template #extra>
      <a-button @click="back">返回</a-button>
    </template>
    <a-descriptions :column="2" :bordered="false">
      <a-descriptions-item label="ProductKey">
        {{ detail.productKey }}
        <a-button type="link" size="small" @click="copy(detail.productKey)">复制</a-button>
      </a-descriptions-item>
      <a-descriptions-item label="ProductSecret">
        {{ detail.productSecret }}
        <a-button type="link" size="small" @click="copy(detail.productSecret)">复制</a-button>
      </a-descriptions-item>
      <a-descriptions-item label="设备数量">
        [{{ deviceCount }}]
        <a-button type="link" size="small" @click="viewDevice">前往管理</a-button>
      </a-descriptions-item>
    </a-descriptions>

    <a-tabs v-model:activeKey="activeName" size="small" type="card" class="tabs-no-margin">
      <a-tab-pane key="info" tab="产品信息">
        <iotProductInfo :product-info="productInfo" @update-product="handleUpdateProduct" />
      </a-tab-pane>
      <a-tab-pane key="topic" tab="Topic 类列表">
        <topicList />
      </a-tab-pane>
      <a-tab-pane key="function" tab="功能定义">
        <iotProductFunction :product-info="productInfo" />
      </a-tab-pane>
      <a-tab-pane key="script" tab="消息解析">
        <product-script :product-info="productInfo" />
      </a-tab-pane>
      <a-tab-pane key="subset" tab="设备子集">
        <productSubList :product-info="productInfo" />
      </a-tab-pane>
    </a-tabs>
  </a-card>
</template>
  
<script setup>
import { useRoute, useRouter } from 'vue-router';
import { ref, onMounted, watch } from 'vue';
import { message } from 'ant-design-vue';
import iotProductInfo from './iot-product-info.vue';
import topicList from './topic-list.vue';
import iotProductFunction from './iot-product-function.vue';
import productSubList from './iot-product-sub-list.vue'
import { iotProductApi } from '/@/api/business/iot/product/iot-product-api.js';
import { iotDeviceApi } from '/@/api/business/iot/device/iot-device-api.js';

const deviceCount = ref(0);
const productInfo = ref({});
const detail = ref({});
const activeName = ref('info');

const router = useRouter();
const route = useRoute();

// 获取产品详情
const getProductDetail = async () => {
  try {
    const response = await iotProductApi.getDetail({
      id: route.query.id,
      productName: route.query.name
    });
    const deviceList = await iotDeviceApi.queryPage({
      pageNum: 1,
      pageSize: 10,
      productKey: response.data.productKey
})
    deviceCount.value = deviceList.data.total;
    const data = response.data;
    productInfo.value = data;
    detail.value = data;
  } catch (error) {
    message.error('获取产品详情失败');
  }
};

// 处理产品信息更新
const handleUpdateProduct = (updatedInfo) => {
  detail.value.productName = updatedInfo.productName;
  productInfo.value = { ...productInfo.value, ...updatedInfo };
};

const back = () => {
  router.push('/iot/product/list');
};

const viewDevice = () => {
  router.push({
    path: '/iot/device/list',
    query: {
      productKey: detail.value.productKey,
      productName:detail.value.productName
    },
  });
};

const copy = (content) => {
  const textarea = document.createElement('textarea');
  textarea.value = content;
  document.body.appendChild(textarea);
  textarea.select();
  document.execCommand('copy');
  document.body.removeChild(textarea);
  message.success('复制成功');
};

// 监听路由变化
watch(() => route.query, (newQuery) => {
  if (newQuery && route.path.includes('/iot/product/detail')) {
    getProductDetail();
  }
}, { immediate: true });
</script>

<style lang="less" scoped>
.tabs-no-margin {
  margin: 0 !important;
  
  :deep(.ant-tabs-nav) {
    margin: 0;
  }
}
</style>


