<template>
  <a-card size="small" :bordered="false" :hoverable="true">
    <a-space style="margin-bottom: 10px">
      <a-button type="text" @click="back"> <left-outlined /> 返回 </a-button>
      <a-divider type="vertical" style="border-color: #dcdfe6" />
      <span class="title">设备列表</span>
    </a-space>
    <a-divider style="margin: 5px 0 10px 0" />

    <a-list :grid="{ gutter: 10, xs: 1, sm: 1, md: 2, lg: 2, xl: 2, xxl: 3 }" :data-source="data" style="margin-top: 20px">
      <template #renderItem="{ item }">
        <a-list-item>
          <a-card hoverable size="default" style="background: linear-gradient(rgba(88, 158, 255, 0.1), white)" @click="showDetail(item)">
            <template #title>
              <a class="detail">{{ item.deviceName }}</a>
            </template>

            <div style="height: 100%; width: 100%; display: flex">
              <div style="flex: 1; display: flex; flex-direction: column; z-index: 2">
                <span class="span-multiline-ellipsis">备注名称：{{ item.deviceNoteName }}</span>
                <span class="span-multiline-ellipsis">所属产品：{{ item.productName }}</span>
                <span class="span-multiline-ellipsis">设备类型：{{ getDeviceTypeDesc(item.deviceType) }}</span>
                <span class="span-multiline-ellipsis">是否启用：{{ getDeviceEnumDesc(item.status) }} </span>
                <span class="span-multiline-ellipsis">设备状态：{{ getDeviceStatusEnumDesc(item.deviceStatus).desc }}</span>
              </div>
              <div style="flex: 1; display: flex; justify-content: center; align-items: center; z-index: 1">
                <img src="/@/assets/images/product/icon2.svg" alt="" style="max-width: 150px; max-height: 150px; object-fit: contain" />
              </div>
            </div>
          </a-card>
        </a-list-item>
      </template>
    </a-list>

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-card>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { operateEnvApi } from '/@/api/business/operate/env/env-device-by-region';
  import TableOperator from '/@/components/support/table-operator/index.vue';
  import { useUserStore } from '/@/store/modules/system/user';
  import { SearchOutlined, ReloadOutlined } from '@ant-design/icons-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import {
    DEVICE_TYPE,
    DEVICE_STATUS_ENUM,
    getDeviceTypeDesc,
    getDeviceStatusEnumDesc,
    getDeviceEnumDesc,
  } from '/@/constants/business/iot/device/iot-device-const.js';

  //-----------------------------详情页跳转------------------------------------
  const router = useRouter();
  const route = useRoute();
  function showDetail(data) {
    router.push({
      path: '/iot/operate/env/detail', // 跳转到数据展示页
      query: {
        id: data.id,
        regionName: regionName, //传递区域名称
        deviceId: data.id, //传递设备ID
        timeValue: timeValue, //传递时间值
      },
    });
  }
  //存储传来的区域名称
  const regionName = route.query.regionName;
  //存储传来的时间值
  const timeValue = route.query.timeValue;

  //页面返回
  const back = () => {
    router.push({
      path: '/Operation/env', //返回环境报告页
      query:{}
    });

    //关闭当前标签页
    useUserStore().closeTagNav(route.name, false);
  };

  const initLoading = ref(false);
  const loading = ref(false);
  const PAGE_SIZE_OPTIONS = ref(['10', '20', '30', '40', '50']);
  // ---------------------------- 查询数据表单和方法 ----------------------------

  const queryFormState = {
    deviceName: undefined, //设备Key
    deviceNoteName: undefined, //备注名称
    deviceId: undefined, //设备ID
    pageNum: 1,
    pageSize: 9,
    sortItemList: [{ isAsc: false, column: 'create_time' }],
  };
  // 查询表单form
  const queryForm = reactive({ ...queryFormState });
  // 卡片数据
  const data = ref([]);
  // 总数
  const total = ref(0);

  // 基于产品key和区域id查询设备数据
  async function queryData() {
    initLoading.value = true;
    loading.value = true;
    let param = {
      productKey: 'OIore67WTTna', //产品key
      regionId: route.query.regionId, //区域ID
      ...queryForm,
    };
    try {
      SmartLoading.show();
      let queryResult = await operateEnvApi.getDeviceByProductKey(param);
      total.value = queryResult.data.length;
      data.value = queryResult.data;
    } catch (e) {
      console.error(e);
    } finally {
      initLoading.value = false;
      loading.value = false;
      SmartLoading.hide();
    }
  }

  onMounted(() => {
    queryData();
  });
</script>

<style lang="less" scoped>
  .region-page-container {
    display: flex;
    width: 100%;
    gap: 16px;

    .year-month-tree {
      width: 200px;
      flex-shrink: 0;
    }
  }

  :deep(.ant-card-body) {
    padding: 10px 20px;
  }

  .span-multiline-ellipsis {
    display: -webkit-box; /* Flexbox 模式 */
    -webkit-box-orient: vertical; /* 设置盒子为垂直方向 */
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    line-height: 1.5;
    max-height: calc(1.5em * 2); /* 与行高和行数匹配 */
    word-break: break-word;
    font-size: 14px;
    margin-bottom: 10px;
  }

  .title {
    color: #606266;
    font-weight: bold;
    font-size: 18px;
  }
</style>
