<!--
  * 枚举型添加参数表单
  *
  * @Author:    文希希
  * @Date:      2025-03-26 17:17:51
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
  <div class="enum-form-container">
    <!-- 添加按钮 -->
    <a-button v-if="!disabled" type="link" size="small" @click="show">
      <template #icon><plus-outlined /></template>
      增加参数
    </a-button>

    <!-- 表格显示枚举数据，仅当有数据时显示 -->
    <a-table
      v-if="modelValue && modelValue.length > 0"
      :dataSource="modelValue"
      :columns="columns"
      :pagination="false"
      size="small"
      style="margin-top: 16px"
      rowKey="id"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.dataIndex === 'action' && !disabled">
          <div class="action-buttons">
            <a-typography-link type="primary" @click="edit(record, index)" style="padding-right: 15px">
              <EditOutlined class="action-icon" />
              编辑
            </a-typography-link>
            <a-popconfirm title="确认删除选中数据?" @confirm="removeEnum(index)">
              <a-typography-link>
                <DeleteOutlined class="action-icon" />
                删除
              </a-typography-link>
            </a-popconfirm>
          </div>
        </template>
      </template>
    </a-table>

    <!-- 新增/编辑枚举模态框 -->
    <a-modal
      v-model:open="visibleFlag"
      :title="isEdit ? '编辑' : '新增'"
      :maskClosable="false"
      :destroyOnClose="true"
      @ok="onSubmit"
      @cancel="onClose"
    >
      <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }">
        <a-form-item name="enumKey" label="参数值" required>
          <template #label>
            <div class="custom-label">
              <a-tooltip title="取值范围：-2147483648~2147483647" placement="topLeft" color="#2c77f2" :overlayStyle="{ maxWidth: '300px' }">
                <info-circle-outlined class="info-icon" />
              </a-tooltip>
              <span>参数值</span>
            </div>
          </template>
          <a-input-number
            v-model:value="form.enumKey"
            style="width: 100%"
            placeholder="枚举编号比如0"
            :min="-2147483648"
            :max="2147483647"
          ></a-input-number>
        </a-form-item>

        <a-form-item name="enumDesc" label="参数描述" required>
          <template #label>
            <div class="custom-label">
              <a-tooltip
                title="支持中文、英文大小写、日文、数字、下划线和短划线，必须以中文、英文或数字开头，不超过20个字符"
                placement="topLeft"
                color="#2c77f2"
                :overlayStyle="{ maxWidth: '800px' }"
              >
                <info-circle-outlined class="info-icon" />
              </a-tooltip>
              <span>参数描述</span>
            </div>
          </template>
          <a-input v-model:value="form.enumDesc" style="width: 100%" placeholder="对该枚举的描述" :maxlength="20"></a-input>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
  import { ref, reactive, defineProps, defineEmits, nextTick } from 'vue';
  import { PlusOutlined, EditOutlined, DeleteOutlined, InfoCircleOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import _ from 'lodash';

  const props = defineProps({
    modelValue: {
      type: Array,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['update:modelValue']);

  // 表格列定义
  const columns = [
    {
      title: '参数值',
      dataIndex: 'enumKey',
      key: 'enumKey',
      width: '30%',
      ellipsis: false,
      customCell: () => {
        return {
          style: 'white-space: normal; word-break: break-word;',
        };
      },
    },
    {
      title: '参数描述',
      dataIndex: 'enumDesc',
      key: 'enumDesc',
      width: '40%',
      ellipsis: false,
      customCell: () => {
        return {
          style: 'white-space: normal; word-break: break-word;',
        };
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: '30%',
    },
  ];

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示模态框
  const visibleFlag = ref(false);
  // 是否为编辑状态
  const isEdit = ref(false);
  // 当前编辑的索引
  const editIndex = ref(-1);

  // 表单引用
  const formRef = ref(null);

  // 表单默认值
  const formDefault = {
    enumKey: null,
    enumDesc: '',
    id: undefined,
  };

  // 当前表单数据
  const form = reactive({ ...formDefault });

  // 表单规则
  const rules = {
    enumKey: [{ required: true, message: '整型参数，取值范围：-2147483648 ~ 2147483647', trigger: 'blur' }],
    enumDesc: [{ required: true, message: '请输入参数描述', trigger: 'blur' }],
  };

  // 显示添加模态框
  function show() {
    isEdit.value = false;
    editIndex.value = -1;
    // 重置表单为默认值
    Object.assign(form, formDefault);
    visibleFlag.value = true;
    nextTick(() => {
      if (formRef.value) {
        formRef.value.clearValidate();
      }
    });
  }

  // 显示编辑模态框
  function edit(record, index) {
    isEdit.value = true;
    editIndex.value = index;
    // 填充表单数据
    Object.assign(form, {
      enumKey: record.enumKey,
      enumDesc: record.enumDesc,
      id: record.id || Date.now(),
    });
    visibleFlag.value = true;
    nextTick(() => {
      if (formRef.value) {
        formRef.value.clearValidate();
      }
    });
  }

  // 关闭模态框
  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  // 提交表单
  async function onSubmit() {
    try {
      // 验证表单
      await formRef.value.validateFields();

      // 准备新值
      const newValue = {
        enumKey: String(form.enumKey),
        enumDesc: form.enumDesc,
        id: form.id || Date.now(),
      };

      // 克隆当前值数组
      const updatedEnums = [...props.modelValue];

      // 检查是否有重复值（编辑时排除当前项）
      const hasDuplicate = updatedEnums.some((item, idx) => {
        return item.enumKey === newValue.enumKey && (!isEdit.value || idx !== editIndex.value);
      });

      if (hasDuplicate) {
        message.error('参数值不能重复');
        return;
      }

      if (isEdit.value) {
        // 编辑模式 - 更新现有项
        updatedEnums[editIndex.value] = newValue;
      } else {
        // 添加模式 - 添加新项
        updatedEnums.push(newValue);
      }

      // 更新数据
      emit('update:modelValue', updatedEnums);

      // 关闭模态框
      visibleFlag.value = false;
      message.success(isEdit.value ? '修改成功' : '添加成功');
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 移除枚举值
  function removeEnum(index) {
    const updatedEnums = [...props.modelValue];
    updatedEnums.splice(index, 1);
    emit('update:modelValue', updatedEnums);
    message.success('删除成功');
  }

  // 暴露方法给父组件
  defineExpose({
    show,
    edit,
  });
</script>

<style scoped>
  .enum-form-container {
    width: 100%;
  }

  .custom-label {
    display: flex;
    align-items: center;
  }

  .info-icon {
    margin-right: 4px;
    color: #1890ff;
    font-size: 14px;
  }

  .action-buttons {
    display: flex;
    align-items: center;
  }

  .action-icon {
    margin-right: 4px;
  }

  :deep(.ant-typography-danger) {
    color: #ff4d4f;
  }

  :deep(.ant-typography-danger:hover) {
    color: #ff7875;
  }

  :deep(.ant-table-cell) {
    white-space: normal;
    word-wrap: break-word;
    word-break: break-word;
  }

  :deep(.ant-table-tbody > tr > td) {
    height: auto;
    padding-top: 8px;
    padding-bottom: 8px;
  }
</style>
