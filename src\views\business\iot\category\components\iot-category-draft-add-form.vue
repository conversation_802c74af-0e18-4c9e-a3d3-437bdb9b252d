<!--
  * 品类草稿->添加功能
  *
  * @Author:    文希希
  * @Date:      2025-03-26 17:17:51
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
  <a-drawer
    title="新增功能"
    placement="right"
    :width="700"
    :maskClosable="false"
    :footer-style="{ textAlign: 'right' }"
    :open="visibleFlag"
    @close="onClose"
  >
    <a-form layout="horizontal" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" :model="form" ref="formRef">
      <!-- 功能类型 -->
      <a-form-item required>
        <template #label>
          <div class="custom-label">
            <a-tooltip
              title="属性一般是设备的运行状态，如当前温度等；服务是设备可被调用的方法，支持定义参数，如执行某项任务；事件则是设备上报的通知，如告警，需要被及时处理。"
              placement="left"
              color="#2c77f2"
            >
              <info-circle-outlined class="info-icon" />
            </a-tooltip>
            <span>功能类型</span>
          </div>
        </template>
        <a-radio-group v-model:value="form.functionType" @change="handleFunctionTypeChange">
          <a-radio :value="1">属性</a-radio>
          <a-radio :value="2">命令</a-radio>
          <a-radio :value="3">事件</a-radio>
        </a-radio-group>
      </a-form-item>

      <!-- 功能名称 -->
      <a-form-item required>
        <template #label>
          <div class="custom-label">
            <a-tooltip
              title="支持中文、大小写字母、日文、数字、短划线、下划线、斜杠和小数点，必须以中文、英文或数字开头，不超过 30 个字符"
              placement="left"
              color="#2c77f2"
            >
              <info-circle-outlined class="info-icon" />
            </a-tooltip>
            <span>功能名称</span>
          </div>
        </template>

        <a-input v-model:value="form.name" placeholder="请输入功能名称" :allowClear="true" />
      </a-form-item>

      <!-- 标识符 -->
      <a-form-item required>
        <template #label>
          <div class="custom-label">
            <a-tooltip
              title="支持英文大小写、数字、下划线和短划线，必须以英文或数字开头，不能为纯数字，不超过 30 个字符"
              placement="left"
              color="#2c77f2"
            >
              <info-circle-outlined class="info-icon" />
            </a-tooltip>
            <span>标识符</span>
          </div>
        </template>

        <a-input v-model:value="form.identifier" placeholder="请输入标识符" :allowClear="true" />
      </a-form-item>

      <!-- 数据类型，仅当功能类型为"属性"时显示 -->
      <a-form-item v-if="form.functionType === 1" label="数据类型" required>
        <a-select v-model:value="form.fieldType" placeholder="请选择数据类型" :allowClear="true" @change="handleFieldTypeChange">
          <a-select-option value="int32">int32 : 整数型</a-select-option>
          <a-select-option value="float">float : 单精度浮点型</a-select-option>
          <a-select-option value="double">double : 双精度浮点型</a-select-option>
          <a-select-option value="text">text : 字符串</a-select-option>
          <a-select-option value="enum">enum : 枚举型</a-select-option>
          <a-select-option value="bool">bool : 布尔型</a-select-option>
          <a-select-option value="date">date : 时间型</a-select-option>
          <a-select-option value="array">array : 数组</a-select-option>
          <a-select-option value="struct">struct : 结构体</a-select-option>
        </a-select>
      </a-form-item>

      <!-- 最小值 - 仅当为数值类型时显示 -->
      <a-form-item v-if="showNumberFields" label="最小值" required>
        <a-input-number
          v-model:value="form.specMin"
          style="width: 100%"
          placeholder="请输入最小值"
          :min="-2147483648"
          :max="2147483647"
          @change="validateNumberFields"
        />
      </a-form-item>

      <!-- 最大值 - 仅当为数值类型时显示 -->
      <a-form-item v-if="showNumberFields" label="最大值" required>
        <a-input-number
          v-model:value="form.specMax"
          style="width: 100%"
          placeholder="请输入最大值"
          :min="-2147483648"
          :max="2147483647"
          @change="validateNumberFields"
        />
      </a-form-item>

      <!-- 步长 - 仅当为数值类型时显示 -->
      <a-form-item v-if="showNumberFields" label="步长" required>
        <a-input-number
          v-model:value="form.specStep"
          style="width: 100%"
          placeholder="请输入步长"
          :min="1"
          :max="2147483647"
          @change="validateNumberFields"
        />
      </a-form-item>

      <!-- 单位 - 仅当为数值类型时显示 -->
      <a-form-item v-if="showNumberFields" label="单位">
        <a-select v-model:value="form.specUnit" placeholder="请选择单位" show-search :allowClear="true">
          <!-- 这里需要从后端获取单位数据，暂时留空 -->
        </a-select>
      </a-form-item>

      <!-- 字符串长度 - 仅当为字符串类型时显示 -->
      <a-form-item v-if="form.functionType === 1 && form.fieldType === 'text'" label="数据长度" required>
        <a-input-number v-model:value="form.specLength" style="width: 100%" :min="1" :max="10240" placeholder="请输入数据长度，最大值为10240" />
      </a-form-item>

      <!-- 布尔值定义 - 仅当为布尔类型时显示 -->
      <template v-if="form.functionType === 1 && form.fieldType === 'bool'">
        <a-form-item label="布尔值 0">
          <a-input v-model:value="form.specBoolFalse" placeholder="如：关" />
        </a-form-item>
        <a-form-item label="布尔值 1">
          <a-input v-model:value="form.specBoolTrue" placeholder="如：开" />
        </a-form-item>
      </template>

      <!-- 时间值定义 - 仅当为时间类型时显示 -->
      <a-form-item v-if="form.functionType === 1 && form.fieldType === 'date'" label="时间格式">
        <a-input v-model:value="form.specDateFormat" disabled />
      </a-form-item>
      <!-- 枚举数据 - 仅当为枚举类型时显示 -->
      <a-form-item v-if="form.functionType === 1 && form.fieldType === 'enum'" label="枚举数据">
        <enum-form v-model="enumsData" :disable="disable" ref="enumFormRef" />
      </a-form-item>

      <!-- 元素类型 - 仅当为数组类型时显示 -->
      <template v-if="form.functionType === 1 && form.fieldType === 'array'">
        <a-form-item label="元素类型" required>
          <a-radio-group v-model:value="form.specItemType" @change="handleSpecItemTypeChange">
            <a-radio value="int32">int32</a-radio>
            <a-radio value="float">float</a-radio>
            <a-radio value="double">double</a-radio>
            <a-radio value="text">text</a-radio>
            <a-radio value="struct">struct</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="元素个数" required>
          <a-input-number v-model:value="form.specSize" style="width: 100%" :min="1" :max="512" placeholder="请输入元素个数，最大值为512" />
        </a-form-item>
      </template>

      <!-- 结构体 - 仅当为结构体类型或数组元素为结构体时显示 -->
      <a-form-item
        v-if="form.functionType === 1 && (form.fieldType === 'struct' || (form.fieldType === 'array' && form.specItemType === 'struct'))"
        label="结构体"
      >
        <struct-form v-model:value="structData" :disable="disable" />
      </a-form-item>

      <!-- 访问权限 - 仅当功能类型为"属性"时显示 -->
      <a-form-item v-if="form.functionType === 1" label="访问权限" required>
        <a-radio-group v-model:value="form.accessMode">
          <a-radio value="r">只读</a-radio>
          <a-radio value="rw">读写</a-radio>
        </a-radio-group>
      </a-form-item>

      <!-- 输入参数 - 仅当功能类型为"命令"时显示 -->
      <a-form-item v-if="form.functionType === 2" label="输入参数">
        <param-struct-editor v-model:value="inputData" />
      </a-form-item>

      <!-- 输出参数 - 仅当功能类型为"命令"或"事件"时显示 -->
      <a-form-item v-if="form.functionType === 2 || form.functionType === 3" label="输出参数">
        <param-struct-editor v-model:value="outputData" />
      </a-form-item>

      <!-- 调用方式 - 仅当功能类型为"命令"时显示 -->
      <a-form-item v-if="form.functionType === 2" required>
        <template #label>
          <div class="custom-label">
            <a-tooltip
              title="异步调用是指云端执行调用后直接返回，不会关心设备的回复消息，如果服务为同步调用，云端会等待设备回复，否则会调用超时。"
              placement="left"
              color="#2c77f2"
            >
              <info-circle-outlined class="info-icon" />
            </a-tooltip>
            <span>调用方式</span>
          </div>
        </template>
        <a-radio-group v-model:value="form.callType">
          <a-radio value="async">异步</a-radio>
          <a-radio value="sync">同步</a-radio>
        </a-radio-group>
      </a-form-item>

      <!-- 事件类型 - 仅当功能类型为"事件"时显示 -->
      <a-form-item v-if="form.functionType === 3" required>
        <template #label>
          <div class="custom-label">
            <a-tooltip
              title="'信息'是设备上报的一般性通知，如完成某项任务等。'告警'和'故障'是设备运行过程中主动上报的突发或异常情况，优先级高。不同的事件类型将用于统计分析。"
              placement="left"
              color="#2c77f2"
            >
              <info-circle-outlined class="info-icon" />
            </a-tooltip>
            <span>事件类型</span>
          </div>
        </template>
        <a-radio-group v-model:value="form.eventType">
          <a-radio value="info">信息</a-radio>
          <a-radio value="alert">告警</a-radio>
          <a-radio value="error">故障</a-radio>
        </a-radio-group>
      </a-form-item>

      <!-- 是否必选 -->
      <a-form-item required>
        <template #label>
          <div class="custom-label">
            <a-tooltip title="此参数在设备调用服务时是否必须传入，如果是，则设备调用服务时必须传入此参数。" placement="left" color="#2c77f2">
              <info-circle-outlined class="info-icon" />
            </a-tooltip>
            <span>是否必选</span>
          </div>
        </template>

        <a-radio-group v-model:value="form.required">
          <a-radio :value="1">必选</a-radio>
          <a-radio :value="0">可选</a-radio>
        </a-radio-group>
      </a-form-item>

      <!-- 描述 -->
      <a-form-item label="描述">
        <a-textarea v-model:value="form.functionDesc" placeholder="请输入描述" :rows="6" />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary">保存</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script setup>
  import { ref, reactive, computed } from 'vue';
  import { InfoCircleOutlined } from '@ant-design/icons-vue';
  import func from '/@/utils/func';
  import EnumForm from './add-function/enum-form.vue';
  import StructForm from './add-function/struct-form.vue';
  import { option } from '/@/option/iot/function';
  import { validate } from 'uuid';

  // 抽屉显示状态
  const visibleFlag = ref(false);
  const formRef = ref(null);

  // 表单数据
  const formDefault = reactive({
    functionType: 1, //功能类型 默认为属性
    name: undefined, //功能名称
    identifier: undefined, //标识符
    fieldType: 'int32', //数据类型 默认为整数型
    specMin: undefined, //最小值
    specMax: undefined, //最大值
    specStep: undefined, //步长
    specUnit: undefined, //单位
    specLength: 64, //字符串 数据长度
    specBoolFalse: undefined, //布尔值定义 0
    specBoolTrue: undefined, //布尔值定义 1
    date: undefined, //时间型
    specDateFormat: 'timestamp', //时间格式定义
    specItemType: 'int32', //元素类型 默认int32
    specSize: 10, //元素个数
    accessMode: 'r', //访问权限 默认只读
    callType: 'async', //调用方式 默认异步
    eventType: 'info', //事件类型 默认信息
    required: 1, //是否必选
    functionDesc: undefined, //描述
    categoryId: undefined, // 将在open方法中设置
  });

  const form = reactive({ ...formDefault });

  // 特殊数据结构
  const enumsData = ref([]); // 枚举数据
  const structData = ref([]); // 结构体数据
  const inputData = ref([]); // 输入参数
  const outputData = ref([]); // 输出参数

  // 重置表单数据
  const resetForm = () => {
    Object.assign(form, { ...formDefault });

    // 保留categoryId
    if (form.categoryId) {
      const categoryId = form.categoryId;
      form.categoryId = categoryId;
    }

    // 重置复杂数据结构
    enumsData.value = [];
    structData.value = [];
    inputData.value = [];
    outputData.value = [];
  };

  // 打开抽屉时的处理函数
  const open = (categoryId) => {
    // 重置表单数据
    resetForm();

    // 设置品类ID
    form.categoryId = categoryId;

    // 显示抽屉
    visibleFlag.value = true;
  };

  // 计算属性：是否显示数值类型的字段
  const showNumberFields = computed(() => {
    return form.functionType === 1 && ['int32', 'float', 'double'].includes(form.fieldType);
  });

  // 处理功能类型变化
  const handleFunctionTypeChange = (e) => {
    const value = e.target.value;

    // 重置不相关的字段
    if (value === 1) {
      // 属性类型默认值
      form.fieldType = 'int32';
      form.accessMode = 'r';
      form.required = 1;
    } else if (value === 2) {
      // 命令类型默认值
      form.callType = 'async';
      form.required = 1;
      inputData.value = [];
      outputData.value = [];
    } else if (value === 3) {
      // 事件类型默认值
      form.eventType = 'info';
      outputData.value = [];
    }
  };

  // 处理数据类型变化
  const handleFieldTypeChange = (value) => {
    // 根据数据类型重置相关字段
    if (['int32', 'float', 'double'].includes(value)) {
      // 数值类型
      form.specMin = undefined;
      form.specMax = undefined;
      form.specStep = undefined;
    } else if (value === 'text') {
      // 字符串类型
      form.specLength = 64;
    } else if (value === 'bool') {
      // 布尔类型
      form.specBoolFalse = undefined;
      form.specBoolTrue = undefined;
    } else if (value === 'enum') {
      // 枚举类型
      enumsData.value = [];
    } else if (value === 'array') {
      // 数组类型
      form.specItemType = 'int32';
      form.specSize = 10;
    } else if (value === 'struct') {
      // 结构体类型
      structData.value = [];
    }
  };

  // 处理元素类型变化
  const handleSpecItemTypeChange = (e) => {
    const value = e.target.value;
    // 如果元素类型为结构体，初始化结构体数据
    if (value === 'struct') {
      structData.value = [];
    }
  };

  // 关闭抽屉
  const onClose = () => {
    visibleFlag.value = false;
  };

  // 对外暴露方法
  defineExpose({
    open,
    close: onClose,
  });
</script>

<style scoped>
  .custom-label {
    display: flex;
    align-items: flex-start;
  }

  .info-icon {
    margin-right: 4px;
    margin-top: 4px;
    color: #1890ff;
    font-size: 14px;
  }

  /* 确保红色星号显示在正确位置 */
  :deep(.ant-form-item-required::before) {
    position: absolute;
    left: -12px;
    top: 2px;
  }
</style>
