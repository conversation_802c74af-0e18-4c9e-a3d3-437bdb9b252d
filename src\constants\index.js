/*
 * 所有常量入口
 *
 * @Author:    1024创新实验室-主任：卓大
 * @Date:      2022-09-06 19:58:28
 * @Wechat:    zhuda1024
 * @Email:     <EMAIL>
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */
import menu from './system/menu-const';
import goods from './business/erp/goods-const';
import category from './business/erp/category-const';
import { LOGIN_DEVICE_ENUM } from './system/login-device-const';
import { FLAG_NUMBER_ENUM, GENDER_ENUM, USER_TYPE_ENUM } from './common-const';
import { LAYOUT_ENUM } from './layout-const';
import file from './support/file-const';
import notice from './business/oa/notice-const';
import loginLog from './support/login-log-const';
import enterprise from './business/oa/enterprise-const';
import message from './business/message/message-const';
import codeGeneratorConst from './support/code-generator-const';
import changeLogConst from './support/change-log-const';
import jobConst from './support/job-const';
import { WORK_ORDER_STATUS_ENUM } from '/@/constants/work-order/maintain-order-const';
import { STATUS_EQUAL_STATUS_ENUM } from '/@/views/business/iot/product/constant/status_equal_const.js';
import { PROTOCOL_ENUM } from '/@/views/business/iot/product/constant/protocol_const.js';
import { PRODUCT_DEVICE_TYPE_ENUM } from '/src/views/business/iot/product/constant/product_device_type_const.js';
import { STATUS_ENUM } from '/@/views/business/iot/device/constant/status_const.js';
import { DEVICE_STATUS_ENUM } from '/@/views/business/iot/device/constant/device_status_const.js';
import { DEVICE_TYPE_ENUM } from '/@/views/business/iot/device/constant/device_type_const.js';
import { DEVICE_ERROR_STATUS_ENUM } from '/@/views/business/maintain/maintain-fault/device_error_status.js';
import { ERROR_STATUS } from '/@/views/business/home/<USER>/device_error_status.js';

export default {
  FLAG_NUMBER_ENUM,
  LOGIN_DEVICE_ENUM,
  GENDER_ENUM,
  USER_TYPE_ENUM,
  LAYOUT_ENUM,
  WORK_ORDER_STATUS_ENUM,
  STATUS_EQUAL_STATUS_ENUM,
  PRODUCT_DEVICE_TYPE_ENUM,
  PROTOCOL_ENUM,
  STATUS_ENUM,
  DEVICE_STATUS_ENUM,
  DEVICE_TYPE_ENUM,
  DEVICE_ERROR_STATUS_ENUM,
  ERROR_STATUS,
  ...loginLog,
  ...menu,
  ...goods,
  ...category,
  ...file,
  ...notice,
  ...enterprise,
  ...message,
  ...codeGeneratorConst,
  ...changeLogConst,
  ...jobConst,
};
