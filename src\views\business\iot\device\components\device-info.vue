<template>
    <a-card>
      <a-descriptions title="产品详情" :column="3" :bordered="false">
        <a-descriptions-item label="产品名称">
          {{ productDetail.productName }}
          <a-button type="link" @click="viewProduct" size="small">查看</a-button>
        </a-descriptions-item>
        <a-descriptions-item label="设备类型">
          {{ productDetail.deviceTypeName }}
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">
          {{ productDetail.createTime }}
        </a-descriptions-item>
        <a-descriptions-item label="所属品类">
          {{ productDetail.categoryNameFull }}
        </a-descriptions-item>
        <a-descriptions-item label="数据状态">
          {{ productDetail.statusName }}
        </a-descriptions-item>
        <a-descriptions-item label="动态注册">
          {{ productDetail.dynamicRegisterName }}
        </a-descriptions-item>
        <a-descriptions-item label="链接协议">
          {{ productDetail.linkProtocolName }}
        </a-descriptions-item>
        <a-descriptions-item label="联网方式">
          {{ productDetail.connectModeName }}
        </a-descriptions-item>
        <a-descriptions-item label="数据格式">
          {{ productDetail.dataTypeName }}
        </a-descriptions-item>
        <a-descriptions-item label="生产厂商">
          {{ productDetail.vendors }}
        </a-descriptions-item>
        <a-descriptions-item label="产品型号">
          {{ productDetail.model }}
        </a-descriptions-item>
        <a-descriptions-item label="产品描述">
          {{ productDetail.productDesc }}
        </a-descriptions-item>
      </a-descriptions>
      <a-divider/>
      <a-descriptions title="设备详情" :column="3" size="default" :bordered="false">
        <a-descriptions-item label="设备名称">
          {{ deviceDetail.deviceName }}
          <a-button type="link" @click="copyDeviceName" size="small">复制</a-button>
        </a-descriptions-item>
        <a-descriptions-item label="备注名称">
          {{ deviceDetail.deviceNoteName }}
          <a-button type="link" @click="showUpdateNameModal" size="small">编辑</a-button>
        </a-descriptions-item>
        <a-descriptions-item label="认证方式">
          -
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">
          {{ deviceDetail.createTime }}
        </a-descriptions-item>
        <a-descriptions-item label="激活时间">
          {{ deviceDetail.activeTime }}
        </a-descriptions-item>
        <a-descriptions-item label="最后上线时间">
          {{ deviceDetail.lastOnlineTime }}
        </a-descriptions-item>
        <a-descriptions-item label="设备状态">
          {{ deviceDetail.deviceStatusName }}
        </a-descriptions-item>
        <a-descriptions-item label="启用状态">
          {{ deviceDetail.status === 1 ? '启用' : '禁用' }}
        </a-descriptions-item>
        <a-descriptions-item label="实时延时">
          <a-button type="link" size="small">测试</a-button>
        </a-descriptions-item>
      </a-descriptions>
      <a-divider/>
      <a-descriptions title="设备扩展信息" :column="3" size="default" :bordered="false">
        <a-descriptions-item label="SDK 语言">
          -
        </a-descriptions-item>
        <a-descriptions-item label="版本号">
          -
        </a-descriptions-item>
        <a-descriptions-item label="模组商">
          -
        </a-descriptions-item>
        <a-descriptions-item label="模组信息">
          -
        </a-descriptions-item>
      </a-descriptions>
      <a-divider/>

      <DeviceUpdateName 
        ref="updateNameRef"
        :deviceInfo="deviceDetail" 
        @update-device="handleUpdateDevice" 
      />
    </a-card>
  </template>
  
  <script setup>
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { ref, onMounted, watch } from 'vue';
  import { iotDeviceApi } from '/@/api/business/iot/device/iot-device-api.js';
  import { iotProductApi } from '/@/api/business/iot/product/iot-product-api.js';
  import DeviceUpdateName from './device-updateName.vue';
  
  // 定义 props
  const props = defineProps({
    productKey: String,
    deviceId: String,
    deviceName: String,
  });
  //----------------------------------详细信息----------------------------------------   
  //根据api获取产品详情
  const productDetail = ref({});
  const getProductDetail = async () => {
    try {
      SmartLoading.show();
      const param = {
        productKey: props.productKey,
      };
      const response = await iotProductApi.getDetail(param);
      productDetail.value = response.data;
    } catch (error) {
      message.error('获取产品详情失败，请稍后重试');
    } finally {
      SmartLoading.hide();
    }
  };

  //根据api获取设备信息   
  const deviceDetail = ref({});
  const getDeviceDetail = async () => {
    try {
      SmartLoading.show();
      const response = await iotDeviceApi.queryDetailByName(props.deviceName);
      deviceDetail.value = response.data;
    } catch (error) {
      message.error('获取详情失败，请稍后重试');
    } finally {
      SmartLoading.hide();
    }
  };
  
  const viewProduct = () => {
    // 查看产品的逻辑
  };
  
  const copyDeviceName = () => {
    navigator.clipboard.writeText(deviceDetail.value.deviceName).then(() => {
      message.success('复制成功');
    }).catch(err => {
      message.error('复制失败');
    });
  };
  
  // 显示备注名称编辑模态框
  const updateNameRef = ref(null);
  const showUpdateNameModal = () => {
    updateNameRef.value.showModal();
  };
  
  // 处理设备信息更新
  const handleUpdateDevice = (updatedInfo) => {
    if (updatedInfo.deviceNoteName) {
      deviceDetail.value.deviceNoteName = updatedInfo.deviceNoteName;
    }
  };
  
  // 监听props，当所有值不为undefined时调用相关方法
  watch(
  () => [props.productKey, props.deviceId, props.deviceName],
  async (newValues) => {
    const [productKey, deviceId, deviceName] = newValues;

    // 确保所有值都不为 undefined
    if (productKey !== undefined && deviceId !== undefined && deviceName !== undefined) {
      // 调用获取产品详情和设备详情的函数
      await getProductDetail();
      await getDeviceDetail();
    }
  },
  {
    immediate: true, // 确保在组件挂载时也检查一次
  }
);

  </script>
  
  <style scoped>
  .cursor {
    cursor: pointer;
  }
  </style>
  