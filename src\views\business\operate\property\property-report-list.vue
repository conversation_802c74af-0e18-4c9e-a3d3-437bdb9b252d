<template>
  <a-row>
    <a-col span="4">
      <TreeTimeSelect @update:timeValue="handleTimeValue" />
    </a-col>

    <a-col span="20">
      <div class="main-content">
        <a-form class="smart-query-form">
          <a-row class="smart-query-form-row">
            <a-form-item label="区域名称" class="smart-query-form-item">
              <a-input style="width: 200px" placeholder="请输入区域名称" v-model:value="queryForm.name" />
            </a-form-item>
            <a-form-item class="smart-query-form-item">
              <a-button type="primary" @click="onSearch">
                <template #icon>
                  <SearchOutlined />
                </template>
                查询
              </a-button>
              <a-button class="smart-margin-left10" @click="resetQuery">
                <template #icon>
                  <ReloadOutlined />
                </template>
                重置
              </a-button>
            </a-form-item>
          </a-row>
        </a-form>

        <a-card size="small" :bordered="false" :hoverable="true">
          <template #extra>
            <div class="smart-table-setting-block">
              <TableOperator v-model="columns" :refresh="queryData" />
            </div>
          </template>

          <a-list :grid="{ gutter: 10, xs: 1, sm: 1, md: 2, lg: 2, xl: 2, xxl: 3 }" :data-source="data" style="height: 84vh ;">
            <template #renderItem="{ item, index }">
              <a-list-item>
                <a-card hoverable size="default" style="background: linear-gradient(rgba(88, 158, 255, 0.1), white)" @click="showDetail(item)">
                  <template #title>
                    <a class="detail">{{ item.name }}</a>
                  </template>
                  <template #extra>
                    <div style="width: inherit; position: absolute; right: 30px; top: 15px">No{{ index + 1 }}</div>
                    <a-checkbox style="position: absolute; right: 5px; top: 3px" @click.stop></a-checkbox>
                  </template>
                  <div style="height: 100%; width: 100%; display: flex">
                    <div style="flex: 1; display: flex; flex-direction: column; z-index: 2">
                      <span class="span-multiline-ellipsis">地址：{{ item.address }}</span>
                      <span class="span-multiline-ellipsis">面积大小：{{ item.size}}</span>
                      <span class="span-multiline-ellipsis">联系人：{{ item.contact }}</span>
                      <span class="span-multiline-ellipsis">电话：{{ item.phone }}</span>
                      <span class="span-multiline-ellipsis">生成时间：{{ item.createTime }}</span>
                    </div>
                    <div style="flex: 1; display: flex; justify-content: center; align-items: center; z-index: 1">
                      <img src="/@/assets/images/product/icon2.svg" alt="" style="max-width: 150px; max-height: 150px; object-fit: contain" />
                    </div>
                  </div>
                </a-card>
              </a-list-item>
            </template>
          </a-list>

          <div class="smart-query-table-page">
            <a-pagination
              showSizeChanger
              showQuickJumper
              show-less-items
              :pageSizeOptions="PAGE_SIZE_OPTIONS"
              :defaultPageSize="queryForm.pageSize"
              v-model:current="queryForm.pageNum"
              v-model:pageSize="queryForm.pageSize"
              :total="total"
              @change="queryData"
              @showSizeChange="queryData"
              :show-total="(total) => `共${total}条`"
            />
          </div>
        </a-card>
      </div>
    </a-col>
  </a-row>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { iotRegionApi } from '/@/api/business/iot/region/iot-region-api';
  import TableOperator from '/@/components/support/table-operator/index.vue';
  import { SearchOutlined, ReloadOutlined } from '@ant-design/icons-vue';
  import TreeTimeSelect from '/@/views/business/operate/components/treetime/index.vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  
  const columns = ref([])
  //-----------------------------获取时间----------------------------
  const selectedTime = ref('');
  // 处理时间选择
  function handleTimeValue(value) {
    selectedTime.value = value;
  }

  //-----------------------------详情页跳转---------------------------
  const router = useRouter();
  function showDetail(data) {
    router.push({
      path: '/operate/device/property/info', // 跳转到产品信息页
      query: {
        regionId: data.id, //传递区域ID
        regionName: data.name, //传递区域名称
        timeValue: selectedTime.value, //传递时间
        lng: data.longitude,
        lat: data.latitude
      },
    });
  }

  const initLoading = ref(false);
  const loading = ref(false);
  const PAGE_SIZE_OPTIONS = ref(['10', '20', '30', '40', '50']);
  // ---------------------------- 查询数据表单和方法 ----------------------------

  const queryFormState = {
    name: undefined, //空间名称
    address: undefined, //地址
    latAndLon: undefined, //经纬度
    size: undefined, //面积大小
    contact: undefined, //联系人
    phone: undefined, //联系电话
    longitude: undefined, //经度
    latitude: undefined, //纬度
    createTime: undefined, //生成时间
    createTimeStart: undefined, //开始时间
    createTimeEnd: undefined, //结束时间
    pageNum: 1,
    pageSize: 9,
    sortItemList: [{ isAsc: false, column: 'create_time' }],
  };
  // 查询表单form
  const queryForm = reactive({ ...queryFormState });
  // 表格数据
  const data = ref([]);
  // 总数
  const total = ref(0);

  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.pageSize;
    Object.assign(queryForm, queryFormState);
    queryForm.pageSize = pageSize;
    queryData();
  }

  // 搜索
  function onSearch() {
    queryForm.pageNum = 1;
    queryData();
  }

  // 查询数据
  async function queryData() {
    initLoading.value = true;
    loading.value = true;
    try {
      SmartLoading.show();
      let queryResult = await iotRegionApi.queryPage(queryForm);
      total.value = queryResult.data.total;
      data.value = queryResult.data.list;
    } catch (e) {
      console.error(e);
    } finally {
      initLoading.value = false;
      loading.value = false;
      SmartLoading.hide();
    }
  }

  onMounted(() => {
    queryData();
  });
</script>

<style lang="less" scoped>
  .region-page-container {
    display: flex;
    width: 100%;
    gap: 16px;

    .year-month-tree {
      width: 200px;
      flex-shrink: 0;
    }

    .main-content {
      flex: 1;
    }
  }

  :deep(.ant-card-body) {
    padding: 10px 20px;
  }

  .span-multiline-ellipsis {
    display: -webkit-box; /* Flexbox 模式 */
    -webkit-box-orient: vertical; /* 设置盒子为垂直方向 */
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 1; /* 限制显示2行，多出的内容隐藏 */
    max-width: 100%;
    line-height: 1.4;
    max-height: calc(1.5em * 2); /* 与行高和行数匹配 */
    word-break: break-word;
    font-size: 13px;
    margin-bottom: 10px;
  }
</style>
