<!--
  * 结构体添加参数表单
  *
  * @Author:    文希希
  * @Date:      2025-03-26 17:17:51
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
  <div class="struct-form-container">
    <a-button v-if="!disabled" type="link" size="small" @click="showAddModal">
      <template #icon><plus-outlined /></template>
      增加参数
    </a-button>

    <a-table v-show="safeModelValue.length > 0" :dataSource="safeModelValue" :columns="columns" size="small" :pagination="false" bordered>
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.dataIndex === 'fieldType'">
          <a-tag>{{ record.fieldType }}</a-tag>
        </template>
        <template v-if="column.dataIndex === 'action' && !disabled">
          <a-space>
            <a @click="handleEdit(record, index)">编辑</a>
            <a-popconfirm title="确定将选择数据删除?" ok-text="确定" cancel-text="取消" @confirm="handleDelete(record, index)">
              <a>删除</a>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 添加/编辑参数的对话框 -->
    <a-modal v-model:open="modalVisible" :title="isEdit ? '编辑参数' : '添加参数'" :width="700" @ok="handleModalOk" @cancel="handleModalCancel">
      <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <!-- 参数名称 -->
        <a-form-item label="参数名称" name="name">
          <a-tooltip
            title="支持中文、大小写字母、日文、数字、短划线、下划线、斜杠和小数点，必须以中文、英文或数字开头，不超过 30 个字符"
            placement="topLeft"
          >
            <a-input v-model:value="form.name" placeholder="请输入参数名称" />
          </a-tooltip>
        </a-form-item>

        <!-- 标识符 -->
        <a-form-item label="标识符" name="identifier">
          <a-tooltip title="支持英文大小写、数字、下划线和短划线，必须以英文或数字开头，不超过 30 个字符" placement="topLeft">
            <a-input v-model:value="form.identifier" placeholder="请输入标识符" />
          </a-tooltip>
        </a-form-item>

        <!-- 数据类型 -->
        <a-form-item label="数据类型" name="fieldType">
          <a-select v-model:value="form.fieldType" placeholder="请选择数据类型" @change="handleFieldTypeChange">
            <a-select-option value="int32">int32 : 整数型</a-select-option>
            <a-select-option value="float">float : 单精度浮点型</a-select-option>
            <a-select-option value="double">double : 双精度浮点型</a-select-option>
            <a-select-option value="text">text : 字符串</a-select-option>
            <a-select-option value="enum">enum : 枚举型</a-select-option>
            <a-select-option value="bool">bool : 布尔型</a-select-option>
            <a-select-option value="date">date : 时间型</a-select-option>
          </a-select>
        </a-form-item>

        <!-- 最小值 - 仅当为数值类型时显示 -->
        <a-form-item v-if="showNumberFields" label="最小值" name="specMin">
          <a-input-number v-model:value="form.specMin" style="width: 100%" :min="-2147483648" :max="2147483647" placeholder="请输入最小值" />
        </a-form-item>

        <!-- 最大值 - 仅当为数值类型时显示 -->
        <a-form-item v-if="showNumberFields" label="最大值" name="specMax">
          <a-input-number v-model:value="form.specMax" style="width: 100%" :min="-2147483648" :max="2147483647" placeholder="请输入最大值" />
        </a-form-item>

        <!-- 步长 - 仅当为数值类型时显示 -->
        <a-form-item v-if="showNumberFields" label="步长" name="specStep">
          <a-input-number v-model:value="form.specStep" style="width: 100%" :min="1" :max="2147483647" placeholder="请输入步长" />
        </a-form-item>

        <!-- 单位 - 仅当为数值类型时显示 -->
        <a-form-item v-if="showNumberFields" label="单位" name="specUnit">
          <a-select v-model:value="form.specUnit" placeholder="请选择单位" show-search :allow-clear="true">
            <!-- 这里需要从后端获取单位数据 -->
          </a-select>
        </a-form-item>

        <!-- 字符串长度 - 仅当为字符串类型时显示 -->
        <a-form-item v-if="form.fieldType === 'text'" label="数据长度" name="specLength">
          <a-input-number v-model:value="form.specLength" style="width: 100%" :min="1" :max="10240" placeholder="请输入数据长度，最大值为10240" />
        </a-form-item>

        <!-- 布尔值定义 - 仅当为布尔类型时显示 -->
        <template v-if="form.fieldType === 'bool'">
          <a-form-item label="布尔值 0" name="specBoolFalse">
            <a-input v-model:value="form.specBoolFalse" placeholder="如：关" />
          </a-form-item>
          <a-form-item label="布尔值 1" name="specBoolTrue">
            <a-input v-model:value="form.specBoolTrue" placeholder="如：开" />
          </a-form-item>
        </template>

        <!-- 时间值定义 - 仅当为时间类型时显示 -->
        <a-form-item v-if="form.fieldType === 'date'" label="时间格式" name="specDateFormat">
          <a-input v-model:value="form.specDateFormat" disabled />
        </a-form-item>

        <!-- 枚举数据 - 仅当为枚举类型时显示 -->
        <a-form-item v-if="form.fieldType === 'enum'" label="枚举数据" name="specEnum">
          <enum-form v-model="enumsData" :disable="disabled" ref="enumFormRef" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
  import { ref, reactive, computed, defineProps, defineEmits } from 'vue';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import func from '/@/utils/func';
  import EnumForm from './enum-form.vue';
  import { validateSpecMin, validateSpecMax, validateSpecStep, validateIdentifier, validateName } from '/@/option/iot/functionNew';
  // 定义props和emits
  const props = defineProps({
    modelValue: {
      type: Array,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  });

  //确保modelValue始终是数组
  const safeModelValue = computed(() => {
    return Array.isArray(props.modelValue) ? props.modelValue : [];
  });

  const emit = defineEmits(['update:modelValue']);

  // 表格列定义
  const columns = [
    {
      title: '参数名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '标识符',
      dataIndex: 'identifier',
      key: 'identifier',
    },
    {
      title: '数据类型',
      dataIndex: 'fieldType',
      key: 'fieldType',
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 150,
    },
  ];

  // 表单和对话框相关状态
  const modalVisible = ref(false); // 对话框是否可见
  const isEdit = ref(false); // 是否为编辑
  const editIndex = ref(-1);
  const formRef = ref(null);
  const enumFormRef = ref(null); // 枚举数据表单引用
  const enumsData = ref([]); // 枚举数据

  // 表单数据初始值
  const defaultForm = {
    name: undefined,
    identifier: undefined,
    fieldType: 'int32',
    specMin: undefined,
    specMax: undefined,
    specStep: undefined,
    specUnit: undefined,
    specLength: 64,
    specBoolFalse: '',
    specBoolTrue: '',
    specDateFormat: 'String类型的UTC时间戳（毫秒）',
    specEnum: undefined,
  };

  const form = reactive({ ...defaultForm });
//-------------------------------校验规则----------------------------------------
  const rules = {
    name: [
      { required: true, message: '请输入参数名称' },
      { validator: (rule, value) => {
          const error = validateName(value);
          if (error) return Promise.reject(error);
          return Promise.resolve();
        }
      }
    ],
    identifier: [
      { required: true, message: '请输入标识符' },
      { validator: (rule, value) => {
          const error = validateIdentifier(value);
          if (error) return Promise.reject(error);
          return Promise.resolve();
        }
      }
    ],
    fieldType: [
      { required: true, message: '请选择数据类型' }
    ],
    specMin: [
      { validator: (rule, value) => {
          if (showNumberFields.value) {
            const error = validateSpecMin(form.fieldType, value, form.specMax);
            if (error) return Promise.reject(error);
          }
          return Promise.resolve();
        }
      }
    ],
    specMax: [
      { validator: (rule, value) => {
          if (showNumberFields.value) {
            const error = validateSpecMax(form.fieldType, form.specMin, value);
            if (error) return Promise.reject(error);
          }
          return Promise.resolve();
        }
      }
    ],
    specStep: [
      { validator: (rule, value) => {
          if (showNumberFields.value) {
            const error = validateSpecStep(form.fieldType, form.specMin, form.specMax, value);
            if (error) return Promise.reject(error);
          }
          return Promise.resolve();
        }
      }
    ],
    specLength: [
      { required: true, message: '请输入数据长度' }
    ]
  };
  //计算属性：是否显示数值类型字段
  const showNumberFields = computed(() => {
    return ['int32', 'float', 'double'].includes(form.fieldType);
  });

  // 监听字段类型变化
  const handleFieldTypeChange = (value) => {
    // 根据数据类型重置相关字段
    if (['int32', 'float', 'double'].includes(value)) {
      form.specMin = undefined;
      form.specMax = undefined;
      form.specStep = undefined;
    } else if (value === 'text') {
      form.specLength = 64;
    } else if (value === 'bool') {
      form.specBoolFalse = '';
      form.specBoolTrue = '';
    } else if (value === 'enum') {
      enumsData.value = [];
    }
  };
  // 方法：显示添加对话框
  const showAddModal = () => {
    isEdit.value = false;
    editIndex.value = -1;
    resetForm();
    modalVisible.value = true;
  };

  // 处理编辑
  const handleEdit = (record, index) => {
    isEdit.value = true;
    editIndex.value = index;

    // 复制记录到表单
    Object.keys(form).forEach((key) => {
      form[key] = record[key] !== undefined ? record[key] : form[key];
    });

    // 处理枚举数据
    if (record.fieldType === 'enum' && record.specEnum) {
      enumsData.value = func.keyValueToJsonArray(record.specEnum);
    } else {
      enumsData.value = [];
    }

    modalVisible.value = true;
  };

  // 处理删除
  const handleDelete = (record, index) => {
    const newValue = [...safeModelValue.value];
    newValue.splice(index, 1);
    emit('update:modelValue', newValue);
  };

  // 处理对话框确认
  const handleModalOk = async () => {
    try {
      if (formRef.value) {
        await formRef.value.validate();

        // 处理表单数据
        const formData = { ...form };

        // 处理枚举数据
        if (form.fieldType === 'enum' && enumsData.value.length > 0) {
          formData.specEnum = func.jsonArrayToKeyValue(enumsData.value);
        }

        // 添加唯一ID
        if (!isEdit.value) {
          formData.id = new Date().getTime();
        }

        // 更新数据
        const newValue = [...safeModelValue.value];
        if (isEdit.value && editIndex.value >= 0) {
          newValue[editIndex.value] = formData;
        } else {
          newValue.push(formData);
        }
        emit('update:modelValue', newValue);

        // 关闭对话框
        modalVisible.value = false;
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  //处理对话框取消
  const handleModalCancel = () => {
    modalVisible.value = false;
  };

  //重置表单
  const resetForm = () => {
    Object.keys(form).forEach((key) => {
      form[key] = defaultForm[key];
    });
    enumsData.value = [];
  };

  //对外暴露方法
  defineExpose({
    show: showAddModal,
  });
</script>

<style scoped>
  .struct-form-container {
    margin-bottom: 16px;
  }
</style>
