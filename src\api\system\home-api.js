/*
 * 首页api
 *
 * @Author:    1024创新实验室-主任：卓大
 * @Date:      2022-09-03 21:59:39
 * @Wechat:    zhuda1024
 * @Email:     <EMAIL>
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */
import { getRequest , postRequest} from '/@/lib/axios';

export const homeApi = {
  /**
   * @description: 首页-金额统计（业绩、收款、订单数等） <AUTHOR>
   */
  homeAmountStatistics: () => {
    return getRequest('/home/<USER>/statistics');
  },
  /**
   * @description: 首页-待办信息 <AUTHOR>
   */
  homeWaitHandle: () => {
    return getRequest('home/wait/handle');
  },

  /**
   * 工作台工单的警告卡片
   */
  homeWorkOrder: (params) => {
    return postRequest('/workOrder/queryPage',params);
  },

  /**
   * 查询工单日志
   */
  queryWorkOrderLog: () => {
    return getRequest('/orderLog/queryAll');
 },

};
