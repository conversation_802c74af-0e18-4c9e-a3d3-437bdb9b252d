<!--
  * 工单编辑模态框
  *
  * @Author:    yourName
  * @Date:      2025-04-11 11:45:56
  * @Copyright  bdic
-->
<template>
    <a-modal
        :title="form.id ? '编辑' : '添加'"
        :width="500"
        :open="visibleFlag"
        @cancel="onClose"
        :maskClosable="false"
        :destroyOnClose="true"
    >
      <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }" >
          <a-form-item label="维修单"  name="faultId">
            <a-select 
            v-model:value="form.faultId"
            placeholder="选择设备"
            :options="faultIdOptions"
            :allowClear="true"
            disabled
            />
          </a-form-item>
          <a-form-item label="工单编号"  name="orderNumber" >
            <a-input-number style="width: 100%" v-model:value="form.orderNumber" placeholder="工单编号" disabled/>
          </a-form-item>
          <a-form-item label="维修人"  name="maintainer" >
            <a-select 
            v-model:value="form.maintainerId"
            placeholder="选择维修人"
            :options="maintainerOptions"
            :allowClear="true"
            @change="handleMaintainerChange"
            />
          </a-form-item>
          <a-form-item label="维修情况"  name="faultRemark" >
            <a-textarea style="width: 100%" v-model:value="form.faultRemark" placeholder="工单编号" />
          </a-form-item>
          <a-form-item label="维修情况照片"  name="situationImg">
          <UploadImg 
          :default-file-list="form.situationImg"
          @change="(fileList) => form.situationImg = fileList"
          />
        </a-form-item>
      </a-form>
  
      <template #footer>
        <a-space>
          <a-button @click="onClose">取消</a-button>
          <a-button type="primary" @click="onSubmit">保存</a-button>
        </a-space>
      </template>
    </a-modal>
  </template>
  <script setup>
    import { reactive, ref, nextTick, onMounted } from 'vue';
    import _ from 'lodash';
    import { message } from 'ant-design-vue';
    import { SmartLoading } from '/@/components/framework/smart-loading';
    import { maintainOrderApi } from '/@/api/business/work-order/maintain-order-api';
    import { maintainFaultApi } from '/@/api/business/maintain/maintain-fault/maintain-fault-api';
    import { employeeApi } from '/@/api/system/employee-api';
    import { smartSentry } from '/@/lib/smart-sentry';
    import UploadImg from '/@/components/support/file-upload/index.vue';
    import employeeSelect from '/@/components/system/employee-select/index.vue'
    // ------------------------ 事件 ------------------------
  
    const emits = defineEmits(['reloadList']);
  
    // ------------------------ 显示与隐藏 ------------------------
    // 是否显示
    const visibleFlag = ref(false);
  
    function show(rowData) {
      Object.assign(form, formDefault);
      if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
    }
      // 使用字典时把下面这注释修改成自己的字典字段 有多个字典字段就复制多份同理修改 不然打开表单时不显示字典初始值
      // if (form.status && form.status.length > 0) {
      //   form.status = form.status.map((e) => e.valueCode);
      // }
      visibleFlag.value = true;
      nextTick(() => {
        formRef.value.clearValidate();
      });
    }
  
    function onClose() {
      Object.assign(form, formDefault);
      visibleFlag.value = false;
    }
  
    // ------------------------ 表单 ------------------------
  
    // 组件ref
    const formRef = ref();
  
    const formDefault = {
        faultId: undefined, //维修单id
        orderNumber: undefined, //工单编号
        maintainer: undefined, //维修人
        maintainerId:undefined,//维修人id
        faultRemark: undefined, //维修情况
        situationImg: undefined, //维修情况照片
    };
  
    let form = reactive({ ...formDefault });
  
    const rules = {
        faultId: [{ required: true, message: '维修单id 必填' }],
        orderNumber:[{ required: true, message: '工单编号 必填' }]
    };
    //维修人下拉框
    const maintainerOptions = ref([]);
    //获取维修人下拉框列表
    async function queryMaintainer(){
      try {
        const res = await employeeApi.queryAll({
          disabledFlag: false,
        });
        maintainerOptions.value = res.data.map((e) => {
          return {
            label: e.actualName,
            value:e.employeeId,
          }
        })
      }catch (err) {
      message.error(err);
    }
    }
    function handleMaintainerChange(value) {
      const selectedMaintainer = maintainerOptions.value.find(option => option.value === value);
        if (selectedMaintainer) {
          form.maintainer = selectedMaintainer.label;
        }
      }
   
    // 点击确定，验证表单
    async function onSubmit() {
      try {
        await formRef.value.validateFields();
        save();
      } catch (err) {
        message.error('参数验证错误，请仔细填写表单数据!');
      }
    }
  
    // 新建、编辑API
    async function save() {
      SmartLoading.show();
      try {
          await maintainOrderApi.update(form);
        message.success('操作成功');
        emits('reloadList');
        onClose();
      } catch (err) {
        smartSentry.captureError(err);
      } finally {
        SmartLoading.hide();
      }
    }
  
    defineExpose({
      show,
    });
   onMounted(queryMaintainer);
  </script>
  