/**
 * 照明场景 api 封装
 *
 * @Author:    李帅兵
 * @Date:      2025-03-28 21:36:15
 * @Copyright  中山睿数信息技术有限公司 2025
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const lightSceneApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/lightScene/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/lightScene/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/lightScene/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/lightScene/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
      return postRequest('/lightScene/batchDelete', idList);
  },
  /**
   *通过id获取照明策略详情 <AUTHOR>
   */
   detail: (id) => {
    return getRequest(`/lightScene/detail/${id}`);
},
  /**
   *配置 <AUTHOR>
   */
   set: (idList) => {
    return postRequest('/lightScene/set', idList);
},
  /**
   *分页查询 <AUTHOR>
   */
   strategyQueryPage: (idList) => {
    return postRequest('/lightStrategy/queryPage', idList);
},
};
