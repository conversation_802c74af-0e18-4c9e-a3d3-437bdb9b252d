<template>
  <!-- 备注名称编辑模态框 -->
  <a-modal
    v-model:open="modalVisible"
    title="编辑备注名称"
    @ok="handleSave"
    @cancel="handleCancel"
    :maskClosable="false"
    width="400px"
  >
    <a-form :model="form" :label-col="{ span: 5 }" :wrapper-col="{ span: 19 }">
      <a-form-item label="备注名称" name="deviceNoteName">
        <a-input v-model:value="form.deviceNoteName" placeholder="请输入备注名称" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { message } from 'ant-design-vue';
import { iotDeviceApi } from '/@/api/business/iot/device/iot-device-api.js';
import { SmartLoading } from '/@/components/framework/smart-loading';

const props = defineProps({
  deviceInfo: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update-device']);

// 模态框可见性状态
const modalVisible = ref(false);

// 表单数据
const form = reactive({
  deviceNoteName: ''
});

// 监听props变化，更新表单数据
watch(() => props.deviceInfo, (newVal) => {
  if (newVal) {
    form.deviceNoteName = newVal.deviceNoteName || '';
  }
}, { immediate: true, deep: true });

// 显示模态框方法（供父组件调用）
const showModal = () => {
  form.deviceNoteName = props.deviceInfo.deviceNoteName || '';
  modalVisible.value = true;
};

// 保存更新
const handleSave = async () => {
  try {
    SmartLoading.show();
    const params = {
      deviceId: props.deviceInfo.id,
      deviceNoteName: form.deviceNoteName,
    };
    await iotDeviceApi.updateNoteName(params);
    message.success('备注名称更新成功');
    emit('update-device', { deviceNoteName: form.deviceNoteName });
    modalVisible.value = false;
  } catch (error) {
    message.error('备注名称更新失败');
  } finally {
    SmartLoading.hide();
  }
};

// 取消编辑
const handleCancel = () => {
  modalVisible.value = false;
};

// 暴露方法给父组件
defineExpose({
  showModal
});
</script>
