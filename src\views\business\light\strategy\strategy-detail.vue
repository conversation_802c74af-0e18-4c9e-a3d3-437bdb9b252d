<!--
  * 照明策略-配置
  *
  * @Author:    骆伟林
  * @Date:      2025-03-28 21:36:15
  * @Copyright  中山睿数信息技术有限公司 2025
-->
<template>
  <div id="app">
    <BlocklyComponent
        id="blockly2"
        :options="options"
        ref="foo"/>
        <a-button type="primary" @click="onClose" style="position: absolute; top: 10px; right: 1%;">返回</a-button>
    <a-button type="primary" v-show="route.query.ifDetail==='false'" @click="save" style="position: absolute; top: 10px; right:5%;">保存</a-button>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import BlocklyComponent from '/@/components/light-strategy-blockly/BlocklyComponent.vue';
import '/@/blocks/stocks';
import * as Blockly from 'blockly';
import { javascriptGenerator } from 'blockly/javascript';
import { pythonGenerator } from 'blockly/python';
import { useRoute } from 'vue-router';
import { lightStrategyApi } from '/@/api/business/light/strategy/light-strategy-api';
import { router } from '/@/router';
import { useUserStore } from '/@/store/modules/system/user';

const route = useRoute();
const s=String(route.query.ifDetail).toLowerCase() === 'true' 
console.log(s);

let form ={}
const foo = ref();

const options = {
  media: 'media/',
  grid: {
    spacing: 25,
    length: 3,
    colour: '#ccc',
    snap: true,
  },
  toolbox: `<xml>
          <category name="变量" colour="%{BKY_VARIABLES_HUE}">
    <block type="variables_get">
      <field name="VAR">环境亮度</field>
    </block>
    <block type="variables_get">
      <field name="VAR">车流量</field>
    </block>
    <block type="variables_get">
      <field name="VAR">天气</field>
    </block>
    <block type="variables_set">
      <field name="VAR">路灯亮度</field>
    </block>
    <block type="variables_get">
      <field name="VAR">路灯亮度</field>
    </block>
  </category>
  <category name="逻辑" colour="%{BKY_LOGIC_HUE}">
     <block type="controls_if">
    <value name="CONDITION">
      <shadow type="logic_compare">
        <field name="OP">GT</field>
      </shadow>
    </value>
    <statement name="DO">
      <block type="procedures_defnoreturn"></block>
    </statement>
  </block>
    <block type="logic_compare">
      <field name="OP">GT</field>
    </block>
  </category>
  <category name="操作" colour="%{BKY_TEXTS_HUE}">
    <block type="math_number">
      <field name="NUM">0</field>
    </block>
  </category>
        </xml>`,
  renderer: 'zelos', 
  readOnly:s
};

function onClose() {
   userStore.closePage(route, router, '/light-strategy-list');
   goBack()
}
function goBack() {
  if (window.history.length > 1) {
    router.push('/light-strategy-list');
  } else {
    router.back();
  }
}
let demoXmlString=undefined;
    async function save() {
  const workspace = foo.value.workspace;
  const workspaceXml = Blockly.Xml.workspaceToDom(workspace);
  const workspaceXmlString = Blockly.Xml.domToText(workspaceXml);
  demoXmlString=workspaceXmlString
  const jsonResult = xmlToJson(demoXmlString);
form.env=JSON.stringify(jsonResult, null, 2)
try{
  await lightStrategyApi.update(form);
}
catch(err){
  console.log(err)
}

}



// 加载工作区函数
const loadWorkspaceFromXml = (xmlString) => {
  try {
    const workspace = foo.value.workspace;
    workspace.clear();
    const xmlDom = Blockly.utils.xml.textToDom(xmlString);
    Blockly.Xml.domToWorkspace(xmlDom, workspace);
    console.log("工作区状态恢复成功");
  } catch (error) {
    console.error("恢复工作区失败:", error);
  }
};

function xmlToJson(xmlString) {
  const xmlDom = Blockly.utils.xml.textToDom(xmlString);
  const workspace = new Blockly.Workspace();
  Blockly.Xml.domToWorkspace(xmlDom, workspace);
  const json = Blockly.serialization.workspaces.save(workspace);
  workspace.dispose();
  return json;
}

function jsonToXml(json) {
  const workspace = new Blockly.Workspace();
  Blockly.serialization.workspaces.load(json, workspace);
  const xmlDom = Blockly.Xml.workspaceToDom(workspace);
  return Blockly.Xml.domToText(xmlDom);
}

const userStore = useUserStore();
onMounted(async() => {
  userStore.clearKeepAliveIncludes();
  const res=await lightStrategyApi.queryDetailById(route.query.id)
  form=res.data
if(form.env!==null){
  loadWorkspaceFromXml(jsonToXml(JSON.parse(form.env)))
}

});
</script>

<style>
#app {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}

html,
body {
  margin: 0;
}

#blockly2 {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
}
</style>